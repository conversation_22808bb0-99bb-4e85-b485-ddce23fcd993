<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use App\Modules\acc\models\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PerfDepGroup 顯示部門名稱.
 *
 * @property int $id id
 * @property int $company_id 公司 id
 * @property string $display_name 顯示部門名稱
 * @property string $code 部門代號
 * @property int $sort 排序
 */
class PerfDepGroup extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'perf_dep_groups';

    protected $fillable = [
        'company_id',
        'display_name',
        'code',
        'sort',
        'empno',
        'hidden',
    ];

    public function employee()
    {
        return $this->hasOne(Employee::class, 'payload->employee_number', 'empno');
    }
}
