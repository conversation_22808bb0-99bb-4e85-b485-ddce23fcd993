<?php

namespace App\Modules\PerfBonus\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $empno 員工編號
 * @property string $empna 員工姓名
 * @property string $wlno 職稱
 * @property string $cemail email。
 * @property string $odate 離職日
 * @property string $depno 部門職位
 * @property string $depna 部門職位名稱
 * @property string $schdepno 部門職位(似乎是控權限用的，目前無需使用)
 * @property string $secempno 不知道這幹嘛的
 * @property string $secempna 不知道這幹嘛的
 * @property string $depempno 不知道這幹嘛的
 * @property string $depempna 不知道這幹嘛的
 * @property string $asman 不知道這幹嘛的
 * @property string $idno 身分證
 * @property string $_unkey 不知道這幹嘛的
 *
 */
class TempWapBemp extends Model
{
    use HasFactory;

    protected $table = 'temp_wap_bemps';

    protected $guarded = [];

    public $timestamps = false;
}
