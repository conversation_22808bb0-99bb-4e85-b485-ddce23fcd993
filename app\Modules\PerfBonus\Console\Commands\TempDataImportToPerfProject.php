<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Console\Commands;

use App\Modules\PerfBonus\Models\PerfErpProject;
use App\Modules\PerfBonus\Models\PerfErpProjectEmployee;
use App\Modules\PerfBonus\Models\TempWapBepj;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TempDataImportToPerfProject extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'perf:temp-data-import-to-perf-project';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '將 temp_wap 的這些資料轉化成所需的資料集合存入 DB';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startTimestamp = strtotime('now');
        $this->printAndLog('info', "從 temp_wap_bepjs 匯入到真正要處理的資料表中 ... start timestamp: $startTimestamp");
        TempWapBepj::query()
            ->select([
                'temp_wap_bepjs.epjno',
                'temp_wap_bepjs.epjna',
                'temp_wap_bepjs.accasno',
                'temp_wap_bepjs.epjna',
                'temp_wap_bepjs.sdate',
                'temp_wap_bepjs.odate',
                'temp_wap_bepjs.schdepno',
                'temp_wap_bepjs.closeym',
                'temp_wap_bepjs.totpay',
                'temp_wap_epjemps.empno',
                'temp_wap_epjemps.empna',
                'temp_wap_epjemps.sno',
                'temp_wap_epjemps.depno', // 部門代號
                'temp_wap_epjemps.depna', // 部門名稱
            ])
            ->join('temp_wap_epjemps', 'temp_wap_epjemps.epjno', '=', 'temp_wap_bepjs.epjno')
            // ->where('temp_wap_bepjs.accasno', '<>', '')
            // ->where('temp_wap_bepjs.closeym', '<>', '')
            ->chunk(
                count: 500,
                callback: function (\Illuminate\Database\Eloquent\Collection $data) {
                    $epjnoCollection = $data->groupBy('epjno');

                    $epjnoCollection->each(function ($val, $epjno) {
                        $epjno = (string)$epjno;

                        $this->printAndLog('info', "處理案件: {$epjno} ...");

                        $companyId = $this->conditionProjectIdGetComponyId((string)$epjno);

                        if ($companyId === 0) {
                            $this->printAndLog('error', "找不到對應的公司別: {$epjno} ... companyId: {$companyId}");
                            return ;
                        }
                        // 1, 3 有開通，其餘 skip
                        if (in_array($companyId, [0, 2, 4, 5])) {
                            $this->printAndLog('error', "(略過)未開通公司別功能: {$epjno} ... companyId: {$companyId}");
                            return ;
                        }


                        if ($companyId == 1) {
                            preg_match('/^(?<year>\d{4})/', $epjno, $matches);

                            if (empty($matches['year'])) {
                                $this->printAndLog('error', "(略過)找不到對應的年份: {$epjno} ... companyId: {$companyId}");
                                return ;
                            }
                            if ($matches['year'] < 2020) {
                                $this->printAndLog('error', "(略過)年份小於 2020: {$epjno} ... companyId: {$companyId}");
                                return ;
                            }
                        }

                        if ($companyId == 3) {
                            preg_match('/^Y(?<year>\d{4})/', $epjno, $matches);

                            if (empty($matches['year'])) {
                                $this->printAndLog('error', "(略過)找不到對應的年份: {$epjno} ... companyId: {$companyId}");
                                return ;
                            }
                            if ($matches['year'] < 2020) {
                                $this->printAndLog('error', "(略過)年份小於 2020: {$epjno} ... companyId: {$companyId}");
                                return ;
                            }
                        }

                        $inDbProjectId = PerfErpProject::updateOrCreate([
                            'epjno' => $epjno
                        ], [
                            'company_id' => $companyId,
                            'project_name' => $val->first()->epjna,
                            'accasno' => $val->first()->accasno,
                            'schdepno' => $val->first()->schdepno,
                            'closeym' => $val->first()->closeym,
                            'total_pay' => $val->first()->totpay ?? 0,
                            's_date' => $this->formatStartDate($val->first()->sdate),
                            'e_date' => $this->formatEndDate($val->first()->odate),
                        ])->id;

                        $val->each(function ($record) use ($inDbProjectId, $epjno) {
                            if ($record->sno === 'PM') {
                                PerfErpProject::find($inDbProjectId)->update([
                                    'pm_empno' => $record->empno,
                                    'pm_code' => $record->depno,
                                ]);
                            }
                            if ($record->sno === 'PC') {
                                PerfErpProject::find($inDbProjectId)->update([
                                    'pc_empno' => $record->empno,
                                    'pc_code' => $record->depno,
                                ]);
                            }
                            PerfErpProjectEmployee::updateOrCreate([
                                'epjno' => $epjno,
                                'empno' => $record->empno,
                            ], [
                                'empna' => $record->empna,
                                'perf_dep_code' => $record->depno,
                                'sno' => $record->sno,
                            ]);

                        });
                    });
                }
            );
            $this->printAndLog('debug', "Finished ! 花費時間: " . (strtotime('now') - $startTimestamp) . " 秒");
    }

    /**
     * 從專案代號的第一個字判斷出是哪間公司的資訊
     * @param string $epjno
     * @return int
     */
    protected function conditionProjectIdGetComponyId($epjno): int
    {
        return match (substr($epjno, 0, 1)) {
            'Y' => 3,
            'Q' => 4,
            'F' => 2,
            '2' => 1,
            default => 0,
        };
    }

    protected function formatStartDate($sDate): ?string
    {
        return match ($sDate) {
            '', null => null,
            default => date('Y-m-d', strtotime($sDate))
        };
    }

    protected function formatEndDate($eDate): ?string
    {
        return match ($eDate) {
            '', null, '無完工日' => null,
            default => date('Y-m-d', strtotime($eDate))
        };
    }

    protected function printAndLog(string $type = 'info', string $message, ...$args)
    {
        // $this->$type($message, ...$args);
        Log::channel('perf-bonus')->$type($message, ...$args);
    }
}
