<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use App\Modules\acc\models\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PerfBonusSetting
 *
 * @property int $id
 * @property string $key
 * @property string $value
 * @property int $company_id
 * @property string $description
 * @property int $update_by
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property \App\Models\Company $company
 */
class PerfBonusSetting extends Model
{
    use HasFactory;

    protected $table = 'perf_bonus_settings';

    protected $fillable = [
        'key',
        'value',
        'company_id',
        'description',
        'update_by',
    ];

    /**
     * Get the company that owns the PerfBonusSetting.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
