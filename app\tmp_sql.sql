
--report
select 
    sl.id, 
    sl.pkey, 
    sl."startYm" as 開始月份, 
    sl."endYm" as 結束月份, 
    sl.pbatch<PERSON> as 批次, 
    sl.epjacc as 會計代號, 
    sl.epjno as 專案編號, 
    pr.epjna as 專案簡稱,
    sl.venno as 廠商代號, 
    pr.venna as 廠商簡稱,
    pr.copname as 廠商名稱,
    "sendStatus" as 發送狀態,
    sl.payload->>'faxNo' as 傳真號碼, 
    sl.payload->>'subject' as 傳送單號, 
    e.payload->>'name' as 建立者,
    sl.created_at as 建立時間, 
    sl.updated_at as 更新時間
from send_logs sl 
left join employees e on e.id = sl.created_by
left join (
    select 
        pkey,
        payload->>'pbatchno' as pbatchno,
        payload->>'symno' as symno,
        list->>'venno' as venno,
        max(payload->>'epjna') as epjna,
        max(list->>'venna') as venna,
        max(list->>'copname') as copname
    from pj_reports
    group by pkey, payload->>'pbatchno', payload->>'symno', list->>'venno'
) pr on pr.venno = sl.venno 
    and pr.pkey = sl.pkey 
    and pr.pbatchno = sl.pbatchno 
    and pr.symno = sl."startYm"
where sl."sendStatus" <> 'send-email' 
    and sl.created_at < '2025-06-01 00:08:54.000'::timestamp 
order by sl.created_at asc;




--pay
select 
    sl.id, 
    sl.pkey, 
    sl."startYm" as 開始月份, 
    sl."endYm" as 結束月份, 
    sl.pbatchno as 批次, 
    sl.epjacc as 會計代號, 
    sl.epjno as 專案編號, 
    p.epjna as 專案簡稱,
    sl.venno as 廠商代號, 
    pd.venna as 廠商簡稱,
    pd.copname as 廠商名稱,
    "sendStatus" as 發送狀態,
    sl.payload->>'faxNo' as 傳真號碼, 
    sl.payload->>'subject' as 傳送單號, 
    e.payload->>'name' as 建立者,
    sl.created_at as 建立時間, 
    sl.updated_at as 更新時間
from send_logs sl 
left join employees e on e.id = sl.created_by
left join (
    select 
        pkey,
        pbatchno,
        symno,
        payload->>'venno' as venno,
        max(payload->>'venna') as venna,
        max(payload->>'copname') as copname
    from pj_pay_details
    group by pkey, pbatchno, symno, payload->>'venno'
) pd on pd.venno = sl.venno 
    and pd.pkey = sl.pkey 
    and pd.pbatchno = sl.pbatchno 
    and pd.symno = sl."startYm"
left join (
    select 
        pkey,
        max(payload->>'epjna') as epjna
    from pj_pays
    group by pkey
) p on p.pkey = sl.pkey
where sl."sendStatus" <> 'send-email' 
    and sl.created_at < '2025-06-01 00:08:54.000'::timestamp 
order by sl.created_at asc;