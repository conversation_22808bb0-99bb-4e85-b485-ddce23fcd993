<?php

declare(strict_types=1);

namespace App\Modules\acc\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\acc\models\Employee;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Session;
class NotificationController extends Controller
{
    public function getNotifications(Request $request)
    {
        $employeeId = Session::get('employee_id');
        $employee = Employee::query()->findOrFail($employeeId);
        /** @var \Illuminate\Pagination\LengthAwarePaginator $notifications */
        $notifications = $employee->notifications()->paginate(15);
        $notifications->transform(fn ($it) => $it->only('id', 'data', 'read_at', 'created_at'));

        return response()->json([
            'data' => $notifications->items(),
        ]);
    }

    public function readNotification(string $notificationId)
    {
        $notification = DatabaseNotification::query()->find($notificationId);

        if (! empty($notification)) {
            $notification->markAsRead();
        }
        return response()->noContent();
    }

    public function readAllNotification(Request $request)
    {
        $employeeId = Session::get('employee_id');
        $employee = Employee::query()->find($employeeId);

        if ($employee) {
            $employee->unreadNotifications()->update(['read_at' => Carbon::now()]);
        }

        return response()->noContent();
    }

    public function deleteAllNotification(Request $request)
    {
        $employeeId = Session::get('employee_id');
        $employee = Employee::query()->find($employeeId);

        if ($employee) {
            DatabaseNotification::query()
                ->where('notifiable_type', Employee::class)
                ->where('notifiable_id', $employee->id)
                ->delete();
        }
        return response()->noContent();
    }

    public function deleteNotification(string $notificationId, Request $request)
    {
        $employeeId = Session::get('employee_id');
        $employee = Employee::query()->find($employeeId);

        if ($employee) {
            DatabaseNotification::query()
                ->where('id', $notificationId)
                ->where('notifiable_type', Employee::class)
                ->where('notifiable_id', $employee->id)
                ->delete();
        }
        return response()->noContent();
    }
}
