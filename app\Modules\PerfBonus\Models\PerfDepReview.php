<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use App\Modules\acc\models\Employee;
use App\Notifications\PerfProjectWhenCompleted;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;

/**
 * PerfDepReview.
 *
 * @property int $id
 * @property string $code 部門代碼
 * @property int $prj_id 關聯 perf_erp_projects.id
 * @property float $amount 實際績效獎金的金額
 * @property float $suggestion_amount 推薦的績效獎金額，通常為實際金額的七成
 * @property string $status 是否已完成 pending, finished, skip
 * @property string $first_empno 第一階段評核人員
 * @property string $second_empno 第二階段評核人員
 * @property string $common_type 評核種類是 工程 還是 專案 分別
 */
class PerfDepReview extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 設計人員
     *  COMMON_TYPE = 'P' // project.
     */
    public const COMMON_TYPE_PM = 'P';

    /**
     * 實施設計人員
     * 工程人員
     *  COMMON_TYPE_CPM = 'E' // engineer.
     */
    public const COMMON_TYPE_CPM = 'E';

    // 尚未評核
    public const STATUS_PENDING = 'pending';

    // 完成評核
    public const STATUS_FINISHED = 'finished';

    // 依賴評核 -> pm, cpm 尚未評核完畢。
    public const STATUS_SKIP = 'skip';

    protected $table = 'perf_dep_reviews';

    protected $fillable = [
        'code', // 部門代碼
        'prj_id', // 關聯 perf_erp_projects.id
        'amount', // 實際金額
        'suggestion_amount', // 推薦金額
        'status', // pending, finished, skip
        'first_empno', // 第一階段評核人員
        'first_finished_at', // 第一階段評核人員
        'second_empno', // 第二階段評核人員
        'second_finished_at', // 第二階段評核人員
        'common_type', // 三轉二審核: 設計人員(PM) 實施設計人員(CPM) 工程人員(CPM)
        'closeym',
    ];

    public function erpProject()
    {
        return $this->belongsTo(PerfErpProject::class, 'prj_id', 'id');
    }

    public function firstEmployee()
    {
        return $this->hasMany(Employee::class, 'payload->employee_number', 'first_empno');
    }

    public function secondEmployee()
    {
        return $this->hasMany(Employee::class, 'payload->employee_number', 'second_empno');
    }

    public function logs()
    {
        return $this->hasMany(PerfLog::class, 'review_id', 'id');
    }

    // 這邊是要將最終結果更新回去 perf_erp_projects
    public static function boot()
    {
        parent::boot();

        self::updated(function (PerfDepReview $model) {
            $projectId = $model->prj_id;

            $pendingCount = $model->newQuery()
                ->where(function ($query) {
                    $query
                        ->whereNotNull('first_empno')
                        ->whereNull('first_finished_at');
                    $query
                        ->orWhereNotNull('second_empno')
                        ->whereNull('second_finished_at');
                })
                ->where('prj_id', $projectId)
                ->where('status', self::STATUS_PENDING)
                ->count();

            if ($pendingCount === 0) {
                $model->erpProject->update(['status' => PerfErpProject::STATUS_COMPLETED]);

                // 這邊要小鈴鐺通知原始提出這個績效獎金的人員。
                $sponsorEmployeeId = $model->erpProject->sponsor_employee_id;

                Log::channel('perf-bonus')->info('專案績效獎金評核完成', [
                    'project_id' => $projectId,
                    'sponsor_employee_id' => $model->erpProject->sponsor_employee_id,
                ]);

                $sponsorEmployee = Employee::find($sponsorEmployeeId);
                $sponsorEmployee->notify(new PerfProjectWhenCompleted(
                    projectId: $projectId,
                    epjno: $model->erpProject->epjno,
                ));
            }
        });
    }
}
