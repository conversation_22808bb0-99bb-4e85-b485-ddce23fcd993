<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('app:test')->runInBackground()->withoutOverlapping()->everyMinute();
        // $schedule->command('app:test2')->runInBackground()->everyMinute();

        // 避免資料過多，時間太長 訂在半夜12點
        // $schedule->command('auto:cmd')->dailyAt('16:00');

        // 清除暫存的檔案
        $schedule->command('app:delete-tmp-dir')->monthlyOn(1,'16:00');

        // 回寫fax結果到sendlog
        // 中華電信: 建議查完一輪後，間隔五分鐘再針對無結果的傳真進行下一輪查詢 
        $schedule->command('app:update-fax-result')->runInBackground()->withoutOverlapping()->everyFiveMinutes();

        // 績效獎金: ERP 資料進行同步
        // $schedule->command('perf:get-erp-data-to-temp-data')->dailyAt('01:00');

        // 績效獎金: 將 ERP 資料同步到專案資料庫中
        $schedule->command('perf:temp-data-import-to-perf-project')->dailyAt('01:15');

        // 績效獎金: 發信件排程
        // $schedule->command('perf:perf-un-deal-to-email')->dailyAt('09:15');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');
        $this->load(__DIR__.'/../Modules/PerfBonus/Console/Commands');

        require base_path('routes/console.php');
    }
}
