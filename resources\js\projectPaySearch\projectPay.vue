<template>
  <!-- project -->
  <div>
    <div class="p-6 flex flex-wrap w-full bg-white rounded-xl shadow">
      <label class="my-auto"> 起始年月:&ensp; </label>
      <Calendar
        v-model="year_start"
        view="month"
        dateFormat="yy/mm"
        placeholder="請選擇年月"
      />&emsp;

      <label class="my-auto"> 結束年月:&ensp; </label>
      <Calendar
        v-model="year_end"
        view="month"
        dateFormat="yy/mm"
        placeholder="請選擇年月"
      />&emsp;

      <label class="my-auto"> 批次:&ensp; </label>
      <MultiSelect
        v-model="select_fetch"
        :filter="true"
        :options="$parent.pbatchno"
        optionValue="code"
        optionLabel="name"
        dataKey="code"
        placeholder="請選擇"
        :maxSelectedLabels="3"
        class="w-25 md:w-20rem"
      />
      &emsp;
      <label class="my-auto"> 會計代號(選填):&ensp; </label>
      <InputText type="text" v-model="epjacc" placeholder="請輸入" @input="epjacc = $event.target.value.toUpperCase()" />&emsp;

      <Checkbox class="my-auto" v-model="fetch_diff" :binary="true" />&ensp;
      <label class="my-auto">撈取差額</label>&emsp;
      <Button
        type="button"
        label="重置"
        icon="pi pi-search"
        :loading="loading"
        @click="clear()"
        text
        raised
      />
      &emsp;
      <Button
        type="button"
        label="查詢"
        :loading="loading"
        :disabled="year_start == null || year_end == null"
        @click="fetchPjPay()"
      />
    </div>
    <div
      class="p-6 my-6 bg-white rounded-xl shadow whitespace-nowrap overflow-x-auto flex"
      :class="
        projects.length == 0 || projects.data?.length == 0
          ? 'justify-center items-center'
          : 'flex-col'
      "
      :style="
        projects.length == 0 || projects.data?.length == 0
          ? 'height : calc(100vh - 276px)'
          : ''
      "
    >
      <div
        class="text-center"
        v-if="projects.length == 0 || projects.data?.length == 0"
      >
        <ProgressSpinner v-if="loading" />
        <img
          v-else-if="projects.length == 0"
          src="../../images/page_state/search.png"
          alt=""
        />
        <img v-else src="../../images/page_state/search_empty.png" alt="" />
        <p v-if="!loading && projects.length == 0">輸入您要查詢的資料</p>
        <p v-else-if="!loading && projects.data?.length == 0">
          無任何查詢結果，請再次查詢
        </p>
      </div>
      <div v-else>
        <DataTable
          v-model:selection="selectedProjects"
          :value="projects.data"
          dataKey="hash_id"
          :metaKeySelection="false"
          :scrollable="true"
          scrollHeight="600px"
          responsiveLayout="scroll"
          stripedRows
          tableStyle="flex : 1 1 0%"
        >
          <!-- @row-select="test()" -->
          <!-- @rowSelect="onRowSelect" -->
          <template #header>
            <div
              class="flex flex-wrap align-items-center justify-between gap-2"
            >
              <p class="text-xl text-900 font-bold">專案付款單</p>
              <div>
                <label class="my-auto"> 公司抬頭:&ensp; </label>
                <Dropdown
                  class="w-40"
                  optionLabel="name"
                  optionValue="id"
                  :options="[
                    { id: 'G', name: '匯僑室內裝修設計股份有限公司' },
                    { id: 'F', name: '鴻僑傢俱有限公司' },
                  ]"
                  v-model="company_title"
                />
                &ensp;
                <Button
                  label="匯出"
                  @click="exportLists()"
                  :loading="loading"
                  :disabled="selectedProjects.length == 0"
                />
              </div>
            </div>
          </template>
          <Column
            frozen
            selectionMode="multiple"
            headerStyle="width: 3rem"
            :checked="checkCount()"
          ></Column>
          <Column frozen field="epjacc" header="會計代號"></Column>
          <Column frozen field="epjno" header="專案代號"></Column>
          <Column frozen field="epjna" header="專案簡稱"></Column>
          <Column field="sdate" header="開工日期"></Column>
          <Column field="odate" header="完工日期"></Column>
          <Column field="venno" header="廠商代號"></Column>
          <Column field="payrmk" header="付款摘要"></Column>
          <Column
            field="vencamt"
            header="發包總價"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'vencamt')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.vencamt) }}
            </template>
          </Column>
          <Column
            field="tvenpay"
            header="發包已付"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'tvenpay')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.tvenpay) }}
            </template>
          </Column>
          <Column
            field="balance"
            header="發包結餘"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'balance')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.balance) }}
            </template>
          </Column>
          <Column
            field="npay"
            header="本期請款"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'npay')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.npay) }}
            </template>
          </Column>
          <Column
            field="ppay"
            header="本期預付"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'ppay')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.ppay) }}
            </template>
          </Column>
          <Column
            field="hpay"
            header="本期保留"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'hpay')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.hpay) }}
            </template>
          </Column>
          <Column
            field="dpay"
            header="本期折讓"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'dpay')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.dpay) }}
            </template>
          </Column>
          <Column
            field="venpay"
            header="本期實付"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'venpay')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.venpay) }}
            </template>
          </Column>
          <Column
            field="tax"
            header="稅金"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'tax')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.tax) }}
            </template>
          </Column>
          <Column field="edate" header="工程主管審核日期"></Column>
          <Column
            field="eamount"
            header="工程主管核准金額"
            style="text-align: right"
            :footer="this.$parent.computedDetailTotal(projects.data, 'eamount')"
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.eamount) }}
            </template>
          </Column>
          <Column field="ermk" header="工程主管備註"></Column>
          <Column field="accdate" header="財務主管審核日期"></Column>
          <Column
            field="accamount"
            header="財務主管核准金額"
            style="text-align: right"
            :footer="
              this.$parent.computedDetailTotal(projects.data, 'accamount')
            "
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.accamount) }}
            </template>
          </Column>
          <Column field="accrmk" header="財務主管備註"></Column>
          <Column field="cpmdate" header="CPM審核日期"></Column>
          <Column
            field="cpmamount"
            header="CPM核准金額"
            style="text-align: right"
            :footer="
              this.$parent.computedDetailTotal(projects.data, 'cpmamount')
            "
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.cpmamount) }}
            </template>
          </Column>
          <Column field="cpmrmk" header="CPM備註"></Column>
          <Column field="pddate" header="PD審核日期"></Column>
          <Column
            field="pdamount"
            header="PD核准金額"
            style="text-align: right"
            :footer="
              this.$parent.computedDetailTotal(projects.data, 'pdamount')
            "
          >
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.pdamount) }}
            </template>
          </Column>
          <Column field="pdrmk" header="PD備註"></Column>
          <ProgressSpinner
            v-if="loading"
            style="position: absolute; top: 50%; left: 50%; z-index: 10"
          />
        </DataTable>
        <Paginator
          v-if="!visibleBottom"
          :data="projects"
          @page="(val) => fetchPjPay(val.page, val.per)"
        />
      </div>
    </div>
    <!-- <Paginator :data="projects" @page="fetchPjPay" /> -->
  </div>
  <Sidebar
    v-model:visible="visibleBottom"
    :dismissable="false"
    position="bottom"
    :baseZIndex="1000"
    :modal="false"
    :showCloseIcon="false"
    class="p-sidebar-xs"
  >
    <div class="flex justify-end w-full">
      <p class="text-sm my-auto">已選取{{ selectedProjects.length }}個</p>
      <p
        @click="
          selectedProjects = [];
          visibleBottom = false;
        "
        class="mx-12 text-sm text-blue-600 underline my-auto cursor-pointer"
      >
        取消選取
      </p>
    </div>
  </Sidebar>
</template>
<script >
import Paginator from "../common/Paginator.vue";

export default {
  components: {
    Paginator,
  },
  data: function () {
    return {
      loading: false,
      visibleBottom: false,
      year_start: this.$parent.newDate,
      year_end: this.$parent.newDate,
      select_fetch: ["1"],
      epjacc: "",
      company_title: "G",
      fetch_diff: false,
      page: 1,
      per: 50,
      projects: [],
      selectedProjects: [],
      details: [],
      selectedDetails: [],
      pjInfo: null,
      apiURL: "/api/acc/report/pjpay",
    };
  },
  mounted() {},
  methods: {
    fetchPjPay(page, per) {
      this.loading = true;
      if (page) this.page = page;
      if (per) this.per = per;
      axios
        .get(this.apiURL + "/list", {
          params: {
            startYm: this.year_start,
            endYm: this.year_end,
            pbatch: this.select_fetch,
            fetchDiff: this.fetch_diff,
            epjacc: this.epjacc,
            page: this.page,
            per: this.per,
          },
        })
        .then((response) => {
          this.projects = response.data ?? [];
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    exportLists() {
      axios({
        url: this.apiURL + "/export",
        method: "POST",
        // headers: {
        //   "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        // },
        data: {
          payList: this.selectedProjects,
          companyTitle: this.company_title,
        },
        responseType: "blob",
      })
        .then((response) => {
          if (response.headers["content-type"].includes("application/zip")) {
            var blob = new Blob([response.data], {
              type: "application/zip",
            });
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
              window.navigator.msSaveOrOpenBlob(blob);
              return;
            }
            var filename = decodeURIComponent(
              response.headers["content-disposition"].split("filename=")[1]
            );
            const data = window.URL.createObjectURL(blob);
            var link = document.createElement("a");
            link.href = data;
            link.download = filename; // response.name + '.pdf'
            link.click();
            // link.remove();
            setTimeout(function () {
              window.URL.revokeObjectURL(data);
            }, 100);
          } else if (
            response.headers["content-type"].includes(
              "application/octet-stream"
            )
          ) {
            var blob = new Blob([response.data], {
              type: "application/vnd.ms-excel;charset=utf-8",
            });

            var filename = decodeURIComponent(
              response.headers["content-disposition"].split(
                "filename*=utf-8''"
              )[1]
            );
            var url = window.URL.createObjectURL(blob);
            var aLink = document.createElement("a");
            aLink.style.display = "none";
            aLink.href = url;
            aLink.setAttribute("download", filename);
            document.body.appendChild(aLink);
            aLink.click();
            setTimeout(function () {
              window.URL.revokeObjectURL(data);
            }, 100);
          } else {
            window.print();
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    clear() {
      this.year_start = this.$parent.newDate;
      this.year_end = this.$parent.newDate;
      this.select_fetch = ["1"];
      this.force_fetch = false;
    },
    checkCount() {
      if (this.selectedProjects.length > 0) {
        this.visibleBottom = true;
      }
      else {
        this.visibleBottom = false;
      }
    },
  },
};
</script>
