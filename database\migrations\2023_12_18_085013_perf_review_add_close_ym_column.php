<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('perf_dep_reviews', function (Blueprint $table) {
            $table->string('closeym')->nullable()->comment('結算年月');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('perf_dep_reviews', function (Blueprint $table) {
            $table->dropColumn('closeym');
        });
    }
};
