<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


// id	    bint	id	
// pkey	    varchar	會計代號_專案代號	
// epjacc	varchar	會計代號	
// epjno	varchar	專案代號	
// payload	json		
//      epjna	    varchar	 專案簡稱
//      pdno	    varchar	 PD員工編號
//      pmno	    varchar	 PM員工編號
//      pcno	    varchar	 CPM員工編號
//      pleper	    int	     請款%
//      venpayper	int	     付款%
//      difamt	    int	     收付差額
//      sdate	    varchar	 開工日期
//      odate	    varchar	 完工日期
//      atypena	    varchar	 專案現況
//      qupamtc	    int	     合約報價
//      qupamtad	int	     追加減報價
//      qupamt	    int	     總報價款
//      tqurece	    int	     已收款
//      tqurecn	    int	     已請未收
//      budamt	    int	     預算成本
//      vencamt	    int	     總發包款
//      venper	    int	     發包%
//      tvpay	    int	     發包已付
//      nacpay	    int	     請款未付
//      venpay	    int	     本期請款
class PjPay extends Model
{
    use HasFactory;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;

    // protected $fillable = ['id', 'key', 'symno', 'pbatchno', 'epjacc', 'epjno', 'payload'];
    protected $casts = ['payload' => 'collection'];


    public function pd()
    {
        return $this->hasOne(Employee::class, 'payload->employee_number', 'payload->pdno')->where('company_id', 1);
    }
    public function pm()
    {
        return $this->hasOne(Employee::class,  'payload->employee_number', 'payload->pmno')->where('company_id', 1);
    }
    public function pc()
    {
        return $this->hasOne(Employee::class,  'payload->employee_number', 'payload->pcno')->where('company_id', 1);
    }

    public function detail()
    {
        return $this->hasMany(PjPayDetail::class, 'pkey', 'pkey');
    }

    public function signlog()
    {
        return $this->hasMany('App\Modules\acc\models\SignLog', 'pkey', 'pkey');
    }
}
