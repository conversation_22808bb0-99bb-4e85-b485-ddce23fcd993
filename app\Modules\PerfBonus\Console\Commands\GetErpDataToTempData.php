<?php

namespace App\Modules\PerfBonus\Console\Commands;

use App\Modules\PerfBonus\Models\TempWapBemp;
use App\Modules\PerfBonus\Models\TempWapBepj;
use App\Modules\PerfBonus\Models\TempWapEpjemp;
use App\Traits\PerfLogTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetErpDataToTempData extends Command
{
    use PerfLogTrait;
    const CHUNK_COUNT = 700;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'perf:get-erp-data-to-temp-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '將 ERP 的資料取出來存入 temp_wap 資料庫中';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // initial wap_bepjs table
        static::perfLog('info', '匯入 ERP 資訊到 temp 表');

        TempWapBepj::query()->truncate();
        TempWapBemp::query()->truncate();
        TempWapEpjemp::query()->truncate();

        // 取得專案資料
        $this->syncToLocalTempWapBepj();

        // 取得員工資訊
        $this->syncToLocalTempWapBemp();

        // 取得專案對應到的員工資料，這邊專案需要資料對應的操作部門有誰
        $this->syncToLocalTempWapEpjemp();

        static::perfLog('info', '完成匯入(ERP 資訊到 temp 表)');
    }

    protected function syncToLocalTempWapBepj(): void
    {
        static::perfLog('info', '同步專案資料到 temp_wap_bepj ...');
        $start = strtotime('now');
        DB::connection('sqlsrv')
            ->table('wap_bepj')
            ->selectRaw(
                "RTRIM(epjno) as epjno, RTRIM(epjna) as epjna, RTRIM(accasno) as accasno, RTRIM(epjname) as epjname, RTRIM(sdate) as sdate, RTRIM(odate) as odate, RTRIM(cusno) as cusno, RTRIM(cusname) as cusname, RTRIM(cusman) as cusman, RTRIM(cmanwtna) as cmanwtna, RTRIM(cmantel) as cmantel, RTRIM(cmanmail) as cmanmail, RTRIM(wkcla) as wkcla, RTRIM(wkclana) as wkclana, RTRIM(schdepno) as schdepno, RTRIM(pjmno) as pjmno, RTRIM(pjmna) as pjmna, RTRIM(atype) as atype, RTRIM(atypena) as atypena, RTRIM(totpay) as totpay, RTRIM(epjbrno) as epjbrno, RTRIM(closeym) as closeym"
            )
            ->chunkById(
                count: self::CHUNK_COUNT,
                callback: fn ($it) => TempWapBepj::query()->insert(
                    json_decode(json_encode($it), true)
                ),
                column: 'epjno',
        );

        static::perfLog('info', '完成 同步專案資料到 temp_wap_bepj ... 耗時: ' . (strtotime('now') - $start) . ' 秒');
    }

    protected function syncToLocalTempWapBemp(): void
    {
        static::perfLog('info', '同步員工資料到 temp_wap_bemp ...');
        $start = strtotime('now');
        DB::connection('sqlsrv')
            ->table('wap_bemp')

            ->selectRaw(
                "RTRIM(empno) as empno, RTRIM(empna) as empna, RTRIM(wlno) as wlno, RTRIM(cemail) as cemail, RTRIM(odate) as odate, RTRIM(depno) as depno, RTRIM(depna) as depna, RTRIM(schdepno) as schdepno, RTRIM(secempno) as secempno, RTRIM(secempna) as secempna, RTRIM(depempno) as depempno, RTRIM(depempna) as depempna, RTRIM(asman) as asman, RTRIM(idno) as idno, RTRIM(_unkey) as _unkey"
            )
            ->chunkById(
                count: self::CHUNK_COUNT,
                callback: fn ($it) => TempWapBemp::query()->insert(
                    json_decode(json_encode($it), true)
                ),
                column: 'empno',
            );
        static::perfLog('info', '完成 同步專案資料到 temp_wap_bemp ... 耗時: ' . (strtotime('now') - $start) . ' 秒');
        $this->info(__METHOD__ . ' finished !');
    }

    protected function syncToLocalTempWapEpjemp(): void
    {
        static::perfLog('info', '同步專案對應到的員工資料到 temp_wap_epjemp ...');
        $start = strtotime('now');


        $totalResult = DB::connection('sqlsrv')->table('wap_epjemp')->count();
        $totalCircle = intval($totalResult / self::CHUNK_COUNT);

        // 除不盡的話，就再多跑一圈
        if ($totalResult % self::CHUNK_COUNT !== 0) {
            $totalCircle += 1;
        }

        $resultCursor = DB::connection('sqlsrv')->table('wap_epjemp')->selectRaw(
            "RTRIM(epjno) as epjno, RTRIM(epjna) as epjna, RTRIM(empno) as empno, RTRIM(empna) as empna, RTRIM(sno) as sno, RTRIM(schdepno) as schdepno, RTRIM(schgrpno) as schgrpno, RTRIM(pjmno) as pjmno, RTRIM(pjmna) as pjmna, RTRIM(depno) as depno, RTRIM(depna) as depna"
        )->cursor();

        $tempData = [];
        $resultCursor->each(function ($it, $idx) use (&$tempData, $totalResult) {
            if (count($tempData) === 10) {
                TempWapEpjemp::query()->insert($tempData);
                $tempData = [];
            }

            $tempData[] = json_decode(json_encode($it), true);
            if ($totalResult ===$idx + 1) {
                TempWapEpjemp::query()->insert($tempData);
            }
        });
        static::perfLog('info', '完成 同步專案資料到 temp_wap_epjemp ... 耗時: ' . (strtotime('now') - $start) . ' 秒');
    }
}
