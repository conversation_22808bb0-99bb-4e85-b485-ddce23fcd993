<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PerfProjectWhenCompleted extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        protected string|int $projectId,
        protected string $epjno,
        protected array $metaData = [],
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $content = sprintf("您提出的績效獎金 %s 已核定完成，立即前往查看", $this->epjno);

        return [
            'content' => $content,
            'metaData' => $this->metaData,
            'redirectTo' => route('perf::front::history', ['id' => $this->epjno]),
        ];
    }
}
