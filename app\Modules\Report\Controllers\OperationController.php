<?php

namespace App\Modules\Report\Controllers;
use App\Http\Controllers\Controller;
use App\Modules\Report\Services\OperationService;
use Illuminate\Http\Request;

class OperationController extends Controller{

    public function __construct(
        protected OperationService $opService,
    ) {}

    public function getTypes(Request $request)
    {
        return response()->json([
            'data' => $this->opService->getLayouts(),
        ]);
    }

    public function index(Request $request)
    {
        $layoutIds = $request->get('layoutIds', []);
        $conditions = $request->only([
            'no',
            'start',
            'end',
            'employee',
            'per_page',
            'page',
            'extraSearch',
        ]);

        $extraSearch = [];
        if (!empty($conditions['extraSearch'])) {
            $extraSearch = json_decode($conditions['extraSearch'], true);
        }

        $dataset = $this->opService->getLayoutData(
            layoutIds: $layoutIds,
            condition: $conditions,
            isExpr: false,
            extraSearch: $extraSearch,
        );

        return response()->json([
            'data' => $dataset,
        ]);
    }

    public function export(Request $request)
    {
        $layoutId = $request->get('layoutId');
        $conditions = $request->only(['no']);

        $showColumns = $request->get('showColumns', [
            '需求名稱',
            '用印單號',
            '申請日',
            '需求日',
            '會計代號',
            '專案代號',
            '專案簡稱',
            '用途',
            '申請人',
            '公司(用印單位)',
            '簽章種類',
            '部門',
            '專技人員',
            '防火管理人/勞安人員',
            '政府-室裝文件',
            '需求文件',
            '備註',
            'PM簽核',
            'PD簽核',
            '總管理處簽核',
            '會簽人員簽核',
            '財務初審',
            '財務覆核',
            '財務核決',
            '財務用印',
            '財務初審/用印',
        ]);

        return $this->opService->export(
            layoutId: $layoutId,
            condition: $conditions,
            showColumns: $showColumns,
        );
    }
}
