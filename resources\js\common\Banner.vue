 <template>
  <div class="w-full h-20 bg-white shadow-md flex felx-row justify-between">
    <div v-if="showStaff || showNotification" class="absolute left-0 w-full h-full z-20" @click="showStaff=false; showNotification=false"> </div>
    <ul class="flex px-3 md:px-6 pt-7">
      <li
        v-for="(name, name_index) in names"
        :key="name_index"
        class="mr-3 opacity-50 hover:opacity-100 hover:font-bold hover:border-b-4 whitespace-nowrap cursor-pointer"
        :class="isActive(name_index)"
        @click="this.$parent.currentTab = name_index"
      >
        <p class="px-3 md:px-6 xl:px-10 pb-7">{{ name }}</p>
      </li>
    </ul>
    <span class="relative mr-8 md:mr-12 xl:mr-16 text-neutral-700 flex justify-center items-center gap-8 md:gap-12 xl:gap-16">
      <font-awesome-icon @click="openStaff" class="cursor-pointer" :icon="['far', 'user-circle']" size="xl"/>
      <div v-show="showStaff" class="absolute top-16 -right-5 w-max shadow"  style="width: 300px;">
        <div class="w-full relative p-8 bg-white rounded-lg shadow z-20">
          <div class="flex justify-between">
            <div class="flex pr-2">
              <div class="my-auto">
                <span class="text-xl font-semibold">{{
                  userInfo == null ? "" : userInfo.name
                }}</span>
                <br />
                <p class="text-sm text-gray-400">
                  {{ userInfo == null ? "" : userInfo.title }}
                </p>
              </div>
            </div>
            <div class="items-center">
              <a href="/sso/logout"><Button label="登出" class="w-28 custom-button" /></a>
            </div>
          </div>
          <div class="flex justify-between mt-6">
            <div class="flex w-full">
              <div class="w-1/2">
                <span class="text-sm text-gray-400"> 員工編號 </span>
                <br />
                {{ userInfo == null ? "" : userInfo.no }}
              </div>
              <span class="text-3xl text-gray-100">｜</span>
              <div class="w-1/2">
                <span class="text-sm text-gray-400"> 部門 </span>
                <br />
                {{ userInfo == null ? "" : userInfo.dep }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <span :class="{'new-message-bell': newMessage}">
        <font-awesome-icon  @click="openNotification" :icon="['far', 'bell']" class="cursor-pointer" size="xl"/>
      </span>
      <div v-show="showNotification" class="absolute top-16 -right-5 w-max shadow"  style="width: 400px;">
        <div class=" w-full max-h-80 relative p-6 pl-4 bg-white rounded-lg shadow z-20">
          <!-- header -->
          <div class="flex justify-between items-center pb-4" >
            <span class="text-xl font-bold pl-2">通知</span>
            <span class="underline cursor-pointer" @click="cleanAllNotification">清除通知</span>
          </div>
          <!-- content -->
          <div class="max-h-56 overflow-hidden overflow-y-scroll scroll-table">
            <ul v-if="notification.length">
              <li v-for="note, noteIndex in notification" class="pl-4 py-4 text-gray-400" :class="{'border-t': noteIndex!==0,  'new-message': !note.read_at}">
                <a :href="note.data.redirectTo" class="hover:underline" @click="readNotification(note.id, note.read_at)">{{ note.data.content }}</a>
              </li>
            </ul>
            <div v-else class="pt-2 pl-2">
              <a href="">當前暫無任何新訊息</a>
            </div>
          </div>
        </div>
      </div>
    </span>
  </div>
</template>
<script>

export default {
  components: {
  },
  props: ["names"],
  data: function () {
    return {
      apiURL: "/api/acc",
      showStaff: false,
      showNotification: false,
      userInfo: null,
      notification: [],
    };
  },
  mounted(){
    this.getUser();
    this.fetchNotification();
  },
  methods: {
    getUser() {
      if (this.userInfo != null) {
        return;
      }
      axios
        .get("/api/acc/common/employee/data")
        .then((response) => {
          this.userInfo = null;
          this.userInfo = response.data;

          if (response.data?.company_code) {
            localStorage.setItem("company_code", response.data.company_code);
          }
          if (response.data?.session_company_code !== null) {
            localStorage.setItem("company_code", response.data.session_company_code);
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },
    fetchNotification() {
      axios
        .get(this.apiURL + '/notify')
        .then(res => {
          this.notification = res.data.data
        })
        .catch(err => {
          console.log(err)
        })
    },
    openStaff() {
      this.showNotification = false
      this.showStaff = !this.showStaff
    },
    openNotification() {
      this.showStaff = false
      this.showNotification = !this.showNotification
    },
    readNotification(id, read) {
      if(!read) {
        axios
          .patch(this.apiURL + '/notify/read/' + id)
          .then(res => {
          })
          .catch(err => {
            this.$toast.add({
              severity: 'error',
              summary: 'Error Message',
              detail: '清除失敗',
              life: 3000
            });
          })
      }
    },
    cleanAllNotification() {
      axios
        .delete(this.apiURL + '/notify/all')
        .then(res => {
          this.notification = []
        })
        .catch(err => {
          this.$toast.add({
            severity: 'error',
            summary: 'Error Message',
            detail: '清除失敗',
            life: 3000
          });
        })
    }
  },
  computed: {
    isActive() {
      return (tabIndex) => {
        return this.$root.currentTab == tabIndex ? "active" : null;
      };
    },
    newMessage() {
      if(this.notification.length!==0) {
        if(this.notification.some(el => !el.read_at)) {
          return true
        }
        return false
      }
      return false
    }
  },
};
</script>
<style scoped>
.scroll-table::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
ul li.active {
  opacity: 100;
  font-weight: 700;
  border-bottom: 4px solid #1677FF;
}
.custom-button {
  width: 95px !important;
  padding: 10px 12px;
}
.new-message-bell {
  position: relative;
}
.new-message-bell::after {
  content: "";
  position: absolute;
  top: -0.25rem;
  right: -0.375rem;
  display: block;
  width: 0.375rem;
  aspect-ratio: 1;
  background-color: rgb(59 130 246);
  border-radius: 100%;
}
.new-message {
  position: relative;
  color: black
}
.new-message::before {
  content: "";
  position: absolute;
  top: 45%;
  left: 0%;
  display: block;
  width: 0.375rem;
  aspect-ratio: 1;
  background-color: rgb(59 130 246);
  border-radius: 100%;
}
</style>
