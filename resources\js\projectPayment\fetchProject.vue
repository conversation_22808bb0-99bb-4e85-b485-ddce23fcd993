<template>
  <!-- project -->
  <div v-if="action == 0">
    <div class="p-6 flex flex-wrap w-full bg-white rounded-xl shadow">
      <label class="my-auto"> 起始年月:&ensp; </label>
      <Calendar v-model="year_start" view="month" dateFormat="yy/mm" placeholder="請選擇年月" />&emsp;
      <label class="my-auto"> 結束年月:&ensp; </label>
      <Calendar v-model="year_end" view="month" dateFormat="yy/mm" placeholder="請選擇年月"
        v-on:keyup.enter="fetchPjPay()" />&emsp;

      <label class="my-auto"> 批次:&ensp; </label>
      <MultiSelect v-model="select_fetch" :options="pbatchno" :filter="true" optionValue="code" optionLabel="name"
        dataKey="code" placeholder="請選擇" :maxSelectedLabels="3" class="w-25 md:w-20rem" />
      &emsp;
      <label class="my-auto"> 會計代號(選填):&ensp; </label>
      <InputText type="text" v-model="epjacc" placeholder="搜尋會計代號" @input="epjacc = $event.target.value.toUpperCase()"
        v-on:keyup.enter="fetchPjPay()" />&emsp;

      <Checkbox class="my-auto" v-model="force_fetch" :binary="true" />&ensp;
      <label class="my-auto">強制撈取</label>&emsp;
      <Button type="button" label="重置" :loading="loading" @click="clear()" text raised />
      &emsp;
      <Button type="button" label="查詢" icon="pi pi-search" :loading="loading" :disabled="year_start == null || year_end == null || select_fetch.length < 1
        " @click="fetchPjPay()" />
      &emsp;
      <Button label="即時更新 ERP" severity="secondary" text @click="showErp()" />
    </div>
    <div class="p-6 my-6 bg-white rounded-xl shadow whitespace-nowrap overflow-x-auto flex" :class="projects.length == 0 || projects.data?.length == 0
      ? 'justify-center items-center'
      : 'flex-col'
      " :style="projects.length == 0 || projects.data?.length == 0
        ? 'height : calc(100vh - 276px)'
        : ''
        ">
      <div class="text-center" v-if="projects.length == 0 || projects.data?.length == 0">
        <!-- <ProgressSpinner v-if="loading" />
        <img
          v-else-if="projects.length == 0"
          src="../../images/page_state/search.png"
          alt=""
        /> -->
        <img v-if="!loading" src="../../images/page_state/search_empty.png" alt="" />
        <p v-if="!loading && projects.length == 0">輸入您要查詢的資料</p>
        <p v-else-if="!loading && projects.data?.length == 0">
          無任何查詢結果，請再次查詢
        </p>
      </div>
      <!-- class="p-6 my-6 bg-white rounded-xl shadow whitespace-nowrap overflow-x-auto flex flex-col" -->
      <div v-else>
        <DataTable v-model:selection="selectedProjects" :value="projects.data" dataKey="pkey" :metaKeySelection="false"
          :scrollable="true" scrollHeight="600px" responsiveLayout="scroll" stripedRows @row-dblclick="Row_dblclick">
          <!-- tableStyle="flex:110%" -->
          <!-- @rowSelect="onRowSelect" -->
          <template #header>
            <ConfirmPopup class="confirmLayout"></ConfirmPopup>
            <div class="flex flex-wrap align-items-center justify-between gap-2">
              <!-- <span class="text-xl text-900 font-bold">專案付款</span> -->
              <p class="text-xl text-900 font-bold">專案付款</p>

              <Button v-if="select_fetch.length == 1" label="送出審核" :loading="loading"
                :disabled="selectedProjects.length == 0" @click="paySubmit($event)" />
            </div>
          </template>
          <Column frozen selectionMode="multiple" headerStyle="width: 3rem" :checked="checkCount()"></Column>
          <Column frozen field="epjacc" header="會計代號" bodyClass="columnHover"></Column>
          <Column frozen field="epjno" header="專案代號" bodyClass="columnHover"></Column>
          <Column frozen field="epjna" header="專案簡稱" footer="小計" bodyClass="columnHover"></Column>
          <Column field="pdno" header="PD姓名" bodyClass="columnHover"></Column>
          <Column field="pmno" header="PM姓名" bodyClass="columnHover"></Column>
          <Column field="pcno" header="CPM姓名" bodyClass="columnHover"></Column>
          <Column field="pleper" header="請款%" style="text-align: right" bodyClass="columnHover">
            <template #body="slotProps">
              {{ slotProps.data.pleper }}%
            </template>
          </Column>
          <Column field="venpayper" header="付款%" style="text-align: right" bodyClass="columnHover">
            <template #body="slotProps">
              {{ slotProps.data.venpayper }}%
            </template>
          </Column>
          <Column field="difamt" header="收付差額" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'difamt')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.difamt) }}
            </template>
          </Column>
          <Column field="sdate" header="開工日期" bodyClass="columnHover"></Column>
          <Column field="odate" header="完工日期" bodyClass="columnHover"></Column>
          <Column field="atypena" header="專案現況" bodyClass="columnHover"></Column>
          <Column field="qupamtc" header="合約報價" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'qupamtc')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.qupamtc) }}
            </template>
          </Column>
          <Column field="qupamtad" header="追加減報價" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'qupamtad')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.qupamtad) }}
            </template>
          </Column>
          <Column field="qupamt" header="總報價款" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'qupamt')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.qupamt) }}
            </template>
          </Column>
          <Column field="tqurece" header="已收款" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'tqurece')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.tqurece) }}
            </template>
          </Column>
          <Column field="tqurecn" header="已請未收" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'tqurecn')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.tqurecn) }}
            </template>
          </Column>
          <Column field="budamt" header="預算成本" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'budamt')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.budamt) }}
            </template>
          </Column>
          <Column field="vencamt" header="總發包款" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'vencamt')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.vencamt) }}
            </template>
          </Column>
          <Column field="venper" header="發包%" style="text-align: right" bodyClass="columnHover">
            <template #body="slotProps">
              {{ slotProps.data.venper }}%
            </template>
          </Column>
          <Column field="tvpay" header="發包已付" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'tvpay')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.tvpay) }}
            </template>
          </Column>
          <Column field="nacpay" header="請款未付" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'nacpay')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.nacpay) }}
            </template>
          </Column>
          <Column field="venpay" header="本期請款" style="text-align: right" bodyClass="columnHover" :footer="this.$parent.computedDetailTotal(selectedProjects, 'venpay')
            ">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.venpay) }}
            </template>
          </Column>
          <!-- <ColumnGroup type="footer">
        <Row>
          <Column
            footer="Totals:"
            :colspan="3"
            footerStyle="text-align:right"
          />
          <Column field="difamt" :footer="totalGroup.difamt" />
        </Row>
      </ColumnGroup> -->
          <!-- <ProgressSpinner
            v-if="loading"
            style="position: absolute; top: 50%; left: 50%; z-index: 10"
          /> -->
        </DataTable>
        <Paginator v-if="!visibleBottom" :data="projects" @page="(val) => fetchPjPay(val.page, val.per)" />
      </div>
    </div>
  </div>

  <!-- detail -->
  <div v-if="action == 1">
    <div class="w-16">
      <span @click="
        details = [];
      selectedDetails = [];
      action = 0;
      " class="cursor-pointer my-auto">
        <p><font-awesome-icon :icon="['fa', 'arrow-left']" /> 返回</p>
      </span>
    </div>

    <br />

    <div v-for="(detail, detail_index) in details" :key="detail_index">
      <!-- 專案資訊 -->
      <div class="p-6 w-full bg-white rounded-xl shadow">
        <div>
          <span class="text-xl text-900 font-bold">專案資訊</span>
        </div>
        <div class="flex align-items-center justify-between gap-2">
          <div class="w-2/3">
            <div class="flex flex-wrap justify-between w-full mt-8">
              <div class="w-40">
                <label class="text-sm text-gray-400">會計代號</label>
                <p>{{ pjInfo.epjacc }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">專案代號</label>
                <p>{{ pjInfo.epjno }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">專案簡稱</label>
                <p>{{ pjInfo.epjna }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">付款年月</label>
                <p>{{ detail[0].symno }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">批次</label>
                <p>{{ detail[0].pbatchno }}</p>
              </div>
            </div>
            <div class="flex flex-wrap justify-between w-full my-4">
              <div class="w-40">
                <label class="text-sm text-gray-400">總報價款</label>
                <p>{{ this.$parent.FormatValue(pjInfo.qupamt) }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">已收款</label>
                <p>{{ this.$parent.FormatValue(pjInfo.tqurece) }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">已請未收</label>
                <p>{{ this.$parent.FormatValue(pjInfo.tqurecn) }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">請款%數</label>
                <p>{{ pjInfo.pleper }}</p>
              </div>
              <div class="w-40"></div>
            </div>
            <div class="flex flex-wrap justify-between w-full">
              <div class="w-40">
                <label class="text-sm text-gray-400">總發包款</label>
                <p>{{ this.$parent.FormatValue(pjInfo.vencamt) }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">發包已付</label>
                <p>{{ this.$parent.FormatValue(pjInfo.tvpay) }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">本期請款</label>
                <p>{{ this.$parent.FormatValue(pjInfo.venpay) }}</p>
              </div>
              <div class="w-40">
                <label class="text-sm text-gray-400">付款%數</label>
                <p>{{ pjInfo.venpayper }}</p>
              </div>
              <div class="w-40"></div>
            </div>
          </div>

          <div class="">
            <ConfirmPopup> </ConfirmPopup>
            <Button v-if="select_fetch.length == 1" label="送出審核" :loading="loading" @click="detailSubmit($event)" />
          </div>
        </div>
      </div>
      <!-- 付款明細 -->
      <div class="p-6 my-6 w-full bg-white rounded-xl shadow">
        <DataTable v-model:selection="selectedDetails" :value="detail" dataKey="id" :metaKeySelection="false"
          :scrollable="true" scrollHeight="600px" responsiveLayout="scroll" stripedRows>
          <template #header>
            <div class="flex align-items-center justify-between gap-2">
              <p class="text-xl text-900 font-bold">專案付款</p>
              <div v-if="select_fetch.length == 1" class="flex">
                <Button v-if="!isEdit" label="編輯" @click="isEdit = true" />
                <Button v-if="isEdit" label="取消" @click="
                  isEdit = false;
                selectedDetails = [];
                " severity="secondary" outlined />
                <Button v-if="isEdit" label="保留" @click="
                  isEdit = false;
                saveDetails();
                " />
              </div>
            </div>
          </template>
          <Column v-if="isEdit" frozen selectionMode="multiple" headerStyle="width: 3rem"></Column>
          <Column header="#" headerStyle="width:4rem">
            <template #body="slotProps">
              {{ slotProps.index + 1 }}
            </template>
          </Column>
          <Column field="venno" header="廠商代號"></Column>
          <Column field="venna" header="廠商簡稱"></Column>
          <Column field="payrmk" header="付款摘要" style="width: 10%" footer="小計"></Column>
          <Column field="vencamt" header="發包總價" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'vencamt')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.vencamt) }}
            </template>
          </Column>
          <Column field="tvenpay" header="發包已付" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'tvenpay')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.tvenpay) }}
            </template>
          </Column>
          <Column field="balance" header="發包結餘" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'balance')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.balance) }}
            </template>
          </Column>
          <Column field="npay" header="本期請款" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'npay')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.npay) }}
            </template>
          </Column>
          <Column field="ppay" header="本期預付" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'ppay')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.ppay) }}
            </template>
          </Column>
          <Column field="hpay" header="本期保留" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'hpay')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.hpay) }}
            </template>
          </Column>
          <Column field="dpay" header="本期折讓" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'dpay')">
            <template #body="slotProps">
              {{ $parent.FormatValue(slotProps.data.dpay) }}
            </template>
          </Column>
          <Column field="venpay" header="本期實付" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'venpay')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.venpay) }}
            </template>
          </Column>
          <Column field="tax" header="稅金" style="text-align: right"
            :footer="this.$parent.computedDetailTotal(detail, 'tax')">
            <template #body="slotProps">
              {{ this.$parent.FormatValue(slotProps.data.tax) }}
            </template>
          </Column>
          <!-- <ColumnGroup type="footer">
            <Row>
              <Column v-for="(col, colIndex) of total_row" :key="colIndex" />
            </Row>
          </ColumnGroup> -->
        </DataTable>
      </div>
    </div>
  </div>
  <!-- 即時更新 ERP -->
  <Dialog header="即時更新 ERP" v-model:visible="displayModal" :dismissableMask="false" :style="{
    'min-width': '500px',
    'max-height': '80vh',
    'z-index': 100,
  }" :contentStyle="{
    height: '100%'
  }" :modal="true" :draggable="false">
    <div class="my-auto font-bold">
      <!-- :options="getRoles"  v-model="reviewToNodeId" @input="invalids.orgs = false"-->

      <div class="pb-8">
        <label class="my-auto">年月</label>
        <br />

        <Calendar v-model="symno" view="month" dateFormat="yy/mm" placeholder="請選擇年月" />
      </div>

      <div class="pb-8">
        <label class="my-auto">批次</label>
        <br />
        <Dropdown v-model="batch" :options="pbatchno" filter optionLabel="name" optionValue="code" placeholder="請選擇批次"
          class="w-2/3 md:w-22rem" />
      </div>
      <div class="pb-8">
        <label class="my-auto">會計代號</label>
        <br />
        <AutoComplete v-model="erpEpjacc" :suggestions="epjacc_list" placeholder="請輸入"
          @input="erpEpjacc = $event.target.value.toUpperCase()"
          @complete="(event) => this.epjacc_list = this.epj_database.map((el) => el.epjacc).filter(el => el.includes(event.query.toUpperCase()))"
          @item-select="(event) => {
            this.erpEpjno = this.epj_database.find(el => el.epjacc == event.value).epjno;
            this.erpEpjname = this.epj_database.find(el => el.epjacc == event.value).epjname;
          }" />
      </div>
      <div class="pb-8">
        <label class="my-auto">專案代號</label>
        <br />
        <AutoComplete v-model="erpEpjno" :suggestions="epjno_list" placeholder="請輸入"
          @input="erpEpjno = $event.target.value.toUpperCase()"
          @complete="(event) => this.epjno_list = this.epj_database.map((el) => el.epjno).filter(el => el.includes(event.query.toUpperCase()))"
          @item-select="(event) => {
            this.erpEpjacc = this.epj_database.find(el => el.epjno == event.value).epjacc;
            this.erpEpjname = this.epj_database.find(el => el.epjno == event.value).epjname;
          }" />
      </div>
      <div>
        <label for="erpName" class="my-auto">專案簡稱</label>
        <br />
        <InputText id="erpName" v-model="erpEpjname" type="text" :disabled="true" />
      </div>
    </div>
    <template #footer>
      <Button @click="displayModal = false" label="取消" class="p-button-outlined p-button-secondary w-28 mr-5"
        :disabled="loading" />
      <Button @click="bellErpGet()" label="更新" class="w-28"
        :disabled="symno == null || batch == null || erpEpjno == '' || loading" />
    </template>
  </Dialog>
  <!-- Toast -->
  <Toast position="top-center" />
  <!-- ProgressSpinner -->
  <ProgressSpinner v-if="loading" style="position: absolute; top: 50%; left: 50%; z-index: 1201" />
  <!--  Sidebar -->
  <Sidebar v-model:visible="visibleBottom" position="bottom" :dismissable="false" :baseZIndex="1000" :modal="false"
    :showCloseIcon="false" class="p-sidebar-xs">
    <div class="flex justify-end w-full">
      <p class="text-sm my-auto">已選取{{ selectedProjects.length }}個</p>
      <p @click="
        selectedProjects = [];
      visibleBottom = false;
      " class="mx-12 text-sm text-blue-600 underline my-auto cursor-pointer">
        取消選取
      </p>
    </div>
  </Sidebar>
</template>
<script>
import axios from "axios";
import Paginator from "../common/Paginator.vue";
import AutoComplete from 'primevue/autocomplete';

export default {
  components: {
    Paginator,
    AutoComplete
  },
  data: function () {
    return {
      isEdit: false,
      loading: false,
      action: 0,
      year_start: this.$parent.newDate,
      year_end: this.$parent.newDate,
      select_fetch: ["1"],
      epjacc: "",
      force_fetch: false,
      pbatchno: [
        { name: 1, code: 1 },
        { name: 2, code: 2 },
        { name: 3, code: 3 },
        { name: 4, code: 4 },
        { name: 5, code: 5 },
      ],
      projects: [],
      selectedProjects: [],
      details: [],
      selectedDetails: [],
      detailTotalRow: [],
      pjInfo: null,
      total_row: [],
      visibleBottom: false,
      apiURL: "/api/acc/pjpay",

      //即時撈取 ERP
      displayModal: false,
      symno: null,
      batch: "",
      erpEpjno: "",
      erpEpjacc: "",
      erpEpjname: "",
      epj_info: [],
      epj_database: [],
      epjacc_list: [],
      epjno_list: [],
      epjname_list: [],
    };
  },
  mounted() {
    this.get_batch();
    this.fetchEpj();
  },
  methods: {
    //#region fetch start
    bellErpGet() {
      if (this.symno == null || this.batch == null || this.erpEpjno == "") {
        this.$parent.toastError("請輸入年月、批次與專案代號!");
        return;
      }
      this.loading = true;
      let param = {
        symno: this.symno,
        batch: this.batch,
        erpEpjno: this.erpEpjno,
      };
      axios
        .post(this.apiURL + "/erp/project", param)
        .then((response) => {
          if (response.status == 200 || response.status == 204) {
            this.displayModal = false;
            this.$parent.toastSucess("處理中請稍後!",7300);
            setTimeout(() => {
              this.year_start = this.symno;
                this.year_end = this.symno;
                this.select_fetch = [this.batch];
                this.force_fetch = true;
                this.epjacc = this.erpEpjacc;
                this.fetchPjPay(1, 50);
            }, 1000 * 8);
          }
          else {
            this.$parent.toastError("撈取失敗");
          }
        })
        .catch((error) => {
          console.error(error);
          this.$parent.toastError("撈取失敗");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    get_batch() {
      axios
        .get(this.apiURL + "/get-batch", {
          params: {},
        })
        .then((response) => {
          this.pbatchno = response.data;
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => { });
    },
    fetchPjPay(page, per) {
      if (
        this.year_start == null ||
        this.year_end == null ||
        this.select_fetch.length < 1
      ) {
        this.$parent.toastError("請輸入開始、結束日期與批次!");
        return;
      }

      this.loading = true;
      axios
        .get(this.apiURL + "/list", {
          params: {
            startYm: this.year_start,
            endYm: this.year_end,
            pbatch: this.select_fetch,
            forceFetch: this.force_fetch,
            epjacc: this.epjacc,
            page: page,
            per: per,
          },
        })
        .then((response) => {
          if (response) {
            this.projects = response.data ?? [];
            this.computedTotalRow();
          } else {
            this.$parent.toastError("查詢失敗");
          }
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchDetail(data) {
      this.pjInfo = data;
      if (!this.year_start || !this.year_end || !this.select_fetch) {
        this.$parent.toastError("請輸入開始、結束日期與批次!");
        return;
      }
      this.loading = true;
      axios
        .get(this.apiURL + "/detail_list", {
          params: {
            startYm: this.year_start,
            endYm: this.year_end,
            pbatch: this.select_fetch,
            pkey: data.pkey,
          },
        })
        .then((response) => {
          this.details = response.data.data ?? [];
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetchEpj() {
      axios
        .get(this.apiURL + "/epj/info")
        .then((response) => {
          this.epj_database = response.data;
          this.epjacc_list = response.data.map((el) => el.epjacc);
          this.epjno_list = response.data.map((el) => el.epjno);
          this.epjname_list = response.data.map((el) => el.epjname);
        })
        .catch((error) => {
          console.log(error)
        })
    },
    search(event) {

    },
    computedTotalRow() {
      if (this.projects.length < 1) return;
      //將json obj轉乘array並map
      this.total_row = Object.values(this.projects.data[0]).map(
        (col, index) => {
          //尋找要算出的欄位名稱
          if (Object.keys(this.projects.data[0])[index] == "difamt") {
            //算出該欄位的加總，'difamt'(收付差額的順序是第16個) 呼應code第107行的':footer="total_row[16]'"
            return this.projects.data.reduce((total, obj) => {
              return total + Object.values(obj)[index];
            }, 0);
          } else {
            return null;
          }
        }
      );
    },

    // 外頁送審
    paySubmit(event) {
      this.$confirm.require({
        target: event.currentTarget,
        message: "是否確定送出審核?",
        acceptLabel: "確定",
        rejectLabel: "取消",
        accept: () => {
          let param = {
            startYm: this.year_start,
            endYm: this.year_end,
            pbatch: this.select_fetch[0],
            projects: this.selectedProjects,
          };
          axios
            .post(this.apiURL + "/submit-pay", param)
            .then((response) => {
              if (response.data) {
                this.$parent.toastSucess("送出成功");
                this.selectedProjects = [];
                this.fetchPjPay();
              } else {
                this.$parent.toastError("送出失敗");
              }
            })
            .catch((error) => {
              console.error(error);
              this.$parent.toastError("送出失敗");
            });
        },
      });
    },
    // submit-detail

    // 內頁送審
    detailSubmit(event) {
      this.$confirm.require({
        target: event.currentTarget,
        message: "是否確定送出審核?",
        acceptLabel: "確定",
        rejectLabel: "取消",
        accept: () => {
          let param = {
            startYm: this.year_start,
            endYm: this.year_end,
            pbatch: this.select_fetch[0],
            pjInfo: this.pjInfo,
            details: this.details,
          };
          axios
            .post(this.apiURL + "/submit-detail", param)
            .then((response) => {
              if (response.data) {
                this.$parent.toastSucess("送出成功");
                this.details = [];
                this.action = 0;
                this.fetchPjPay();
              } else {
                this.$parent.toastError("送出失敗");
              }
            })
            .catch((error) => {
              console.error(error);
              this.$parent.toastError("送出失敗");
            });
        },
      });
    },
    getEpj() {
      axios
        .get(this.apiURL + "/epj/info", {
          params: {},
        })
        .then((response) => {
          this.epj_info = response.data;
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => { });
    },

    //#endregion
    // fetch end
    // ===========================================================
    //#region 邏輯或業務 start

    //#endregion
    // 邏輯或業務 end
    // ===========================================================
    //#region 介面互動 start

    showErp() {
      this.symno = this.$parent.newDate;
      this.batch = "1";
      this.erpEpjno = "";
      this.erpEpjacc = "";
      this.displayModal = true;
    },
    clear() {
      this.year_start = this.$parent.newDate;
      this.year_end = this.$parent.newDate;
      this.select_fetch = ["1"];
      this.force_fetch = false;
      this.epjacc = "";
    },

    Row_dblclick(event) {
      this.action = 1;
      this.fetchDetail(event.data);
    },
    saveDetails() {
      this.details = Object.values(this.details).map((item) => {
        return item.filter((x) => this.selectedDetails.includes(x));
      });
      this.selectedDetails = [];
    },
    checkCount() {
      if (this.action == 0 && this.selectedProjects.length > 0) {
        this.visibleBottom = true;
      } else if (this.action == 1 && this.selectedDetails.length > 0) {
        this.visibleBottom = true;
      } else {
        this.visibleBottom = false;
      }
    },
    //#endregion
    // 介面互動 end
    // ===========================================================
  },
};
</script>
