<template>
  <Banner :names="['依專案查詢', '依人員查詢']" />
  <Search v-if="!currentTab" mode="historyByCase" :subsidiary="subsidiary" :employeeList="employeeList">
    <template #dataHeader="dataHeaderProps">
      <span class="text-lg font-bold">核定結果</span>
      <Button label="全部匯出" @click="dataHeaderProps.exportBonus"></Button>  
    </template>
    <template #table="tableProps">
      <div class="grow flex flex-col">
        <!-- list head -->
        <div class="overflow-x-scroll overflow-hidden scroll-header text-left" @scroll="tableProps.verticalAlignTable">
          <div class="flex font-sans bg-gray-50 sticky top-0 z-10" style="min-width: 1572px;">
            <!-- <label for="selectAll" class="block aspect-square cursor-pointer flex justify-center items-center" style="min-width: 80px;">
              <input v-model="tableProps.selectAll" type="checkbox" class="scale-150 cursor-pointer"  id="selectAll" @change="tableProps.selectAllProjects($event)">
            </label> -->
            <div v-for="col in historyByCaseHeader" class="text-center font-bold flex justify-center items-center" style="height: 80px; min-width: 189px;">{{ col.displayName }}</div>
            <div class="w-[60px]"></div>
          </div>
        </div>
        <!-- list body -->
        <ScrollPanel class="custom-bar h-1 grow">
          <div v-for="row,rowIndex in tableProps.dataList.data" :class="{'bg-gray-50': rowIndex%2 && !row.selected, 'bg-blue-50': row.selected}" class="font-sans font-normal border border-gray-100 hover:border-blue-400" :id="`row_${rowIndex}`" style="min-width: 1572px;">
            <div class="flex">
              <!-- col:checkbox -->
              <!-- <label :for="`select_${rowIndex}`" class="block aspect-square cursor-pointer flex items-center justify-center" style="min-width: 80px;">
                <input v-model="row.selected" type="checkbox" class="scale-150 cursor-pointer" :id="`select_${rowIndex}`" @change="tableProps.selectProject($event, row)">
              </label> -->
              <!-- col:info -->
              <div v-for="col in historyByCaseHeader" class="flex items-center justify-center" style="height: 80px; min-width: 189px;">
                <template v-if="col.section==='amount'">{{ tableProps.formatValue(row.reviews.reduce((acc,cur) => (acc+cur.amount*1),0)) }}</template>
                <template v-else-if="col.section==='suggestionAmount'">{{ tableProps.formatValue(row.reviews.reduce((acc,cur) => (acc+cur.suggestionAmount*1),0)) }}</template>
                <template v-else-if="col.section==='reviewedAmount'">{{ tableProps.formatValue(row.reviews.reduce((acc,cur) => (acc+cur.logs.reduce((acc,cur) => (acc+(cur.finallyReviewerPayload?.[2] ?? 0)),0)),0)) }}</template>
                <template v-else>{{ col.type==="money" ? tableProps.formatValue(row[col.section]) : row[col.section] }}</template>
              </div>
              <!-- col:expand -->
              <div class="flex justify-center items-center w-[60px]">
                <div class="cursor-pointer w-10 aspect-square rounded-full hover:bg-gray-200 flex justify-center items-center" @click="tableProps.expandRow(rowIndex)">
                  <font-awesome-icon :icon="['fas', 'chevron-down']" />
                </div>
              </div>
            </div>
            <div class="w-full expandable-table pl-[40px] pr-[60px]" :class="{'expanding': tableProps.expandRowIndex.includes(rowIndex)}">
              <table class="min-h-0 mt-1 table-fixed w-full">
                <thead>
                  <tr class="h-20 bg-gray-50" :class="{'bg-gray-50': !rowIndex%2 && !row.selected, 'bg-white': rowIndex%2 && !row.selected}">
                    <!-- <th class="bg-white"></th>
                    <th class="bg-white"></th> -->
                    <th v-for="col, colIndex in historyByCaseExpandHeader" class="border-b border-gray-200" :class="{'w-[189px]': colIndex>2}">{{ col.name }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="innerRow,innerRowIndex in row.reviews" class="h-20" :class="{'bg-gray-50': !rowIndex%2 && innerRowIndex%2 && !row.selected, 'bg-white': rowIndex%2 && innerRowIndex%2 && !row.selected}">
                    <!-- <td class="bg-white"></td>
                    <td class="bg-white"></td> -->
                    <td v-for="col in historyByCaseExpandHeader" class="text-center border-b border-gray-200">
                      <template v-if="col.index==='reviewedAmount'">{{ tableProps.formatValue(innerRow.logs.reduce((acc,cur) => (acc+(cur.finallyReviewerPayload?.[2] ?? 0)),0)) }}</template>
                      <template v-else>{{ col.type==='money' ? tableProps.formatValue(innerRow[col.index]) : innerRow[col.index] }}</template>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </ScrollPanel>
      </div>
    </template>
  </Search>
  <Search v-else mode="historyByStaff" :subsidiary="subsidiary" :employeeList="employeeList">
    <template #dataHeader="dataHeaderProps">
      <span class="text-lg font-bold">核定結果</span>
      <Button label="全部匯出" @click="dataHeaderProps.exportBonus"></Button>  
    </template>
    <template #table="tableProps">
      <table class="grow w-full h-max flex flex-col">
        <thead class="overflow-x-scroll overflow-hidden scroll-header text-left" @scroll="tableProps.verticalAlignTable">
          <tr class="h-20 table w-full table-fixed border-y bg-gray-50" style="min-width: 1200px;">
            <th v-for="header in historyByStaffHeader" class="pl-6">
              {{ header.displayName }}
            </th>
          </tr>
        </thead>
        <ScrollPanel class="custom-bar h-1 grow">
          <tbody>
            <tr v-for="row,rowIndex in tableProps.dataList.data" class="text-center table h-20 w-full table-fixed border border-gray-100 hover:border-blue-400 cursor-pointer" :class="{'bg-gray-50': rowIndex%2===1 && !row.selected, 'bg-blue-50': row.selected}" @click="tableProps.openResultDetail(rowIndex)" style="min-width: 1200px;">
              <template v-for="col in historyByStaffHeader">
                <td v-if="col.displayName==='部門'" class="text-left pl-6"> {{ row.info?.orgName ?? '-' }}</td>
                <td v-else-if="col.displayName==='職稱'" class="text-left pl-6"> {{ row.info?.jobTitle ?? '-' }}</td>
                <td v-else class="text-left pl-6"> {{ col.type === 'money' ? tableProps.formatValue(row[col.section]) : row[col.section] }}</td>
              </template>
            </tr>
          </tbody>
        </ScrollPanel>
      </table>
    </template>
  </Search>
</template>
  
<script>

import Banner from "@/common/Banner.vue";
import Search from "@/perfBonus/common/Search.vue";
import ScrollPanel from 'primevue/scrollpanel';
export default {
  components: {
    Banner,
    Search,
    ScrollPanel
  },
  mounted() {
    this.subsidiary = localStorage.getItem('company_code')
    this.fetchEmployeeList()
  },
  data() {
    return {
      currentTab: 0,
      employeeList: [],
      subsidiary: '',
      apiURL: "/api/acc/perf-bonus",
      historyByCaseHeader: [
        {displayName: '專案代號', section: 'epjno', type: "string"},
        {displayName: '會計代號', section: 'accId', type: "string"},
        {displayName: '專案簡稱', section: 'projectName', type: "string"},
        {displayName: '合約總價', section: 'totalPay', type: "money"},
        {displayName: '績效年月', section: 'closeYm', type: "string"},
        {displayName: '績效額度', section: 'amount', type: "money"},
        {displayName: '建議額度', section: 'suggestionAmount', type: "money"},
        {displayName: '核定金額', section: 'reviewedAmount', type: "money"},
      ],
      historyByStaffHeader: [
        {displayName: '人員', section: 'empna', type: "string"},
        {displayName: '部門', section: 'info', type: "string"},
        {displayName: '職稱', section: 'info', type: "string"},
        {displayName: '績效年月', section: 'closeYm', type: "string"},
        {displayName: '專案數量', section: 'projectCount', type: "string"},
        {displayName: '總績效獎金', section: 'totalAmount', type: "money"},
      ],
      historyByCaseExpandHeader: [
        {name: "部門", index: "codeName", type: "string"},
        {name: "初核人員", index: "firstEmpna", type: "object"},
        {name: "覆核人員", index: "secondEmpna", type: "object"},
        {name: "績效額度", index: "amount", type: "money"},
        {name: "建議額度", index: "suggestionAmount", type: "money"},
        {name: "核定金額", index: "reviewedAmount", type: "money"}
      ],
    };
  },
  methods: {
    fetchEmployeeList() {
      axios
        .get(this.apiURL + `/f/employees/${this.subsidiary}`)
        .then(res => {
          this.employeeList = res.data.data
        })
        .catch(err => {
          console.log(err)
        })
    },
  },
};
</script>

<style scoped>
.scroll-header::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.expandable-table {
  display: grid;
  grid-template-rows: 0fr;
  transition: 0.5s;
  overflow: hidden;
}
.expanding {
  grid-template-rows: 1fr;
  padding-bottom: 24px;
}
</style>