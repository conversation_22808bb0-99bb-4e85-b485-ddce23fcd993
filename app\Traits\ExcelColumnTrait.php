<?php

declare(strict_types=1);

namespace App\Traits;

trait ExcelColumnTrait
{
    /**
     * 計算 excel 欄位的工具
     * ============================
     * 範例輸入:
     *  (1): x=0, y=0
     *  (2): x=5, y=0
     *  (3): x=0, y=1
     * ----------------------------
     * 範例輸出:
     *  (1): A1
     *  (2): F1
     *  (3): A2
     * ============================
     *
     * @param int $xAxis
     * @param int|null $yAxis
     * @return string
     */
    public function calculateColumn(int $xAxis = 0, ?int $yAxis = null)
    {
        $tmp = '';
        if ($xAxis > 25) {
            $tmp .= $this->calculateColumn(intval($xAxis / 26) - 1);
        }
        $tmp .= chr($xAxis % 26 + 65);

        if ($yAxis !== null) {
            $tmp .= $yAxis + 1;
        }
        return $tmp;
    }
}
