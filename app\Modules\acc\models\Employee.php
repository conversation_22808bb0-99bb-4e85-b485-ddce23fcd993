<?php

namespace App\Modules\acc\models;

use App\Modules\PerfBonus\Models\PerfEmployeePermission;
use App\Modules\PerfBonus\Models\PerfPermission;
use Illuminate\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class Employee extends Model implements \Illuminate\Contracts\Auth\Authenticatable
{
    use HasFactory, SoftDeletes, Notifiable, Authenticatable, HasApiTokens;

    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $fillable = ['id', 'company_id', 'payload', 'metadata', 'created_by'];
    protected $casts = ['payload' => 'collection', 'metadata' => 'collection'];

    public function orgs()
    {
        return $this->hasManyThrough(OrgUnit::class, OrgUnitMember::class, 'employee_id', 'id', 'id', 'org_unit_id');
    }

    public function company()
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function perfPermissions()
    {
        return $this->hasManyThrough(
            PerfPermission::class,
            PerfEmployeePermission::class,
            'employee_id',
            'id',
            'id',
            'permission_id'
        );
    }
}
