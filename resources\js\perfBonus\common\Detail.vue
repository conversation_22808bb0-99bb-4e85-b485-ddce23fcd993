<template>
  <div v-if="mode!=='historyByStaff'" class="mx-auto p-6 font-sans custom-height-detail" style="max-width: 1620px;">
    <button v-show="detailData" class=" w-20 h-10 rounded-lg flex justify-center items-center gap-2 hover:bg-gray-200" style="color: #1D2939;" @click="$parent.action=0; $parent.detailData=null">
      <i>
        <font-awesome-icon :icon="['fas', 'arrow-left']" />
      </i>
      <span>返回</span>
    </button>
    <template v-if="detailData && detailData.id">
      <!-- header:info -->
      <div class="mt-4 mb-6 p-6 shadow rounded-lg overflow-hidden bg-white" >
        <div class="text-xl font-bold pb-5">專案資訊</div>
        <ul class="flex flex-wrap gap-6 items-center justify-between">
          <template v-if="mode==='review'">
           <li v-for="col in editInfoHeader" style="min-width: 120px;">
              <div class="font-normal text-sm" style="color: #98A2B3;">{{ col.name }}</div>
              <div v-if="col.index === 'Date'" class="font-bold"> {{ (detailData.sDate||'未指定日期') + ' - ' + (detailData.eDate||'未指定日期') }} </div>
              <div v-else-if="col.type === 'money'" class="font-bold"> {{ detailData[col.index] ? formatValue(detailData[col.index]) : '&nbsp;' }} </div>
              <div v-else class="font-bold"> {{ detailData[col.index] || '&nbsp;' }} </div>
            </li>
          </template>
          <template >
           <li v-for="col in infoHeader" style="min-width: 120px;">
              <div class="font-normal text-sm" style="color: #98A2B3;">{{ col.name }}</div>
              <div v-if="col.index === 'Date'" class="font-bold"> {{ (detailData.sDate||'未指定日期') + ' - ' + (detailData.eDate||'未指定日期') }} </div>
              <div v-else-if="col.type === 'money'" class="font-bold"> {{ detailData[col.index] ? formatValue(detailData[col.index]) : '&nbsp;' }} </div>
              <div v-else class="font-bold"> {{ detailData[col.index] || '&nbsp;' }} </div>
            </li>
          </template>
          <slot name="actionButton" :confirm="confirm"></slot>
        </ul>
      </div>
      <!-- body:review -->
      <div class="bg-white rounded-lg overflow-hidden mb-6" >
        <!-- review:header -->
        <div class="flex items-center justify-between p-6" >
          <h2 class="text-xl font-bold">績效獎金核定</h2>
          <div v-if="edit" class="flex gap-4">
            <Button label="新增人員" severity="secondary" outlined class="custom-secondary-button" @click="addMember"/>
            <Button label="儲存修改" severity="secondary" outlined class="custom-secondary-button" @click="saveChange"/>
          </div>
        </div>
        <!-- review:body -->
        <ScrollPanel class="custom-bar">
          <table class="w-full table-fixed" style="min-width: 1551px;">
            <thead>
              <tr class="h-20 bg-gray-50">
                <th v-for="col in tableHeader" :class="{'percentage-25': col.percentage === 25, 'percentage-100': col.percentage === 100}">{{ col.name }}</th>
                <th v-if="edit" class="w-14"></th>
              </tr>
            </thead>
            <tbody>
              <template v-if="edit">
                <tr v-for="row,rowIndex in reviewResult" class="h-20" :class="{'bg-gray-50': rowIndex%2}" :key="row.empno">
                  <td v-if="!row.isNew">
                    <div class="text-sm text-neutral-400">{{ detailData.logs[rowIndex].showTitle }}</div>
                    <div class="font-bold">{{ detailData.logs[rowIndex].empna +  rate(row.payload[0]) }}</div>
                  </td>
                  <td v-else style="padding-left: 16px;">
                    <Dropdown :class="{'p-invalid': !row.empno&&validated}" v-model="row.empno" :options="employeeList" optionValue="number" optionLabel="name" filter @click="validated=false" placeholder="請選擇" panelClass='w-56'>
                      <template #option="slotProps">
                        <span>{{ slotProps.option.orgName + " " + slotProps.option.name }}</span>
                      </template>
                      <template #filtericon>
                        <span class="pi pi-search"></span>
                      </template>
                    </Dropdown>
                  </td>
                  <td>{{ detailData.logs[rowIndex]?.questionExtra?.pmScores[0] || '-' }}</td>
                  <td>{{ detailData.logs[rowIndex]?.questionExtra?.pmScores[1] || '-' }}</td>
                  <td>{{ detailData.logs[rowIndex]?.questionExtra?.pmScores[2] || '-' }}</td>
                  <td>{{ detailData.logs[rowIndex]?.questionExtra?.pmScores[3] || '-' }}</td>
                  <td>
                    <InputNumber inputClass="w-24 h-10 text-center" v-model="row.payload[0]" :min="0" :max="100" @blur="computeTable" :allowEmpty="false"></InputNumber>
                  </td>
                  <td>{{ (Math.round(row.payload[0] / totalScore * 100) || 0) + '%' }}</td>
                  <td>{{ Math.round(Math.pow(row.payload[0],2) / totalScore) || 0 }}</td>
                  <td>{{ formatValue((Math.round(Math.pow(row.payload[0],2) / totalScore / totalProjectScore * detailData.suggestionAmount / 10) * 10||0)) }}</td>
                  <td v-if="detailData.firstFinishedAt">{{ detailData.logs[rowIndex] ? formatValue(detailData.logs[rowIndex].firstReviewerPayload?.[2] ?? 0) : 0 }}</td>
                  <td>
                    <InputNumber inputClass="w-24 h-10 text-center" v-model="row.payload[2]" @blur="validateAmount" :allowEmpty="false"></InputNumber>
                  </td>
                  <td v-if="edit" style="padding: 0;" class="h-20">
                    <div v-if="row.isNew" class="cursor-pointer hover:bg-gray-200 rounded-full w-10 aspect-square flex justify-center items-center" @click="deleteMember(rowIndex)">
                      <img src="../../../images/others/delete.svg" alt="deleteMember">
                    </div>
                  </td>
                </tr>
                <tr class="h-20 font-bold" :class="{'bg-gray-50': reviewResult.length%2}">
                  <td v-for="col in new Array(7)"></td>
                  <td>總計</td>
                  <td>{{ formatValue(totalComputedSuggestionAmount) }}</td>
                  <td v-if="detailData.firstFinishedAt">{{ formatValue(detailData.logs.reduce((acc,cur) => (acc+(cur.firstReviewerPayload?.[2] ?? 0)),0)) }}</td>
                  <td>{{ formatValue(reviewResult.reduce((acc,cur) => (acc+cur.payload[2]), 0)) }}</td>
                  <td></td>
                </tr>
              </template>
              <template v-else>
                <tr v-for="row,rowIndex in detailData.logs" class="h-20" :class="{'bg-gray-50': rowIndex%2}">
                  <td>
                    <div class="text-sm text-neutral-400">{{ row.showTitle || employeeList.find(el => el.number===row.empno).jobTitle  }}</div>
                    <div class="flex justify-between">
                      <div class="font-bold">{{ row.empna + rate(row.finallyReviewerPayload?.[0] ?? row.firstReviewerPayload?.[0] ?? 0) }}</div>
                      <div v-if="row.isNew" class="bg-blue-500 rounded-full text-white w-10 text-center leading-5">new</div>
                    </div>
                  </td>
                  <td>{{ row.questionExtra?.pmScores[0] ?? '-' }}</td>
                  <td>{{ row.questionExtra?.pmScores[1] ?? '-' }}</td>
                  <td>{{ row.questionExtra?.pmScores[2] ?? '-' }}</td>
                  <td>{{ row.questionExtra?.pmScores[3] ?? '-' }}</td>
                  <td>{{ row.finallyReviewerPayload?.[0] ?? row.firstReviewerPayload?.[0] ?? 0 }}</td>
                  <td>{{ (Math.round((row.finallyReviewerPayload?.[0] ?? row.firstReviewerPayload?.[0] ?? 0) / totalScore * 100) || 0) + '%' }}</td>
                  <td>{{ Math.round(Math.pow(row.finallyReviewerPayload?.[0] ?? row.firstReviewerPayload?.[0] ?? 0,2) / totalScore) || 0 }}</td>
                  <td>{{ formatValue((Math.round(Math.pow(row.finallyReviewerPayload?.[0] ?? row.firstReviewerPayload?.[0] ?? 0,2) / totalScore / totalProjectScore * detailData.suggestionAmount / 10) * 10||0)) }}</td>
                  <td>{{ formatValue(row.firstReviewerPayload?.[2]??0) }}</td>
                  <td>{{ row.finallyReviewerPayload?.[2] ?? '-' }}</td>
                </tr>
                <tr class="h-20 font-bold" :class="{'bg-gray-50': reviewResult.length%2}">
                  <td v-for="col in new Array(7)"></td>
                  <td>總計</td>
                  <td>{{ formatValue(totalComputedSuggestionAmount) }}</td>
                  <td>{{ formatValue(detailData.logs.reduce((acc,cur) => (acc+(cur.firstReviewerPayload?.[2]??0)), 0)) }}</td>
                  <td>{{ detailData.secondFinishedAt ? formatValue(detailData.logs.reduce((acc,cur) => (acc+cur.finallyReviewerPayload[2]), 0)) : '-'}}</td>
                </tr>
              </template>
            </tbody>
          </table>
        </ScrollPanel>
      </div>
      <!-- footer:criteria -->
      <div class="p-6 bg-white rounded-lg overflow-hidden">
        <div class="text-xl font-bold pb-4">評鑑標準</div>
        <table class="font-normal">
          <tbody>
            <tr v-for="row in criteria" class="">
              <td style="width: 125px;" class="whitespace-nowrap pr-6">{{ row.class }}</td>
              <td style="width: 575px;" class="pr-6">
                <div class="text-left">{{ row.description }}</div>
              </td>
              <td class="h-10 flex gap-2 whitespace-nowrap">
                <span class="w-5 text-right">{{ row.minimum }}</span>
                <span>-</span>
                <span>{{ row.highest }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </template>
    <Dialog v-model:visible="recommendedAmountWarningView" :draggable="false" modal :showHeader="false" :style="{ width: '384px'}" class="rounded overflow-hidden" >
      <div class="flex flex-col justify-center items-center pt-8 px-6">
        <img src="../../../images/popup_state/warning.svg" alt="warning" class="mb-8">
        <div class="text-xl font-bold mb-24">
          <span>核定金額超出建議額度</span>
          <span class="text-red-500">{{ '$' + amountExceeded }}</span>
        </div>
        <Button label="我知道了" @click="recommendedAmountWarningView = false"></Button>
      </div>
    </Dialog>
  </div>
  <div v-else class="mx-auto p-6 font-sans custom-height-detail" style="max-width: 1620px;">
    <button class=" w-20 h-10 rounded-lg flex justify-center items-center gap-2 hover:bg-gray-200" style="color: #1D2939;" @click="$parent.action=0; $parent.detailData=null">
      <i>
        <font-awesome-icon :icon="['fas', 'arrow-left']" />
      </i>
      <span>返回</span>
    </button>
    <div class="mt-4 mb-6 p-6 shadow rounded-lg overflow-hidden bg-white" >
      <ul class="flex flex-wrap gap-6 items-center justify-between">
        <li v-for="col in staffInfoHeader" style="min-width: 120px;">
          <div class="font-normal text-sm" style="color: #98A2B3;">{{ col.name }}</div>
          <div class="font-bold"> {{ col.type==='money' ?  formatValue(detailData[col.index]) : (detailData[col.index] || '-') }} </div>
        </li>
      </ul>
    </div>
    <div class="bg-white rounded-lg overflow-hidden mb-6" >
      <!-- review:header -->
      <div class="flex items-center justify-between p-6" >
        <h2 class="text-xl font-bold">專案列表</h2>
      </div>
      <!-- review:body -->
      <ScrollPanel class="custom-bar">
        <table class="w-full table-fixed" style="min-width: 1200px;">
          <thead>
            <tr class="h-20 bg-gray-50">
              <th v-for="col in staffTableHeader" class="staff-header" :class="{'percentage-25': col.percentage === 25, 'percentage-100': col.percentage === 100}">{{ col.name }}</th>
              <th v-if="edit" class="w-14"></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="row,rowIndex in detailData.projects" class="h-20" :class="{'bg-gray-50': rowIndex%2}">
              <td v-for="col in staffTableHeader">
                <template v-if="col.index==='number'">{{ rowIndex+1 }}</template>
                <template v-if="col.index==='empno'">{{ detailData[col.index] }}</template>
                <template v-else>{{ col.type==='money' ? formatValue(row[col.index]) : row[col.index] }}</template>
              </td>
            </tr>
          </tbody>
        </table>
      </ScrollPanel>
    </div>
  </div>
</template>

<script>
import Button from 'primevue/button';
import ConfirmPopup from 'primevue/confirmpopup';
import Dialog from 'primevue/dialog';
import Dropdown from 'primevue/dropdown';
import InputNumber from 'primevue/inputnumber';
import ScrollPanel from 'primevue/scrollpanel';
export default {
  components: {
    Button,
    ConfirmPopup,
    Dialog,
    Dropdown,
    InputNumber,
    ScrollPanel
  },  
  props: {
    mode: {},
    edit: {
      default: false
    },
    detailData: {},
    employeeList: {},
  },
  data() {
    return {
      infoHeader: [
        {name: "專案簡稱", index: "projectName", type: "string"},
        {name: "工期", index: "Date", type: "string"},
        {name: "合約總價", index: "totalPay", type: "money"},
        {name: "部門", index: "", type: "string"},
        {name: "績效年月", index: "closeYm", type: "string"}, 
        {name: "績效額度", index: "amount", type: "money"}, 
        {name: "建議額度", index: "suggestionAmount", type: "money"}, 
        {name: "核定金額", index: "", type: "money"}, 
      ],
      editInfoHeader: [
        {name: "專案簡稱", index: "projectName", type: "string"},
        {name: "工期", index: "Date", type: "string"},
        {name: "合約總價", index: "totalPay", type: "money"},
        {name: "績效年月", index: "closeYm", type: "string"}, 
        {name: "績效額度", index: "amount", type: "money"}, 
        {name: "建議額度", index: "suggestionAmount", type: "money"}, 
      ],
      staffInfoHeader: [
        {name: '人員', index: 'empna', type: "string"},
        {name: '部門', index: 'orgName', type: "string"},
        {name: '職稱', index: 'jobTitle', type: "string"},
        {name: '績效年月', index: 'closeYm', type: "string"},
        {name: '專案數量', index: 'projectCount', type: "string"},
        {name: '總績效獎金', index: 'totalAmount', type: "money"},
      ],
      staffTableHeader: [
        {name: '列號', index: 'number', type: "string"},
        {name: '專案代號', index: 'epjno', type: "string"},
        {name: '會計代號', index: 'accId', type: "string"},
        {name: '專案簡稱', index: 'projectName', type: "string"},
        {name: '建議額度', index: 'suggestionAmount', type: "money"},
        {name: '核定金額', index: 'finalAmount', type: "money"},
      ],
      constructorReviewHeader: [
        {name: "工程進度管控", index: "", type: "string", percentage: 25},
        {name: "工程品質管理", index: "", type: "string", percentage: 25},
        {name: "工程問題排除與解決", index: "", type: "string", percentage: 25},
        {name: "與平行同仁及廠商的溝通", index: "", type: "string", percentage: 25}
      ],
      designerReviewedHeader: [
        {name: "創意經驗/解圖能力", index: "", type: "string", percentage: 25},
        {name: "協調意願/圖面準確度", index: "", type: "string", percentage: 25},
        {name: "工作效率", index: "", type: "string", percentage: 25},
        {name: "與平行同仁的溝通", index: "", type: "string", percentage: 25}
      ],
      criteria: [
        {class: "A+", description: "工作表現超越主管期待，足以成為他人楷模", minimum : 90, highest: 100},
        {class: "A", description: "工作表現符合主管期待，能完成90%以上工作任務", minimum :85, highest: 89.9},
        {class: "B", description: "工作表現符合主管期待，能完成80%以上工作任務", minimum : 75, highest: 84.9},
        {class: "C", description: "無法獨立完成交辦任務，經常性需要主管的指導", minimum : 70, highest: 74.9},
        {class: "D", description: "無法獨立完成交辦任務，經常性指後仍與主管期待有落差", minimum : 0, highest: 69.9},
      ],
      apiURL: "/api/acc/perf-bonus",
      reviewResult: [],
      updated: false,
      recommendedAmountWarningView: false,
      amountExceeded: 0,
      validated: false
    };
  },
  mounted() {
  },
  updated() {
    // 資料進來後 且 為 edit mode
    if(this.detailData?.id && this.edit && !this.updated) {
      this.updated = true
      let totalAmount = this.detailData.suggestionAmount
      let totalScore = this.detailData.logs.reduce((acc, cur) => {
        if (cur.firstReviewerPayload) {
          return acc + cur.firstReviewerPayload.pdScore
        } else if (cur.questionExtra) {
          return acc + cur.questionExtra.pdScore
        } 
        return acc
      }, 0)
      let totalProjectScore = this.detailData.logs.reduce((acc, cur) => {
        if (cur.firstReviewerPayload) {
          return acc + cur.firstReviewerPayload.pdScore**2/totalScore
        } else if (cur.questionExtra) {
          return acc + cur.questionExtra.pdScore**2/totalScore
        } 
        return acc
      }, 0)
      this.reviewResult = JSON.parse(JSON.stringify(this.detailData.logs.map(el => {
        if(el.finallyReviewerPayload) {
          return  {
                    empno: el.empno,
                    payload: el.finallyReviewerPayload,
                    isNew: el.isNew
                  }
        } else if(el.firstReviewerPayload) {
          return  {
                    empno: el.empno,
                    payload: el.firstReviewerPayload,
                    isNew: el.isNew
                  }
        }
        return  {
                  empno: el.empno,
                  payload: [(el.questionExtra?.pdScore || 0),  (Math.round((el.questionExtra?.pdScore || 0)**2/totalScore/totalProjectScore*totalAmount / 10) *10||0), (Math.round((el.questionExtra?.pdScore || 0)**2/totalScore/totalProjectScore*totalAmount / 10) *10||0)],
                  isNew: el.isNew
                }
      })))
    }
  },
  methods: {
    // table
    computeTable() {
      this.$nextTick(() => {
        this.reviewResult.map(el => {
          el.payload[1] = Math.round(Math.pow(el.payload[0],2) / this.totalScore / this.totalProjectScore * this.detailData.suggestionAmount / 10) * 10 || 0
          el.payload[2] = Math.round(Math.pow(el.payload[0],2) / this.totalScore / this.totalProjectScore * this.detailData.suggestionAmount / 10) * 10 || 0
          return el
        })
      })
    },
    validateAmount() {
      this.$nextTick(() => {
        if(this.reviewResult.reduce((acc,cur) => (acc+cur.payload[2]), 0) > this.detailData.suggestionAmount) {
          this.amountExceeded = this.reviewResult.reduce((acc,cur) => (acc+cur.payload[2]), 0) - this.detailData.suggestionAmount
          this.recommendedAmountWarningView = true
        }
      })
    },
    addMember() {
      this.reviewResult.push({
        empno: null,
        payload: [0,0,0],
        isNew: true
      })
    },
    deleteMember(index) {
      this.reviewResult.splice(index, 1)
      this.computeTable()
    },
    customOptionLabel(option) {
        return `${option.orgName} ${option.name}`;
    },
    rate(score) {
      if(score>=90) {
        return '(A+)'
      } else if(score>=85) {
        return '(A)'
      } else if(score>=75) {
        return '(B)'
      } else if(score>=70) {
        return '(C)'
      } else if(score>=0) {
        return '(D)'
      }
      return ''
    },
    //action 
    validatePayload() {
      if(this.reviewResult.some(el => !el.empno)) {
        this.validated = true
        return false
      }
      return true
    },
    saveChange() {
      if(this.validatePayload()) {
        axios
          .put(this.apiURL + '/s/review/' + this.detailData.id, {
            confirm: 0,
            data: this.reviewResult
          })
          .then(res => {
            this.$toast.add({ 
              severity: 'success', 
              summary: 'success Message',
              detail: '儲存成功',
              life: 3000
            });
          })
          .catch(err => {
            this.$toast.add({ 
              severity: 'error', 
              summary: 'Error Message',
              detail: '儲存失敗',
              life: 3000
            });
          })
      } else {
        this.$toast.add({ 
          severity: 'error', 
          summary: 'Error Message',
          detail: '儲存失敗，人員不可為空',
          life: 3000
        });
      }
    },
    confirm(e) {
      this.$confirm.require({
        target: e.currentTarget,
        message: '是否確定送出核定?',
        acceptLabel: "確認",
        rejectLabel: "取消",
        accept: () => {
            this.submit()
        },
      });
    },
    submit() {
      if(this.validatePayload()) {
        axios
          .put(this.apiURL + '/s/review/' + this.detailData.id, {
            confirm: 1,
            data: this.reviewResult
          })
          .then(res => {
            this.$toast.add({ 
              severity: 'success', 
              summary: 'success Message',
              detail: '核定成功',
              life: 3000
            });
            this.$parent.action = 0;
            this.$parent.detailData = null;
            this.$parent.dataLoaded = false;
            this.$parent.fetchReviewList()
          })
          .catch(err => {
            this.$toast.add({ 
              severity: 'error', 
              summary: 'Error Message',
              detail: '送出失敗',
              life: 3000
            });
          })
      } else {
        this.$toast.add({ 
          severity: 'error', 
          summary: 'Error Message',
          detail: '送出失敗，人員不可為空',
          life: 3000
        });
      }
    },
    // others
    formatValue(int) {
      return Number.parseFloat(int).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    },
  },
  computed: {
    tableHeader() {
      let header1 = [
        {name: "人員/評鑑", index: "", type: "string"},
      ]

      let header2 = [
        {name: "總計", index: "", type: "string", percentage: 100},
        {name: "百分比", index: "", type: "string"},
        {name: "專案得分", index: "", type: "string"},
        {name: "建議額度", index: "", type: "money"},
        {name: "初核金額", index: "", type: "money"},
      ]
      
      if(this.detailData.commontype*1) {
        header1.push(...this.designerReviewedHeader)
        header1.push(...header2)
      } else {
        header1.push(...this.constructorReviewHeader)
        header1.push(...header2)
      }

      if(this.detailData.firstFinishedAt) {
        header1.push({name: "覆核金額", index: "", type: "money"})
      }

      return header1
    },
    totalScore() {
      let ans = undefined
      if(this.edit) {
        ans = this.reviewResult.reduce((acc,cur) => (acc+cur.payload[0]), 0)
      } else {
        if(this.detailData.secondFinishedAt) {
          ans = this.detailData.logs.reduce((acc,cur) => (acc+cur.finallyReviewerPayload[0]), 0)
        }else {
          ans = this.detailData.logs.reduce((acc,cur) => (acc+(cur.firstReviewerPayload?.[0]??0)), 0)
        }
      }

      return ans
    },
    totalProjectScore() {
      let ans = undefined
      if(this.edit) {
        ans = this.reviewResult.reduce((acc,cur) => (acc+(Math.pow(cur.payload[0],2)/this.totalScore)||0), 0)
      } else {
        if(this.detailData.secondFinishedAt) {
          ans = this.detailData.logs.reduce((acc,cur) => (acc+(Math.pow(cur.finallyReviewerPayload[0],2)/this.totalScore)||0), 0)
        }else {
          ans = this.detailData.logs.reduce((acc,cur) => (acc+(Math.pow((cur.firstReviewerPayload?.[0]??0),2)/this.totalScore)||0), 0)
        }
      }

      return ans
    },
    totalComputedSuggestionAmount() {
      let ans = undefined
      if(this.edit) {
        ans = this.reviewResult.reduce((acc,cur) => (acc+(Math.round(Math.pow(cur.payload[0],2)/this.totalScore/this.totalProjectScore*this.detailData.suggestionAmount / 10)*10)||0), 0)
      } else {
        if(this.detailData.secondFinishedAt) {
          ans = this.detailData.logs.reduce((acc,cur) => (acc+(Math.round(Math.pow(cur.finallyReviewerPayload[0],2)/this.totalScore/this.totalProjectScore*this.detailData.suggestionAmount / 10)*10)||0), 0)
        }else {
          ans = this.detailData.logs.reduce((acc,cur) => (acc+(Math.round(Math.pow((cur.firstReviewerPayload?.[0]??0),2)/this.totalScore/this.totalProjectScore*this.detailData.suggestionAmount / 10)*10)||0), 0)
        }
      }

      return ans
    }
  },
}
</script>

<style scoped>
.staff-header {
  padding-top: 0px;
  vertical-align: middle;
}
th:first-child {
  width: 152px;
  padding-left: 24px;
  text-align: left;
}
th {
  padding-top: 18px;
  vertical-align: top;
}
td:first-child {
  padding-left: 24px;
}
td:not(:first-child) {
  text-align: center;
}
.custom-height-detail {
  min-height: calc(100vh - 160px);
}
@media (min-width: 768px) {
  .custom-height-detail {
    min-height: calc(100% - 80px);
  }
}
.custom-button {
  padding: 10px 18px;
}
table span {
  white-space: nowrap;
}
.custom-secondary-button {
  padding: 10px 18px;
}
::v-deep(.p-button.custom-secondary-button span) {
    color: #292e35;
}
.percentage-25::after {
  content: '(25%)';
}
.percentage-100::after {
  content: '(100%)';
  display: block;
}
::v-deep(.custom-bar .p-scrollpanel-content) {
    padding-bottom: 0px !important;
}
</style>