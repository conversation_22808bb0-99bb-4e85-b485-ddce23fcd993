<?php

use App\Modules\PerfBonus\Models\PerfDepGroup;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('perf_erp_projects', function (Blueprint $table) {
            $table->id();
            $table->string('project_name')->comment('專案名稱');
            // $table->string('company', 10)->commnet('通常為 project 的 no 最前面區分(T台灣,Y上海,Q,F)');
            $table->unsignedBigInteger('company_id')->commnet('companies 的 id');
            $table->string('epjno')->index()->comment('ERP 中的 wap_bepjs\'s epjno 的欄位: 專案id');
            $table->string('accasno')->nullable()->index()->comment('ERP 中的 wap_bepjs\'s accasno 的欄位: 會計單號');
            $table->string('pc_empno', 12)->nullable()->comment('生產管控的emp no');
            $table->string('pc_code', 10)->nullable()->comment('生產管控的emp code');
            $table->string('pm_empno', 12)->nullable()->comment('專案經理的emp no');
            $table->string('pm_code', 10)->nullable()->comment('專案經理的emp code');
            $table->date('s_date')->nullable()->comment('工程起始日');
            $table->date('e_date')->nullable()->comment('工程完工日');
            $table->string('closeym', 8)->default('')->index()->comment('ERP 中的 wap_bepjs\'s closeym 的欄位: 績效年月');
            $table->string('schdepno', 4)->comment('ERP 中的 wap_bepjs\'s schdepno 的欄位: 單位');
            $table->enum('status', ['notApplied', 'inProgress', 'completed'])->comment('送審狀態，總共有三種: notApplied, inProgress, completed')->default('notApplied');
            $table->decimal('total_pay', 12, 2)->default(0)->comment('合約總價');
            $table->timestamps();
        });

        Schema::create('perf_dep_groups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id');
            $table->string('display_name')->comment('顯示部門名稱');
            $table->string('code', 10)->index()->comment('部門代號');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->boolean('hidden')->default(false)->comment('排序');
            $table->string('empno', 12)->nullable()->comment('部門主管員工編號');
            $table->string('gen_code')->storedAs("CASE code
    WHEN 'A' THEN 'A'
    WHEN 'AR' THEN 'A'
    WHEN 'AT' THEN 'A'
    WHEN 'BP' THEN 'B'
    WHEN 'BS' THEN 'B'
    WHEN 'BC' THEN 'B'
    WHEN 'B' THEN 'B'
    WHEN 'RB' THEN 'C'
    WHEN 'R' THEN 'C'
    WHEN 'C' THEN 'C'
    WHEN 'YDB' THEN 'YCB'
    WHEN 'YCB' THEN 'YCB'
    WHEN 'YDR&D' THEN 'YCB'
    WHEN 'YBC' THEN 'YBC'
    WHEN 'YBD' THEN 'YBC'
    ELSE code
end
            ")->index()->comment('部門代號 MAPPING');
        });

        PerfDepGroup::create(['company_id' => '1', 'display_name' => '商空部', 'code' => 'B', 'sort' => 0, 'hidden' => '0', 'empno' => 'B8905441']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => 'Y設二部工程', 'code' => 'YCB', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => 'Y設二部設計', 'code' => 'YDB', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => 'Y商空部設計', 'code' => 'YBD', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => 'Y設二部-研發', 'code' => 'YDR&D', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '商空部PM組', 'code' => 'BP', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '商空部客服組', 'code' => 'BS', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '商空部工程組', 'code' => 'BC', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => 'Q專案部', 'code' => 'QP', 'sort' => 0, 'hidden' => '1']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '層峰', 'code' => '0', 'sort' => 0, 'hidden' => '1']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '行政服務部', 'code' => 'A', 'sort' => 0, 'hidden' => '1']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '工二部', 'code' => 'CB', 'sort' => 10, 'hidden' => '0', 'empno' => 'CB9211620']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '工三部', 'code' => 'CC', 'sort' => 20, 'hidden' => '0', 'empno' => 'CC9308681']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '工五部', 'code' => 'E', 'sort' => 30, 'hidden' => '0', 'empno' => 'E9002481']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '設一部', 'code' => 'DA', 'sort' => 40, 'hidden' => '0', 'empno' => 'DA7807027']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '設二部', 'code' => 'DB', 'sort' => 50, 'hidden' => '0', 'empno' => 'DB8804388']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '設三部', 'code' => 'DC', 'sort' => 60, 'hidden' => '0', 'empno' => 'DA7807027']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '展業部', 'code' => 'RB', 'sort' => 70, 'hidden' => '0', 'empno' => 'RB17051535']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '實施設計部', 'code' => 'TD', 'sort' => 80, 'hidden' => '1', 'empno' => 'DA7807027']);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '資材部', 'code' => 'AR', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '實施設計部', 'code' => 'C', 'sort' => 80, 'hidden' => '0', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '1', 'display_name' => '技術部機電組', 'code' => 'TT', 'sort' => 0, 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '2', 'display_name' => '鴻僑M', 'code' => 'XM' , 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '2', 'display_name' => '鴻僑P', 'code' => 'XP' , 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '2', 'display_name' => '鴻僑委任二部', 'code' => 'XVB' , 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '2', 'display_name' => '鴻僑生產一部', 'code' => 'XMA' , 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '2', 'display_name' => '鴻僑委任一部', 'code' => 'XVA' , 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '2', 'display_name' => '鴻僑委任三部', 'code' => 'XVC' , 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => 'Y行政部', 'code' => 'YA', 'sort' => '0', 'hidden' => '0']);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => 'Y商空部工程', 'code' => 'YBC', 'sort' => '0', 'hidden' => '0']);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => 'Y設二部工程', 'code' => 'YCB', 'sort' => '0', 'hidden' => '0']);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => 'Y研展事業部', 'code' => 'YP', 'sort' => '0', 'hidden' => '0']);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => '設一部', 'code' => 'DA', 'sort' => '0', 'hidden' => '1', 'empno' => 'DA7807027']);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => '商空部PM組', 'code' => 'BP', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => '設二部', 'code' => 'DB', 'sort' => '0', 'hidden' => '1', 'empno' => 'DB8804388']);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => 'Y設二部設計', 'code' => 'YDB', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => 'Y設二部-研發', 'code' => 'YDR&D', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '3', 'display_name' => 'Y商空部設計', 'code' => 'YBD', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q生產一部', 'code' => 'QMA', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q專案部', 'code' => 'QP', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q專案一部', 'code' => 'QPA', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q生產部', 'code' => 'QM', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q製圖組', 'code' => 'QPD', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q專管組', 'code' => 'QPM', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q專案二部', 'code' => 'QPB', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q研展部', 'code' => 'QPP', 'sort' => '0', 'hidden' => '1', 'empno' => null]);
        PerfDepGroup::create(['company_id' => '4', 'display_name' => 'Q財務', 'code' => 'QF', 'sort' => '0', 'hidden' => '1', 'empno' => null]);

        Schema::create('perf_erp_project_employee', function (Blueprint $table) {
            $table->id();
            $table->string('epjno')->index()->comment('ERP 中的 wap_bepjs\'s epjno 的欄位: 專案id');
            $table->string('empno')->index()->comment('員工編號');
            $table->string('empna')->comment('員工姓名');
            $table->string('sno', 5)->nullable()->comment('role');
            $table->string('perf_dep_code')->comment('部門代號');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('perf_erp_projects');
        Schema::dropIfExists('perf_dep_groups');
        Schema::dropIfExists('perf_erp_project_employee');
    }
};
