<?php

namespace App\Console\Commands;

use App\Jobs\DataImport;
use Illuminate\Console\Command;

class AutomationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:cmd';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '每日任務';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DataImport::dispatch();
    }
}
