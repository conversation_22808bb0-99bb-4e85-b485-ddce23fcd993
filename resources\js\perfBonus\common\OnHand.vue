<template>
  <div v-if="!action" class="mx-auto p-6 custom-height" style="max-width: 1620px">
    <div v-if="dataLoaded" class="bg-white flex flex-col shadow rounded-lg overflow-hidden h-full">
      <!-- header -->
      <div class="px-6 py-4 flex flex-wrap gap-1 justify-between border-b">
        <span class="p-input-icon-right">
          <i class="pi pi-search cursor-pointer" @click="search"></i>
          <InputText v-model="projectId" @keydown.enter="search" placeholder="搜尋專案代號"/>
        </span>
        <slot name="headerButton" :selectedProjects="selectedProjects" :confirm="confirm"></slot>
      </div>
      <!-- content -->
      <slot name="table" :dataList="dataList" :openReviewProject="openReviewProject" :verticalAlignTable="verticalAlignTable" :formatValue="formatValue" :selectAll="selectAll" :selectAllProjects="selectAllProjects" :selectProject="selectProject" :expandRow="expandRow" :expandRowIndex="expandRowIndex"></slot>
      <!-- footer & status -->
      <Paginator v-if="dataList.data.length && selectedProjects.length===0" class="border-t" :data="dataList.pagination" :per="paginationPer" :sPer="10" @page="searchPage"></Paginator>
      <div v-else-if="dataList.data.length && selectedProjects.length>0" class="border-t px-6 py-4 bg-gray-50 flex items-center justify-end">
        <span class="custom-span-for-selected">已選取{{selectedProjects.length}}個</span>
        <Button label="取消選取" text @click="selectAllProjects(false)"></Button>
      </div>
      <DataStatus v-else :data="dataList.data" :searched="searched"></DataStatus>
    </div>
  </div>
  <Detail v-if="action" :mode="mode" :edit="true" :employeeList="employeeList" :detailData="detailData">
    <template #actionButton="actionButtonProps">
      <li>
        <ConfirmPopup style="width: 244px" class="custom-confirm-popup">
          <template #message="slotProps">
            <div class="p-6 pb-8">
              <p class="pl-2">{{ slotProps.message.message }}</p>
            </div>
          </template>
        </ConfirmPopup>
        <Button label="確認核定" style="padding: 10px 18px;" @click="actionButtonProps.confirm($event)" ></Button>
      </li>
    </template>
  </Detail>
  <Toast position="top-center" />
</template>

<script>
import ConfirmPopup from 'primevue/confirmpopup';
import DataStatus from '../../common/DataStatus.vue';
import Paginator from '../../common/Paginator.vue';
import ScrollPanel from 'primevue/scrollpanel';
import Detail from './Detail.vue';

export default {
  components: {
    ConfirmPopup,
    DataStatus,
    Paginator,
    ScrollPanel,
    Detail,
  },  
  props: {
    mode: {
      required: true
    },
    subsidiary: {
      required: true
    },
    employeeList: {
      required: true
    }
  },
  data() {
    return {
      // default
      apiURL: "/api/acc/perf-bonus",
      dataLoaded: false,
      searched: 0,
      paginationPer: [
                {name: '10 　 筆', value: 10},
                {name: '30 　 筆', value: 30},
                {name: '60 　 筆', value: 60},
                {name: '100　筆', value: 100},
      ],
      action: 0,
      detailData: null,
      // mounted fetch
      dataList: [],
      // input 
      projectId: null,
      expandRowIndex: [],
      selectAll: false,
      selectedProjects: [],
    };
  },
  async mounted() {
    if(this.mode==='submit') {
      await this.fetchSubmitList()
    } else {
      await this.fetchReviewList()
    }
    this.searched = 0
    
    if(document.querySelector('.p-scrollpanel-content')) {
      document.querySelector('.p-scrollpanel-content').addEventListener('scroll', () => {
        if(document.querySelector('.scroll-header').scrollLeft != document.querySelector('.p-scrollpanel-content').scrollLeft) {
          document.querySelector('.scroll-header').scrollLeft = document.querySelector('.p-scrollpanel-content').scrollLeft
        }
      })
    }
  },
  methods: {
    async fetchSubmitList() {
      await axios
        .get(this.apiURL + `/f/processing/${this.subsidiary}`, {params: {epjno: this.projectId}})
        .then(res => {
          this.dataList = res.data
          this.selectedProjects = []
          this.dataLoaded = true
          this.searched = 1
        })
        .catch(err => {
          this.$toast.add({ 
              severity: 'error', 
              summary: 'Error Message',
              detail: '開啟失敗',
              life: 3000
            });
          console.warn(err)
        })
    },
    async fetchReviewList() {
      await axios
      .get(this.apiURL + `/s/review`, {
        params: {
          projectId: this.projectId,
          perPage: 10,
          page: 1,
        }
      })
      .then(res => {
        this.dataList = res.data
        this.dataLoaded = true
        this.searched = 1
      })
      .catch(err => {
        this.$toast.add({ 
          severity: 'error', 
          summary: 'Error Message',
          detail: '開啟失敗',
          life: 3000
        });
      })
    },
    search() {
      if(this.mode==='submit') {
        this.fetchSubmitList()
      } else {
        this.fetchReviewList()
      }
    },
    openReviewProject(id) {
        this.action = 1
        axios
          .get(this.apiURL + '/s/review/' + id)
          .then(res => {
            this.detailData = res.data.data[0]
          })
          .catch(err => {
            this.$toast.add({ 
              severity: 'error', 
              summary: 'Error Message',
              detail: '開啟失敗',
              life: 3000
            });
            this.detailData = {}
          })
      },
    formatValue(int) {
      return Number.parseFloat(int).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    },
    expandRow(index) {
      if(this.expandRowIndex.includes(index)) {
        this.expandRowIndex = this.expandRowIndex.filter(el => el !== index)
      } else {
        this.expandRowIndex.push(index)
      }
    },
    selectAllProjects(e) {
      if(e.target?.checked) {
        this.selectedProjects = this.dataList.data.map(el => {
          el.selected = true;
          return el.epjno
        })
      } else {
        if(this.selectAll) { // 如果從"全選"開，"取消選取"關閉時也要把"全選"取消打勾
          this.selectAll = false
        }
        this.dataList.data.forEach(element => {
          element.selected = false;
        })
        this.selectedProjects = []
      }
    },
    selectProject(e, row) {
      if(e.target.checked) {
        const {epjno} = row
        this.selectedProjects.push(epjno)
      } else {
        this.selectedProjects = this.selectedProjects.filter(el => el != row.epjno)
      }
    },
    verticalAlignTable() {
      if(document.querySelector('.scroll-header').scrollLeft != document.querySelector('.p-scrollpanel-content').scrollLeft) {
        document.querySelector('.p-scrollpanel-content').scrollLeft = document.querySelector('.scroll-header').scrollLeft
      }
    },
    searchPage(params) {
      this.searchVariables.perPage = params.per
      this.searchVariables.page = params.page
      this.search()
    },
    confirm(e) {
      this.$confirm.require({
        target: e.currentTarget,
        message: '是否確定送出核定?',
        acceptLabel: "確認",
        rejectLabel: "取消",
        position: "right",
        accept: () => {
            this.submit()
        },
      });
    },
    submit() {
      axios
        .delete(this.apiURL+ `/f/processing/${this.subsidiary}/${this.selectedProjects.join(',')}`)
        .then(res => {
          this.$toast.add({ 
            severity: 'success', 
            summary: 'success Message',
            detail: '送出成功',
            life: 3000
          });
  
          this.fetchSubmitList()
          this.selectedProjects = []
        })
        .catch(err => {
          this.$toast.add({ 
            severity: 'error', 
            summary: 'Error Message',
            detail: '送出失敗',
            life: 3000
          });
        })
    }
  },
  watch: {
    dataList: {
      deep: true,
      handler() {
        if(this.dataList.data.length) {
          if(this.dataList.data.every(el => el.selected)) {
            this.selectAll = true
          } else {
            this.selectAll = false
          }
        } else {
          this.selectAll = false
        }
      }
    },
  }
};
</script>

<style scoped>
.scroll-header::-webkit-scrollbar {
  width: 0px;
  height: 0px
}
.custom-height {
  height: calc(100vh - 160px);
  min-height: 720px
}
@media (min-width: 768px) {
  .custom-height {
    height: calc(100% - 80px);
  }
}
::v-deep(.p-scrollpanel.custom-bar .p-scrollpanel-bar) {
    background-color: rgb(100 116 139);
    border-radius: 5px;
}
.custom-button {
  padding: 8px 20px;
}
.custom-span-for-selected {
  transform: translateY(1px);
  border: 1.25px solid transparent;
  padding: 12px 20px 12px 20px;
}
</style>