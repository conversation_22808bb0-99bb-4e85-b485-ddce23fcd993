# Mailpit -> just make like Mailtrap api

## Mailtrap API [Link](https://mailtrap.docs.apiary.io/#reference/shared-inbox/get)

- 取列表 (GET) `api/mailtrap/api/v1/inboxes/${bucket_name}/messages`
- 取得信 (GET) `api/mailtrap/api/v1/inboxes/${bucket_name}/messages/${mail_id}`
- 刪除信 (DELETE) `api/mailtrap/api/v1/inboxes/${bucket_name}/messages/${mail_id}`
- 更新信 (PATCH) `api/mailtrap/api/v1/inboxes/${bucket_name}/messages/${mail_id}`

## 使用說明:

> 放在對應 ap 的 public 底下，透過 php 猜測目標 ap，並且將 tag 帶上做搜索。 <br/>
> ```shell
> git subtree add --prefix public/mailpit ssh://*******************/fdmc/smtp-ap-getter.git 28bec9abdf6ed19dd3f41f36f588d0c889619f8a
> ```

## local 使用:
- 將 module.php 中， `if (strpos($my_path, '/mnt') === false) {` 這個條件下的 `['_', 'FDMC-ASAP-DEV']` 的第二個值改成自己想要搜尋的 tag。

## RELEASE API

- (GET) {ap_domain}/{project_dir_name}/release.php/inboxes/messages

- (GET) {ap_domain}/{project_dir_name}/release.php/inboxes/messages/EbxjYMjE62awPQjcGVMgd7
- (GET) {ap_domain}/{project_dir_name}/release.php/inboxes/messages/EbxjYMjE62awPQjcGVMgd7.raw
- (GET) {ap_domain}/{project_dir_name}/release.php/inboxes/messages/EbxjYMjE62awPQjcGVMgd7.html
- (GET) {ap_domain}/{project_dir_name}/release.php/inboxes/messages/EbxjYMjE62awPQjcGVMgd7.txt

- (DELETE) {ap_domain}/{project_dir_name}/release.php/inboxes/messages/EbxjYMjE62awPQjcGVMgd7
- (PATCH) {ap_domain}/{project_dir_name}/release.php/inboxes/messages/EbxjYMjE62awPQjcGVMgd7
