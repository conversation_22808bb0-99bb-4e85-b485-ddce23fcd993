<?php

namespace App\Modules\PerfBonus\Console\Commands;

use App\Modules\acc\Services\NotificationService;
use App\Modules\PerfBonus\Models\PerfEmailNotifyLog;
use App\Modules\PerfBonus\Services\EmployeeService;
use App\Modules\PerfBonus\Services\PerfBonusService;
use App\Notifications\PerfUnDealNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class PerfUnDealToEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'perf:perf-un-deal-to-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '未審核的人員通知命令，發信件通知審核人員審核.(由系統設定是否發信及發信頻率、內容)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('perf bonus generate notification to reviewer');

        /** @var PerfBonusService $perfBonusService */
        $perfBonusService = app(PerfBonusService::class);

        /** @var NotificationService $notificationService */
        $notificationService = app(NotificationService::class);

        /** @var EmployeeService $employeeService */
        $employeeService = app(EmployeeService::class);


        $perfNotificationSettingArr = $notificationService->getPerfNotificationSetting();
        foreach (['RHT' => 1, 'RHY' => 3] as $company => $companyId) {

            $this->info("company: {$company}");
            $setting = $perfNotificationSettingArr[$companyId];
            $title = $setting['title'] ?? '績效評核尚未完成';
            $text = $setting['text'] ?? '';
            $frequencyDay = intval($setting['frequencyDay']) <= 0 ? 1 : intval($setting['frequencyDay']);
            $status = intval($setting['status'] ?? null);

            // 關閉通知則不處理這間公司的通知
            if ($status === 0) {
                continue;
            }

            $notReviews = DB::table('perf_dep_reviews')
                ->where(function ($query) {
                    // 無初核人員
                    $query->whereNull('deleted_at')
                        ->whereNull('first_empno')
                        ->whereNull('second_finished_at');
                    // 有初核人員
                    $query->orWhereNull('deleted_at')
                        ->whereNotNull('first_empno')
                        ->whereNull('first_finished_at');
                    // 有初核人員，但是初核人員已經完成
                    $query->orWhereNull('deleted_at')
                        ->whereNotNull('first_empno')
                        ->whereNotNull('first_finished_at')
                        ->whereNull('second_finished_at');

                })
                ->get(['prj_id', 'first_empno', 'second_empno', 'first_finished_at', 'second_finished_at'])
                ->transform(function ($it) {
                    $it->notifyEmpno = match (true) {
                        // 無初核人員
                        empty($it->first_empno) && empty($it->second_finished_at) => $it->second_empno,
                        // 有初核人員，但是初核人員尚未完成
                        !empty($it->first_empno) && empty($it->first_finished_at) => $it->first_empno,
                        // 有初核人員，但是初核人員已經完成
                        !empty($it->first_empno) && !empty($it->first_finished_at) && empty($it->second_finished_at) => $it->second_empno,
                        default => null,
                    };
                    return $it;
                })
                ->filter(function ($it) {
                    return !empty($it->notifyEmpno);
                });

            // 審核人發信件
            $notReviews = $notReviews->groupBy('notifyEmpno');
            $notReviews->each(function ($itNotifyProjects, $itEmpno) use ($company, $title, $text, $frequencyDay) {
                /** @var EmployeeService $employeeService */
                $employeeService = app(EmployeeService::class);
                $employee = $employeeService->getEmployeeByEmpno($itEmpno);

                $notifyCount = $itNotifyProjects->count();
                $personalText = str_replace([':{x}', ':{name}'], [$notifyCount, $employee->name], $text);
                $employee->email = '<EMAIL>';

                $hasPass = PerfEmailNotifyLog::where('email', $employee->email)
                    ->whereBetween('send_date', [now()->subDays($frequencyDay)->toDateString(), now()->toDateString()])
                    ->first();

                if ($hasPass) {
                    return ;
                }
                $employee->notify(new PerfUnDealNotification(
                    name: $employee->name,
                    company: $company,
                    title: $title,
                    text: $personalText,
                ));
                PerfEmailNotifyLog::create([
                    'email' => $employee->email,
                    'send_date' => now()->toDateString(),
                ]);
            });
        }
    }
}
