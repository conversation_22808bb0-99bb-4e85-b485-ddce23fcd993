<template>
  <Banner :names="['用印申請']" />
  <div class="m-6">
    <div class="p-6 flex flex-col flex-wrap w-full bg-white rounded-xl shadow">
      <label class="mb-4 text-xl">查詢結果</label>
      <MultiSelect
        v-model="selectedColumns"
        :options="columns"
        optionLabel="name"
        filter 
        placeholder="選擇欄位"
        class="w-64"
        :resetFilterOnHide="true"
      />
      <hr class="my-8"/>
      <div class="flex flex-col gap-6">
        <label class="text-xl">查詢條件</label>
        <!-- criteria -->
        <div class="flex flex-wrap gap-6">
          <div>
            <label class="my-auto">表單類型: </label>
            <MultiSelect
              v-model="selectedSealTypeColumns"
              :options="sealTypeColumns"
              optionLabel="name"
              optionValue="layout_id"
              placeholder="選擇表單"
              class="md:w-64"
            />
          </div>
          <div>
            <label class="my-auto">申請日期區間: </label>
            <Calendar
              v-model="dateRange"
              selectionMode="range"
              placeholder="請選擇年月"
            />
          </div>
          <div>
            <label class="my-auto">需求單號: </label>
            <InputText type="text" v-model="no" placeholder="請輸入需求單號" />
          </div>
          <div>
            <label class="my-auto">申請人: </label>
            <Dropdown
              filter
              type="text"
              v-model="employee"
              optionLabel="name"
              optionValue="id"
              :options="employees"
              placeholder="請選擇"
              :resetFilterOnHide="true"
            />
          </div>
          <Button
            type="button"
            label="清除"
            severity="secondary" text raised
            @click="clearCriteria()"
          />
        </div>
        <!-- additional criteria -->
        <div v-for="(selector, index) of selectors" class="flex gap-6">
          <Dropdown
            v-model="selectors[index]['name']"
            :options="additionalColumns"
            :optionDisabled="disableOptions(index)"
            optionLabel="name"
            optionValue="name"
            @change="setSelector($event, index)"
            placeholder="選擇欄位"
            filter
            :resetFilterOnHide="true"
          />
          <Calendar
            v-if="selector.type == 'calendar'"
            v-model="selector.value"
            placeholder="請選擇年月"
          />
          <InputText
            v-else
            type="text"
            v-model="selector.value"
            placeholder="請輸入"
          />
          <font-awesome-icon
            class="pt-4"
            :icon="['fas', 'trash']"
            @click="deleteSelector(index)"
          />
        </div>
        <!-- buttons -->
        <div class="w-full flex gap-6 justify-end">
          <Button
            type="button"
            label="新增條件"
            severity="secondary" text raised
            @click="addSelector()"
          />
          <Button
            type="button"
            label="查詢"
            icon="pi pi-search"
            :loading="loading"
            @click="fetch()"
          />
        </div>
      </div>
    </div>
    <div
      class="p-6 my-6 bg-white rounded-xl shadow whitespace-nowrap overflow-x-auto flex justify-center items-center"
      style='min-height : calc(100vh - 536px)'
    >
      <div class="flex flex-col gap-5" v-if="loading">
        <img class="rotating" src="../../images//page_state/loading_circle.svg" alt="search"/>
        <span class="font-bold">資料載入中...</span>
      </div>
      <div class="text-center" v-else-if="result.length == 0">
        <img
          src="../../images/page_state/search.svg"
          alt="輸入您要查詢的資料"
        />
      </div>
      <div v-else-if="result.data?.length == 0">
        <img src="../../images/page_state/search_empty.svg" alt="" />
      </div>
      <div class="w-full rounded-lg overflow-hidden" v-else>
        <DataTable
          v-model:selection="selectedValue"
          :value="result.data"
          selectionMode="multiple"
          :metaKeySelection="false"
          scrollable
          scrollHeight="600px"
          stripedRows
        >
          <template #header>
            <div
              class="flex flex-wrap items-center justify-between gap-2"
            >
              <p class="text-xl text-900 font-bold">用印申請</p>
              <div>
                <Button
                  :disabled="selectedValue.length == 0"
                  @click="exportLists"
                  :loading="loading"
                  label="匯出"
                />
                &ensp;
              </div>
            </div>
          </template>
          <Column
            frozen
            selectionMode="multiple"
            headerStyle="width: 3rem"
          >
          </Column>
          <Column
            v-for="col of selectedColumns"
            :header="col.name"
            :field="col.id"
            :key="col.id"
          >
            <template #body="slotProps">
              <div v-if="col.index=='附件'" class="max-w-md w-max whitespace-normal hover:underline" v-html="slotProps.data[col.index]"></div>
              <div v-else class="max-w-md w-max whitespace-normal">{{ slotProps.data[col.index] }}</div>
            </template>
          </Column>
        </DataTable>
        <Paginator :data="result.paginate" :sPer="per" @page="fetchPage"/>
      </div>
    </div>
  </div>
  <Sidebar
    v-model:visible="visibleBottom"
    position="bottom"
    :baseZIndex="1000"
    :dismissable="false"
    :modal="false"
    :showCloseIcon="false"
    class="p-sidebar-xs"
  >
    <div class="flex justify-end w-full">
      <p class="text-sm my-auto">已選取{{ selectedValue.length }}個</p>
      <p
        @click="
          selectedValue = [];
          visibleBottom = false;
        "
        class="mx-12 text-sm text-blue-600 underline my-auto cursor-pointer"
      >
        取消選取
      </p>
    </div>
  </Sidebar>
</template>
<script>
import Banner from "../common/Banner.vue";
import Paginator from "../common/Paginator.vue";
import Button from 'primevue/button';
import Calendar from 'primevue/calendar';
import Column from 'primevue/column';
import DataTable from 'primevue/datatable';
import Dropdown from 'primevue/dropdown';
import InputText from 'primevue/inputtext';
import MultiSelect from 'primevue/multiselect';
import Sidebar from "primevue/sidebar";

export default {
  components: {
    Banner,
    Paginator,
    Button,
    Calendar,
    Column,
    DataTable,
    Dropdown,
    InputText,
    MultiSelect,
    Sidebar,
  },
  data: function () {
    return {
      loading: false,
      dateRange: [],
      visibleBottom:false,
      page: 1,
      per: 50,
      columns: [
        { name: '需求名稱', index: 'layout_name', type: 'input' },
        { name: '用印單號', index: 'no', type: 'input' },
        { name: '申請日', index: 'created_at', type: 'input' },
        { name: '需求日', index: '需求日期', type: 'input' },
        { name: '會計代號', index: '會計代號', type: 'input' },
        { name: '專案代號', index: '專案代號', type: 'input' },
        { name: '專案簡稱', index: '專案名稱', type: 'input' },
        { name: '用途', index: '用途', type: 'input' },
        { name: '申請人', index: 'applicant_name', type: 'input' },
        { name: '公司(用印單位)', index: '公司(用印單位)', type: 'input' },
        { name: '簽章種類', index: '簽章種類', type: 'input' },
        { name: '部門', index: '部門', type: 'input' },
        { name: '專技人員', index: '專技人員', type: 'input' },
        { name: '防火管理人/勞安人員', index: '防火管理人/勞安人員', type: 'input' },
        { name: '政府-室裝文件', index: '政府-室裝文件', type: 'input' },
        { name: '需求文件', index: '需求文件', type: 'input' },
        { name: '備註', index: '備註', type: 'input' },
        { name: 'PM簽核', index: 'PM', type: 'input' },
        { name: 'PD簽核', index: 'PD', type: 'input' },
        { name: '總管理處簽核', index: '總管理處', type: 'input' },
        { name: '會簽人員簽核', index: '會簽人員', type: 'input' },
        { name: '財務初審', index: '財務初審', type: 'input' },
        { name: '財務覆核', index: '財務覆核', type: 'input' },
        { name: '財務核決', index: '財務核決', type: 'input' },
        { name: '財務用印', index: '財務用印', type: 'input' },
        { name: '財務初審/用印', index: '財務初審/用印', type: 'input' },
        { name: '財務用印時間', index: 'last_time', type: 'input' },
        { name: '附件', index: '附件', type: 'input' },
      ],
      additionalColumns: [
        { name: '需求日', value: '', type: 'calendar' },
        { name: '會計代號', value: '', type: 'input' },
        { name: '專案代號', value: '', type: 'input' },
        { name: '專案簡稱', value: '', type: 'input' },
        { name: '用途', value: '', type: 'input' },
        { name: '公司(用印單位)', value: '', type: 'input' },
        { name: '簽章種類', value: '', type: 'input' },
        { name: '部門', value: '', type: 'input' },
        { name: '專技人員', value: '', type: 'input' },
        { name: '防火管理人/勞安人員', value: '', type: 'input' },
        { name: '政府-室裝文件', value: '', type: 'input' },
        { name: '需求文件', value: '', type: 'input' },
        { name: '備註', value: '', type: 'input' },
        { name: 'PM簽核', value: '', type: 'input' },
        { name: 'PD簽核', value: '', type: 'input' },
        { name: '總管理處簽核', value: '', type: 'input' },
        { name: '會簽人員簽核', value: '', type: 'input' },
        { name: '財務初審', value: '', type: 'input' },
        { name: '財務覆核', value: '', type: 'input' },
        { name: '財務核決', value: '', type: 'input' },
        { name: '財務用印', value: '', type: 'input' },
        { name: '財務初審/用印', value: '', type: 'input' },
        { name: '財務用印時間', value: '', type: 'calendar' },
      ],
      sealTypeColumns: [],
      employees: [],
      selectedColumns: [],
      selectedSealTypeColumns: [],
      selectedValue: [],
      whereSelector: [],
      selectors: [],
      no: null,
      employee: null,
      result: [],
      currentTab: 0,
    };
  },
  mounted() {
    this.fetchSealTypeColumns();
    this.fetchEmployees();
    let previousSelectedColumns = window.localStorage.getItem('selectedColumns')
    if(previousSelectedColumns) {
      this.selectedColumns = JSON.parse(previousSelectedColumns)
    } else {
      this.selectedColumns = this.columns
    }
  },
  watch:{
    selectedValue(newArray,_){
        if (newArray.length > 0)
            this.visibleBottom = true;
        else
            this.visibleBottom = false;
    },
    selectedColumns(n,_) {
      window.localStorage.setItem('selectedColumns', JSON.stringify(n))
    }
  },
  methods: {
    fetchEmployees() {
      axios
        .get("/api/acc/common/employees")
        .then((response) => {
          let data = response.data ?? [];
          this.employees = data;
        })
        .catch((error) => {
          console.error(error);
        })
    },
    fetchSealTypeColumns() {
      axios
        .get("/api/acc/operation/layouts")
        .then((response) => {
          this.sealTypeColumns = response.data.data;
        })
        .catch((error) => {
          console.error(error);
        })
    },
    clearCriteria() {
      this.dateRange = []
      this.no = null
      this.employee = ""
      this.selectedSealTypeColumns = []
      this.page = 1
      this.per = 50
      this.selectors = []
    },
    fetchPage(params) {
      this.page = params.page
      this.per = params.per
      this.fetch()
    },
    fetch() {
      this.selectedValue = []
      this.loading = true;
      axios
        .get("/api/acc/operation/layouts/data", {
          params: {
            layoutIds: this.selectedSealTypeColumns,
            start: this.dateRange[0],
            end: this.dateRange[1],
            no: this.no,
            employee: this.employee,
            extraSearch: JSON.stringify(this.extraSearch),
            page: this.page,
            per_page: this.per,
          },
        })
        .then((response) => {
          this.result = response.data.data
          this.formatResponse()
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    formatResponse() {
      this.result.data = this.result.data.map(el => {
        let transformedValues = {}
        for (const key in el.values) {
          const item = el.values[key];
          transformedValues[item.name] = item.value;
        }
        let transformedSignRoles = {}
        for (const key in el.sign_roles) {
          const item = el.sign_roles[key];
          transformedSignRoles[item.self_name] = item.role_name;
        }

        this.result.paginate['current_page'] = this.page

        return {...el, ...transformedValues, ...transformedSignRoles}
      })
    },
    addSelector() {
      this.selectors.push({name: undefined, value: "", type: 'input'});
    },
    setSelector(e, index){
      this.selectors[index].type = this.additionalColumns.find(el => el.name == e.value).type
    },
    deleteSelector(index) {
      this.selectors = this.selectors.filter((_,i) => !(i == index));
    },
    disableOptions(index){
      return (opt) => {
        return this.selectors.filter((_,i) => !(i==index)).map(el=>el.name).includes(opt.name)
      }
    },
    exportLists() {
      axios({
        url: '/api/acc/operation/layouts/export',
        method: "GET",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        },
        params: {
          no: this.selectedValue.map(el => el.no),
          showColumns: this.selectedColumns.map(el => el.name),
        },
        responseType: "blob",
      })
      .then((response) => {
        let blob = new Blob([response.data], {
          type: "application/vnd.ms-excel;charset=utf-8",
        });

        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
        const day = currentDate.getDate().toString().padStart(2, '0');
        const hours = currentDate.getHours().toString().padStart(2, '0');
        const minutes = currentDate.getMinutes().toString().padStart(2, '0');
        const seconds = currentDate.getSeconds().toString().padStart(2, '0');

        const filename = `${year}${month}${day}${hours}${minutes}${seconds}.xlsx`;
        
        const url = window.URL.createObjectURL(blob);
        const aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", filename);
        document.body.appendChild(aLink);
        aLink.click();
        setTimeout(function () {
          window.URL.revokeObjectURL(url);
        }, 100);
      })
      .catch((error) => {
        console.log(error);
      });
    },
  },
  computed: {
    extraSearch() {
      let extraSearch = {}
      for (const key in this.selectors) {
        const item = this.selectors[key];
        extraSearch[item.name] = item.value;
      }
      return extraSearch
      // let extraSearch = []
      // for (const key in this.selectors) {
      //   const item = this.selectors[key];
      //   extraSearch.push({[item.name]: item.value})
      // }
      // return extraSearch
    },
  }
};
</script>

<style scoped>
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>