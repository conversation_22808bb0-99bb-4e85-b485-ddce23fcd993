<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\Session;

trait FormatDate
{
    // protected $company_id;
    // protected $user_id;
    protected $timezone;

    public function __construct()
    {

    }

    public function dateToISO($date)
    {
        return Carbon::parse($date)->toISOString();
    }

    public function formatDate($date, $type)
    {
        if(!isset($date) || empty($date)) return null;
        $date = Carbon::parse($date)->setTimezone($this->timezone);

        switch ($type) {
            case 'ym':
                $date = $date->format('Ym');
                break;
            case 'date_time':
                $date = $date->format('Y/m/d H:i');
                break;
            case 'date':
                $date = $date->format('Y/m/d');
                break;
            case 'time':
                $date = $date->format('H:i');
                break;
            default:
                $date = $date->format('Y/m/d');
                break;
        }
        return $date;
    }
    /**
     *
     *@param $type string date|date_time
     *@param $codes collection Code::where('code_kind', 'AC')->get()
     */
    public function formatDateWeek($date, $type, $codes)
    {
        $date = Carbon::parse($date)->setTimezone($this->timezone);
        $day = $date->format('Y/m/d');
        $w = 'W' . Carbon::parse($date)->dayOfWeek;
        $codes = $codes->where('code_id', $w)->first()->nm_zh_tw;

        if ($type == 'date_time') {
            $time = $date->format('H:i');
            $result = $day . "(" .  $codes . ")" . $time;
        } else if ($type == 'date')
            $result = sprintf($day . "(%s)", $codes);

        return $result;
    }

    /**
     *@param  $codes collection Code::all();
     *@param $status int
     */
    public function signTimestamp($code, $code_id)
    {
        $stamp =  $this->formatDateWeek(now(), 'date_time', $code->where('code_kind', 'AC'));

        return $stamp . ($code_id == 10 ? ' 已退回至申請人' : ' 已簽核');
    }
}
