<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('perf_dep_reviews', function (Blueprint $table) {
            $table->id();
            $table->string('code', 10)->index()->comment('部門代碼');
            $table->unsignedBigInteger('prj_id')->index()->comment('關聯 perf_erp_projects.id');
            $table->decimal('amount', 12, 2)->comment('實際績效獎金的金額');
            $table->decimal('suggestion_amount', 12, 2)->comment('推薦的績效獎金額，通常為實際金額的七成');
            $table->enum('status', ['pending', 'finished', 'skip'])->default('pending')->comment('是否已完成');
            $table->string('first_empno', 12)->nullable()->comment('第一階段評核人員');
            $table->datetime('first_finished_at')->nullable()->comment('初核人員的填寫完成日期');
            $table->string('second_empno', 12)->nullable()->comment('第二階段評核人員(通常為部門主管)');
            $table->datetime('second_finished_at')->nullable()->comment('覆核人員的填寫完成日期');
            $table->string('common_type')->default('P')->comment('三轉二評分: 看審核是否是 PM 還是 CPM 要評，總共有三種腳色，如同工程人員類型');
            $table->softDeletes();
            $table->timestamps();
        });


        Schema::create('perf_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('review_id')->index()->comment('關聯 perf_erp_projects.id');
            $table->string('sno', 5)->comment('erp 中記錄的相關 sno 資訊');
            $table->string('empno', 12)->index()->comment('員工編號');
            $table->string('empna')->comment('員工姓名');
            $table->string('show_title')->default('')->comment('職稱顯示');
            $table->boolean('is_new')->default(false)->comment('原本無紀錄在專案內的人員');
            $table->string('append_by', 12)->nullable()->comment('原本無紀錄在專案內的人員 - 透過誰新增的');
            // 初核人員的相關資料
            $table->jsonb('first_reviewer_payload')->nullable()->comment('初核人員的填寫相關資料');
            // 覆核人員的相關資料
            $table->jsonb('finally_reviewer_payload')->nullable()->comment('覆核人員的填寫相關資料');
            $table->unsignedSmallInteger('member_type')->nullable()->comment('工程人員類型 -> 0:工程人員 1:設計人員 2:實施設計人員');
            $table->jsonb('question_extra')->nullable()->comment('RH CCSS 額外資訊');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('perf_dep_reviews');
        Schema::dropIfExists('perf_logs');
    }
};
