<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pj_pay_details', function (Blueprint $table) {
            $table->bigInteger('unkey')->comment('erp_pay_detail_unique_key');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pj_pay_details', function (Blueprint $table) {
            $table->dropColumn('unkey');
        });
    }
};
