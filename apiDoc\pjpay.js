
付款列表
/**
 *
 * @api {get} /acc/pjpay/list
 * @apiName getPayList
 *
 * @apiParam  {String, required} startYm - 開始年月
 * @apiParam  {String, required} endYm - 結束年月
 * @apiParam  {String, required} pbatchno 批次
 * @apiParam  {Int, required} page 頁數
 * @apiParam  {Int, required} per 筆數
 * @apiParam  {String} epjacc 會計代號
 * @apiParam  {Bool} forceFetch 強制撈取
 *
 *
 * @apiParamExample  {type} Request-Example:
 * {
 *      startYm : 202303,
 *      endYm : 202305,
 *      pbatchno : 0,
 *      epjacc : 2021023,
 *      forceFetch : true,
 * page : 1,
 * per : 50,
 * }
 *
 *
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     property : value
 *      data (array):
 *          (object)
 *              id
                epjacc - 會計代號
                epjno - 專案代號
                epjna - 專案簡稱
                pdname - PD姓名
                pmname - PM姓名
                pcname - CPM姓名
                pleper - 請款%
                venpayper - 付款%
                difamt - 收付差額
                sdate - 開工日期
                odate - 完工日期
                atypena - 專案現況
                qupamtc - 合約報價
                qupamtad - 追加減報價
                qupamt - 總報價款
                tqurece -  已收款
                tqurecn - 已請未收
                budamt - 預算成本
                vencamt - 總發包款
                venper - 發包%
                tvpay - 發包已付
                nacpay - 請款未付
                venpay - 本期請款
 *      current_page:1
 *      per_page:50
 *      total:5
 *      lastPage:5
 * }
 *
 * ---------------------------------------------------------------------------
 */

付款明細
/**
 *
 * @api {get} /acc/pjpay/detail_list
 * @apiName getPayDetailList
 *
 * @apiParam  {String, required} startYm - 開始年月
 * @apiParam  {String, required} endYm - 結束年月
 * @apiParam  {String, required} pbatchno 批次
 * @apiParam  {String, required} epjacc 會計代號
 * @apiParam  {String, required} epjno 專案代號
 *
 *
 * @apiParamExample  {type} Request-Example:
 * {
 *      startYm : 202303,
 *      endYm : 202305,
 *      pbatchno : 0,
 *      epjacc : S98011B,
 *      epjno : S98014B,
 * }
 *
 *
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     data (array):
 *          (object)
 *              id
                symno - 年月
                pbatchno - 批次
                epjacc - 會計代號
                epjno - 專案代號
                venno - 廠商代號
                venna - 廠商簡稱
                payrmk - 付款摘要
                vencamt - 發包總價
                tvenpay - 發包已付
                balance - 發包結餘
                npay - 本期請款
                ppay - 本期預付
                hpay - 本期保留
                dpay - 本期折讓
                venpay - 本期實付
                tax - 稅金
 * }
 *
 *---------------------------------------------------------------------------
 */

送出付款
/**
 *
 * @api {post} /acc/pjpay/submit-pay
 * @apiName submitPay
 *
 * @apiParam  {String, required} startYm - 開始年月
 * @apiParam  {String, required} endYm - 結束年月
 * @apiParam  {String, required} pbatchno 批次
*
*
* @apiParam  {Array, required} projects - 專案
*              (object):
                    {Int, required} id - pay 的id
                    {Int, required} pkey
                    {String, required} epjacc - 會計代號
                    {String, required} epjno - 專案代號
                    {String, required} epjna - 專案簡稱
                    {String, required} pdname - PD姓名
                    {String, required} pmname - PM姓名
                    {String, required} pcname - CPM姓名
                    {Int, required} pleper - 請款%
                    {Int, required} venpayper - 付款%
                    {Int, required} difamt - 收付差額
                    {String, required} sdate - 開工日期
                    {String, required} odate - 完工日期
                    {String, required} atypena - 專案現況
                    {Int, required} qupamtc - 合約報價
                    {Int, required} qupamtad - 追加減報價
                    {Int, required} qupamt - 總報價款
                    {Int, required} tqurece -  已收款
                    {Int, required} tqurecn - 已請未收
                    {Int, required} budamt - 預算成本
                    {Int, required} vencamt - 總發包款
                    {Int, required} venper - 發包%
                    {Int, required} tvpay - 發包已付
                    {Int, required} nacpay - 請款未付
                    {Int, required} venpay - 本期請款

 * @apiParamExample  {type} Request-Example:
 * {
 *      startYm : 202303,
 *      endYm : 202305,
 *      pbatchno : 0,
 *      pay_id : 12,
 *      pkey : S98011B_S98014B,
 *      epjacc : S98011B,
 *      epjno : S98014B,
 *      epjna : 亞CC1F展咖
        pdname : 王大明
        pmname :王大明
        pcname :王大明
        pleper :12.24
        venpayper :12.24
        difamt : 12345
        sdate : 20111027
        odate : 無完工日
        atypena :會結案
        qupamtc :2924773
        qupamtad :0
        qupamt :48993271
        tqurece :0
        tqurecn :0
        budamt :0
        vencamt :0
        venper :102.33
        tvpay : 0
        nacpay :0
        venpay :0
 * }
 *
 *
 * @apiSuccessExample {int} Success-Response:
 * {
 *     1
 * }
 * @apiErrorExample {int} Error-Response:
 * {
 *      0
 * }
 *---------------------------------------------------------------------------
 */


送出付款明細
/**
 *
 * @api {post} /acc/pjpay/submit-detail
 * @apiName submitDetail
 * @apiParam {String, required} startYm - 開始年月
 * @apiParam {String, required} endYm - 結束年月
 * @apiParam {String, required} pbatchno 批次
 * @apiParam  {object, required} pjInfo
 *               {Int, required} pkey
 *               {String, required} epjacc - 會計代號
 *               {String, required} epjno - 專案代號
 *               {String, required} epjna - 專案簡稱
 *               {String, required} pdname - PD姓名
 *               {String, required} pmname - PM姓名
 *               {String, required} pcname - CPM姓名
 *               {Int, required} pleper - 請款%
 *               {Int, required} venpayper - 付款%
 *               {Int, required} difamt - 收付差額
 *               {String, required} sdate - 開工日期
 *               {String, required} odate - 完工日期
 *               {String, required} atypena - 專案現況
 *               {Int, required} qupamtc - 合約報價
 *               {Int, required} qupamtad - 追加減報價
 *               {Int, required} qupamt - 總報價款
 *               {Int, required} tqurece -  已收款
 *               {Int, required} tqurecn - 已請未收
 *               {Int, required} budamt - 預算成本
 *               {Int, required} vencamt - 總發包款
 *               {Int, required} venper - 發包%
 *               {Int, required} tvpay - 發包已付
 *               {Int, required} nacpay - 請款未付
 *               {Int, required} venpay - 本期請款
 *
 * @apiParam  {Array, required} details - 明細
 *              c
 *                  {Int, required} id
 *                  {Int, required} pkey
                    {String, required}  epjacc - 會計代號
                    {String, required}  epjno - 專案代號
                    {String, required}  venno - 廠商代號
                    {String, required}  venna - 廠商簡稱
                    {String, required}  payrmk - 付款摘要
                    {Int, required}  vencamt - 發包總價
                    {Int, required}  tvenpay - 發包已付
                    {Int, required}  balance - 發包結餘
                    {Int, required}  npay - 本期請款
                    {Int, required}  ppay - 本期預付
                    {Int, required}  hpay - 本期保留
                    {Int, required}  dpay - 本期折讓
                    {Int, required}  venpay - 本期實付
                    {Int, required}  tax - 稅金
 *
 * @apiParamExample  {type} Request-Example:
 * {
 *      startYm : 202303,
 *      endYm : 202305,
 *      pbatch : 0,
 *      pjInfo:
 *         (object):
        *      pay_id : 12,
        *      epjacc : S98011B,
        *      epjno : S98014B,
        *      epjna : 亞CC1F展咖
                pdname : 王大明
                pmname :王大明
                pcname :王大明
                pleper :12.24
                venpayper :12.24
                difamt : 12345
                sdate : 20111027
                odate : 無完工日
                atypena :會結案
                qupamtc :2924773
                qupamtad :0
                qupamt :48993271
                tqurece :0
                tqurecn :0
                budamt :0
                vencamt :0
                venper :102.33
                tvpay : 0
                nacpay :0
                venpay :0

        details(array):
            (object):
                detail_id : 1
                venno : VTJ0027
                venna :虹輝開發
                payrmk :扣漏水維修費用71000+5%
                vencamt :0
                tvenpay :0
                balance :0
                npay :-346
                ppay :-7654
                hpay :-455
                dpay : -19500
                venpay :-19500
                tax :0
 * }
 *
 *
 * @apiSuccessExample {int} Success-Response:
 * {
 *     1
 * }
 * @apiErrorExample {int} Error-Response:
 * {
 *      0
 * }
 *---------------------------------------------------------------------------
 */



 審核進度
/**
 *
 * @api {get} /acc/pjpay/on_going
 * @apiName onGoing
 *
 * @apiParam  {String} epjacc - 會計代號
*
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     data (array):
 *          (object)
 *              as_id - id
 *              as_no - no
 *              status - 狀態
                epjacc - 會計代號
                epjno - 專案代號
                venno - 廠商代號
                startYm - 開始年月
                endYm - 結束年月
                created_at - 送出審核日期
                sign_roles - 謙和流程
                details  (array) - 廠商付款明細
            *          (object)
                            venno - 廠商代號
                            venna - 廠商簡稱
                            payrmk - 付款摘要
                            vencamt - 發包總價
                            tvenpay - 發包已付
                            balance - 發包結餘
                            npay - 本期請款
                            ppay - 本期預付
                            hpay - 本期保留
                            dpay - 本期折讓
                            venpay - 本期實付
                            tax - 稅金

                            eamount - 工程主管核准金額
                            ermk - 工程主管備註
                            accamount - 財務主管核准金額
                            accrmk - 財務主管備註
                            cpmamount - CPM核准金額
                            cpmrmk - CPM備註
                            pdamount - PD核准金額
                            pdrmk - PD備註
 * }
 *---------------------------------------------------------------------------
 */




取消申請
/**
 *
 * @api {delete} /acc/pjpay/cancel_pay/{id}
 * @apiName deleteDemand
 *
 * @apiParam  {String} as_id - id
*
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *    1
 * }
 * @apiErrorExample {int} Error-Response:
 * {
 *      0
 * }
 *---------------------------------------------------------------------------
 */











