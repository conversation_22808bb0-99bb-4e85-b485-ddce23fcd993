<?php

use App\Modules\acc\Controllers\AuthController;
use App\Modules\acc\Controllers\CommonController;
use App\Modules\acc\Controllers\PjPayController;
use App\Modules\acc\Controllers\ReportController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });

Route::post('/acc/asap-resonse/pj/report', [App\Modules\WebHook\Controllers\WebHookController::class, 'acceptResponseInfo']);

Route::prefix('acc')->middleware('check.login')->namespace('\App\Modules\acc\Controllers')->group(function () {
    // webhook 回傳
    // $url = config('app.resopnse_to_report_url');
  

    //常用
    Route::prefix('common')->group(function () {
        Route::get('/employee/data', 'CommonController@fetchEmployeeData');
        Route::get('/employees', 'CommonController@fetchEmployees');
    });
    Route::prefix('pjpay')->group(function () {

        Route::controller(CommonController::class)->group(function () {
            // 取得批次
            Route::get('/get-batch', 'fetchBatch');
        });

        Route::controller(PjPayController::class)->group(function () {
            // 取得列表
            Route::get('/list', 'getPayList');
            Route::get('/detail_list', 'getPayDetailList');
            Route::get('/epj/info', 'fetchEpj');
            // 送出審核
            Route::post('/submit-pay', 'submitPay');
            Route::post('/submit-detail', 'submitDetail');

            // 取得審核進度
            Route::get('/on_going', 'onGoing');

            //取消申請
            Route::delete('/cancel_pay/{ac_id}', 'cancelPay');

            // get erp
            Route::post('/erp/project', 'fetchERPProject');
        });
    });

    Route::get('/operation/layouts', [\App\Modules\Report\Controllers\OperationController::class, 'getTypes']);
    Route::get('/operation/layouts/data', [\App\Modules\Report\Controllers\OperationController::class, 'index']);
    Route::get('/operation/layouts/export', [\App\Modules\Report\Controllers\OperationController::class, 'export']);

    Route::prefix('report')->group(function () {

        Route::controller(ReportController::class)->group(function () {
            // 專案付款
            Route::get('/pjpay/list', 'getPayReport');
            // 廠商
            Route::get('/venpay/list', 'getVenReport');
            // 匯出
            Route::post('/pjpay/export', 'payExport');
            Route::post('/venpay/export', 'venExport');
            // 傳真 ，寄信
            Route::post('/venpay/send', 'venSend');

        });
        //零用金報表
        Route::get('/cash/columns', 'PettyCashController@fetchColumns');
        Route::get('/cash', 'PettyCashController@fetchCash');
        Route::post('/cash/excel', 'PettyCashController@exportExcel');
    });

    Route::prefix('auth')->controller(AuthController::class)->group(function () {
        Route::get('/func-auth', 'fetchFuncAuth');
        Route::post('/func-auth/update', 'updateFuncAuth');
    });

    // 小鈴鐺功能
    Route::group(['prefix' => 'notify'], function () {
        // 取得通知列表
        Route::get('/', [App\Modules\acc\Controllers\NotificationController::class, 'getNotifications']);
        // 標記已讀(全部)
        Route::patch('/read/all', [App\Modules\acc\Controllers\NotificationController::class, 'readAllNotification']);
        // 標記已讀(單筆)
        Route::patch('/read/{notificationId}', [App\Modules\acc\Controllers\NotificationController::class, 'readNotification']);
        // 刪除通知(全部)
        Route::delete('/all', [App\Modules\acc\Controllers\NotificationController::class, 'deleteAllNotification']);
        // 刪除通知(單筆)
        Route::delete('/{notificationId}', [App\Modules\acc\Controllers\NotificationController::class, 'deleteNotification']);
    });


    /**
     * 績效獎金
     */

    // 顯示部門中的所有員工(作用於初審) <- 原本在專案撈取的部分，現在拉出來共用
    Route::get('perf-bonus/f/employees/{company}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'employees']);
    // 可發放績效獎金的部門代號對照列表(前端顯示使用，可由後端調整順序) <- 原本在專案撈取的部分，現在拉出來共用
    Route::get('perf-bonus/f/code/{company}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'code']);

    // 績效獎金所有相關 API
    Route::group(['prefix' => 'perf-bonus', 'middleware' => ['perf.permission']], function () {
        Route::group(['prefix' => 'setting', 'as' => 'settings::'], function () {
            // 相關設定 - 核定通知
            Route::get('/{company}/notification', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getNotification'])->name('notification-get');
            // 相關設定 - 設定核定通知相關資料
            Route::patch('/{company}/notification', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'notification'])->name('notification-patch');
            // 相關設定 - 功能權限
            Route::get('/{company}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getSetting'])->name('setting-get');
            // 相關設定 -  更新功能權限
            Route::put('/{company}/{setting_id}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'putSetting'])->name('setting-put');
        });

        // 專案撈取 prefix: f -> first
        Route::group(['prefix' => 'f', 'as' => 'first::'], function () {
            // 顯示對應公司的所有專案 (專案撈取)
            Route::get('projects/{company}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'index'])->name('project-get');
            // 送出審核(等待初審, 主管審核)
            Route::post('projects/{company}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'submitDetail']);
            // 取得正在審核的紀錄
            Route::get('processing/{company}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getProcessing']);
            // 取消審核
            Route::delete('processing/{company}/{ids}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'cancelProcess']);
        });

        // 獎金核定 prefix: s -> second
        Route::group(['prefix' => 's', 'as' => 'second::'], function () {

            Route::group(['prefix' => 'review', 'as' => 'review::'], function () {
                Route::get('/', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getShouldReviewProject']);
                Route::get('/{review_id}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getShouldReviewDetail']);
                Route::put('/{review_id}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'patchReview']);
            });

            Route::group(['prefix' => 'result', 'as' => 'result::'], function () {
                Route::get('/', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getAlreadyReview']);
                Route::get('/{review_id}', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getAlreadyReviewDetail']);
            });
        });

        // 結果查詢
        Route::group(['prefix' => 'result', 'as' => 'result::'], function () {
            Route::get('/project', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getResultByProjects'])->name('project');
            Route::get('/employee', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'getResultByEmployees'])->name('employee');
        });

        // 檔案匯出相關
        Route::group(['prefix' => 'download', 'as' => 'download::'], function () {
            Route::get('/projects', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'exportByProjects']);
            Route::get('/employees', [App\Modules\PerfBonus\Http\Controllers\PerfBonusController::class, 'exportByEmployees']);
        });
    });

});
