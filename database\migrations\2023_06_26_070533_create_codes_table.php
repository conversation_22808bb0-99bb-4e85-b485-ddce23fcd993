<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('codes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('code_kind');
            $table->string('code_parent');
            $table->string('code_id');
            $table->unsignedBigInteger('sort_order');
            $table->string('nm_zh_tw')->nullable();
            $table->string('nm_zh_cn')->nullable();
            $table->string('nm_en_us')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        DB::table('codes')->insert([
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W0', 'sort_order' => '0', 'nm_zh_tw' => '日', 'nm_zh_cn' => '', 'nm_en_us' => 'Sunday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W1', 'sort_order' => '1', 'nm_zh_tw' => '一', 'nm_zh_cn' => '', 'nm_en_us' => 'Monday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W2', 'sort_order' => '2', 'nm_zh_tw' => '二', 'nm_zh_cn' => '', 'nm_en_us' => 'Tuesday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W3', 'sort_order' => '3', 'nm_zh_tw' => '三', 'nm_zh_cn' => '', 'nm_en_us' => 'Wednesday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W4', 'sort_order' => '4', 'nm_zh_tw' => '四', 'nm_zh_cn' => '', 'nm_en_us' => 'Thursday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W5', 'sort_order' => '5', 'nm_zh_tw' => '五', 'nm_zh_cn' => '', 'nm_en_us' => 'Friday'],
            ['code_kind' => 'AA', 'code_parent' => '', 'code_id' => 'W6', 'sort_order' => '6', 'nm_zh_tw' => '六', 'nm_zh_cn' => '', 'nm_en_us' => 'Saturday'],

            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '0', 'sort_order' => '0', 'nm_zh_tw' => '未審核', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '1', 'sort_order' => '1', 'nm_zh_tw' => '審核中', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '2', 'sort_order' => '2', 'nm_zh_tw' => '同意', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '3', 'sort_order' => '3', 'nm_zh_tw' => '駁回', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '4', 'sort_order' => '4', 'nm_zh_tw' => '再審中', 'nm_zh_cn' => '', 'nm_en_us' => ''],
            ['code_kind' => 'AC', 'code_parent' => '', 'code_id' => '5', 'sort_order' => '5', 'nm_zh_tw' => '會簽', 'nm_zh_cn' => '', 'nm_en_us' => ''],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('codes');
    }
};
