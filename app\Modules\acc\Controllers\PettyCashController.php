<?php

namespace App\Modules\acc\Controllers;

use App\Modules\acc\models\CashColumns;
use App\Modules\acc\models\PettyCash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Traits\FormatDate;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Modules\acc\Controllers\ReportController as Report;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PettyCashController extends CommonController
{

    use FormatDate;
    protected $user;
    protected $CompanyId;
    protected $timezone;
    public function __construct()
    {
        $this->user = Session::get('employee_id');
        $this->CompanyId = Session::get('CompanyId');
        $this->timezone = Session::get('timezone');
    }
     //撈取零用金報表
    public function fetchCash(Request $request)
    {
        // if ($request->missing(['columns', 'endYm', 'pbatch'])) {
        //     return 0;
        // }
        $page = (int) $request->input('page') ?? 1;
        $per = $request->input('per') ?? 50;
        $query = PettyCash::with('employee', 'org')
            ->whereHas('company',function($q){
                $q->where('companies.id',$this->CompanyId);
                        });
        //判斷查詢條件
        if ($request->has('start')) {
            $query->where('created_at', '>=', Carbon::parse($request->input('start'))->setTimezone($this->timezone));
        }
        if ($request->has('end')) {
            $query->where('created_at', '<=', Carbon::parse($request->input('end'))->setTimezone($this->timezone));
        }
        if ($request->has('no')) {
            $query->where('no', 'like', '%' . $request->input('no') . '%');
        }
        if ($request->has('employee')) {
            $query->where('employee_id', $request->input('employee'));
        }
        //json欄位中的查詢條件比大小需用DB QUERY查詢後join
        if ($request->has('selectors')) {
            foreach ($request->get('selectors') as $selector) {
                if ($selector['type'] != 'date' && $selector['type'] != 'money'&& $selector['type'] != 'total') {
                    $json = [[
                        'name' => $selector['name'],
                        'txt_column' => $selector['value']
                    ]];
                    $query->whereJsonContains('columns', $json);
                }
                else if ($selector['type'] == 'date') {
                    $sql='(SELECT pc.id,columns
                    FROM petty_cash as pc
                    cross join lateral
                    jsonb_array_elements(pc.columns) id(ele)
             where (ele->>\'name\')::text = \'' .  $selector['name'] . '\'
                 AND ((ele->>\'txt_column\')::text)::date >= (\'' . $selector['value'][0] . '\')::date';
                 if(isset($selector['value'][1])){
                    $sql.=' AND ((ele->>\'txt_column\')::text)::date <= (\'' . $selector['value'][1] . '\')::date';
                 }
                 $sql.=')'.$selector['id'];
                    $query->join(
                        DB::raw($sql),
                        function ($join)use($selector) {
                            $join->on('petty_cash.id', '=', $selector['id'].'.id')->on('petty_cash.columns', '=', $selector['id'].'.columns');
                        }
                    );
                }else if ($selector['type'] == 'money'||$selector['type'] == 'total') {
                    $query->join(
                        DB::raw('(SELECT pc.id,columns
                     FROM petty_cash as pc
                     cross join lateral
                     jsonb_array_elements(pc.columns) id(ele)
              where (ele->>\'name\')::text = \'' . $selector['name'] . '\'
                  AND (ele->>\'txt_column\')::float >= ' . $selector['value'] . '

                     )
                     '.$selector['id']),
                        function ($join)use($selector)  {
                            $join->on('petty_cash.id', '=', $selector['id'].'.id')->on('petty_cash.columns', '=', $selector['id'].'.columns');
                        }
                    );
                }
            }
        }

        $result = $query->paginate($per);
        //分頁取出後重新排序欄位 ID須為文字所以前面加上C前贅字
        $result->getCollection()->transform(function ($item, $key) use ($request) {
            $defaultColumns = [
                strval('c' . '99') => $item->employee->payload->get('name'),
                strval('c' . '98') => $item->org->payload->get('name'),
                strval('c' . '97') => $item->employee->payload->get('job_title'),
                strval('c' . '96') => $this->FormatDate($item->created_at, 'date_time'),
                strval('c' . '95') => $item->no,
            ];
            foreach ($item->columns as $column) {
                $defaultColumns = array_merge($defaultColumns, ['c' . strval($column['id']) => $column['txt_column']]);
            }
            return $defaultColumns;
        });
        return $result;
    }
    //動態取得欄位資訊
    public function fetchColumns()
    {
        $data = CashColumns::where('company_id', $this->CompanyId)->first();
        $columns = $data->columns->map(function ($column) {
            //ID須為文字所以前面加上C前贅字
            $c = [
                'id' => 'c' . $column['id'],
                'name' => $column['name'],
                "type" => $column['type'],
            ];
            if ($column['type'] == 'dropdown' || $column['type'] == 'cascade') {
                $c['options'] = Arr::map($column['options'], function (array $option, int $key) {
                    return Arr::only($option, ['id', 'name']);
                });
            }
            return $c;
        });
        //部分固定資訊不在payload中故寫死
        $columns = collect([
            [
                'id' => 'c99',
                'name' => '申請人',
                "type" => 'input',
            ],
            [
                'id' => 'c98',
                'name' => '部門',
                "type" => 'input',
            ],
            [
                'id' => 'c97',
                'name' => '職稱',
                "type" => 'input',
            ],
            [
                'id' => 'c96',
                'name' => '申請日期',
                "type" => 'date',
            ],
            [
                'id' => 'c95',
                'name' => '單號',
                "type" => 'input',
            ],
        ])->merge($columns);
        return $columns;
    }
    //匯出excel 整理成固定的格式後呼叫共用 x軸放column y軸放row
    public function exportExcel(Request $request)
    {
        $docName = '零用金申請';
        $request->merge(['docName' => $docName]);
        $names = data_get($request->columns, '*.name');
        $ids = data_get($request->columns, '*.id');
        $request->merge(['names' => $names]);
        $mapped = Arr::map($request->values, function (array $item, int $key) use ($ids) {
            $slice = Arr::only($item, $ids);
            [$keys, $values] = Arr::divide($slice);
            return $values;
        });
        $request->merge(['mata' => $mapped]);
        return Report::outputExcel($request);
    }
}
