<?php

declare(strict_types=1);

namespace App\Traits;
use App\Modules\acc\models\Company;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

trait CompanyTrait
{
    protected string $companyCacheKey = 'cache_companies';

    protected function getCompanyId(string|int $company): int
    {
        if (is_numeric($company)) {
            return (int)$company;
        }
        if (!Cache::has($this->companyCacheKey)) {
            $this->refreshCompaniesCache();
        }

        return Cache::get($this->companyCacheKey)->pluck('id', 'name')->toArray()[$company] ?? 0;
    }

    protected function getCompanyName(int $companyId): string
    {
        if (!Cache::has($this->companyCacheKey)) {
            $this->refreshCompaniesCache();
        }
        return Cache::get($this->companyCacheKey)->pluck('name', 'id')->toArray()[$companyId] ?? null;
    }

    protected function refreshCompaniesCache()
    {
        $companiesData = DB::table('companies')->get(['id', 'payload->name as name']);
        Cache::put($this->companyCacheKey, $companiesData, 60 * 5);
    }
}
