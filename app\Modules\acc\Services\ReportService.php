<?php

namespace App\Modules\acc\Services;

use <PERSON><PERSON>\Gotenberg;
use <PERSON>enberg\Stream;
use App\Jobs\SendEmailJob;
use App\Jobs\SendFaxJob;
use App\Modules\acc\models\Employee;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
class ReportService
{
    protected $tmpDir;

    public function setTmpDir($dir): void
    {
        $this->tmpDir = $dir;
    }
    public function venEmail(string $dir, $info, $companyTitle, $logs): void
    {
        SendEmailJob::dispatch($dir, $info, $companyTitle, $logs);
    }
    public function venFax(string $dir, $info, $subjectTime, $logs): void
    {
        SendFaxJob::dispatch($dir, $info, $subjectTime, $logs);
    }
    public function reNumber($faxNo)
    {
        /** 因樹娟要直接當作正式的來用 所以先註解掉測試的 */
        if (config('app')['debug']) {
            return config('fax')['fax_debug_num']['5f'];
        }


        // 加上國碼
        // 去掉區碼的0
        $fax = explode("-", $faxNo);
        $number = '886' . \Str::remove('0', $fax[0]) . $fax[1] . $fax[2];
        return intval($number);
    }

    // 確認模板存在
    public function checkFile($baseName): bool
    {
        return Storage::disk('model')->exists('/' . $baseName);
    }

    // 取得模板
    public function getFilePath($baseName): string
    {
        $filePath = 'app/acc/export-model/' . $baseName;
        return $filePath;
    }
    /**
     * 匯出成word/excel
     * @param string $type 表單類型
     * @param mixed $sendType 匯出類型
     * @param mixed $baseName 模板檔案名稱，包含附檔名
     * @param mixed $filePath 模板path
     * @param mixed $data 
     * @param mixed $col
     * @return void
     */
    public function export(string $type, $sendType, $baseName, $filePath, $data, $col): void
    {
        // 副檔名
        $extension = pathinfo($baseName, PATHINFO_EXTENSION);
        // 目前沒有excel
        if ($extension == 'xls' || $extension == 'xlsx') {
            // return $this->outputExcel($d, $filePath);
            // exit;
        } else {
            $this->maketWord($type, $data, $col, $filePath);
        }
    }

    /**
     * 匯出成word
     * PHPoffice\PhpWord，是xml結構無法知道頁數，所以擴充空白行還是用算的
     * @param string $type
     * @param mixed $data
     * @param mixed $col
     * @param mixed $filePath
     */
    public function maketWord(string $type, $data, $col, $filePath)
    {
        if (empty($data))
            return 0;

        $templateProcessor = new \PhpOffice\PhpWord\TemplateProcessor(storage_path($filePath));
        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        // word 內所有可替換的變數
        $getVar = $templateProcessor->getVariables();

        // 表單欄位
        if ($type == 'pay')
            $templateProcessor->cloneRowAndSetValues('venno', $data);
        else if ($type == 'ven')
            $templateProcessor->cloneRowAndSetValues('epjacc', $data);
        // $this->formProcess($templateProcessor, $data, $getVar);

        // 單一欄位
        $employee = Employee::withTrashed()->get(); //->where('company_id', 1)
        foreach ($col as $key => $value) {
            // 將id 替換成人名
            if (\Str::contains($key, '名稱') && in_array($key, $getVar)) {
                // 將id 換成 名稱
                $name = $employee->firstwhere('id', $value)?->payload->get('name');
                $templateProcessor->setValue($key, $name);
            } else if (in_array($key, $getVar))
                $templateProcessor->setValue($key, $value);
        }

        // 要加總


        // 大約20 行滿版
        $rule_count = 20;
        // 剩下的最後一行要調整高度滿版
        // 加小計行及簽名行
        $row_count = count($data) + 2;
        // 最後頁有多少行資料
        $mod_count = ($row_count % $rule_count);
        // 大於0，clone $rule_count - $mod_count
        if ($mod_count > 1) {
            $templateProcessor->cloneRow('empty', $rule_count - $mod_count - 1);
        } else
            $templateProcessor->deleteRow('empty');



        // 剩下的變數給空白或清除
        $cleanVar = $templateProcessor->getVariables();
        foreach ($cleanVar as $key => $value) {
            $templateProcessor->setValue($value, '');
        }

        if ($type == 'pay')
            // 付款 :會計代號-付款年月-批次
            $fileName = $col['epjacc'] . '_' . $col['startYm'] . '_' . $col['pbatchno'];
        else if ($type == 'ven')
            // 廠商:廠商代號-付款年月-批次
            $fileName = $col['venno'] . '_' . $col['startYm'] . '_' . $col['pbatchno'];

        // 新增新的word
        $newPath = storage_path('app/public/acc/temp/' . $this->tmpDir . '/' . $fileName . '.docx');
        $templateProcessor->saveAs($newPath);
    }

    public function convertPDFAndResponseZip()
    {
        $dir = $this->setDirAndConvertPDF();

        // 要下载的压缩包的名称
        $zip_name = time() . '.zip';
        // 製作zip
        $this->makeZip($zip_name);

        // 回傳
        return response()->download($dir . '/' . $zip_name);
    }

    public function setDirAndConvertPDF()
    {
        // 暫存資料夾位置
        $dir = storage_path('app/public/acc/temp/' . $this->tmpDir);
        // $dir = 'C:\laragon\www\FDMC-ACC\storage\app\public\acc\temp\20230806091939_583';
        // 製作pdf
        $files = File::files($dir);
        foreach ($files as $key => $value) {
            // dd($value->getFilename());
            $this->convertPDF($value->getPathname(), $value->getFilename());
        }

        return $dir;
    }

    // public function convertPDF($path, $fileName = '')
    // {
    //     try {
    //         //TODO: Get your ClientID and ClientSecret at https://dashboard.aspose.cloud (free registration is required).
    //         $ClientSecret = "aacfe5991a5473b4cdf1e3ad2568d7cc";
    //         $ClientId = "81f3349b-f044-48e1-92b7-7b0fb8b5bc47";
    //         $wordsApi = new \Aspose\Words\WordsApi($ClientId, $ClientSecret);
    //         $format = "pdf";
    //         $file = ($path);
    //         $request = new \Aspose\Words\Model\Requests\ConvertDocumentRequest($file, $format, null);
    //         $result = $wordsApi->ConvertDocument($request);
    //         $name = str_replace('docx', 'pdf', $path);
    //         copy($result->getPathName(), $name);
    //     } catch (\Exception $e) {
    //         echo "Something went wrong: ", $e->getMessage(), "\n";
    //         PHP_EOL;
    //     }
    // }

    public function convertPDF($path, $fileName)
    {
        \Log::info('convert pdf begin... path: ' . $path);

        // 自架的服務
        $file = Gotenberg::save(
            Gotenberg::libreOffice(config('app.pdf_serv_url'))
                ->convert(Stream::path($path)),
            storage_path('app/public/acc/temp/' . $this->tmpDir)

        );

        // FileName回來的是hash，傳真跟email必須使用正確的名稱才可以work
        File::move(
            storage_path('app/public/acc/temp/' . $this->tmpDir) . '/' . $file,
            storage_path('app/public/acc/temp/' . $this->tmpDir) . '/' . str_replace('docx', 'pdf', $fileName)
        );

        \Log::info('convert pdf end...');
    }

    public function makeZip(string $zip_name)
    {
        $zip = new \ZipArchive();
        // 指定打包之後的壓縮檔要放位置 檔名為  $zip_name
        $path = storage_path('app/public/acc/temp/') . $this->tmpDir;
        $zip->open($path . '/' . $zip_name, \ZipArchive::CREATE | \ZipArchive::OVERWRITE);

        // 抓取暫時資料夾內的所有檔案
        $files = File::files($path);
        foreach ($files as $key => $value) {
            if (pathinfo($value, PATHINFO_EXTENSION) == 'pdf') {
                $relativeNameInZipFile = basename($value);
                $zip->addFile($value, $relativeNameInZipFile);
            }
        }
        $zip->close();
    }


    /** 
     * 全形轉半形 
     * @param string $str 
     * @return string     
     **/
    public function sbc2Dbc($str)
    {
        return preg_replace(
            // 全形字元  
            '/[\x{3000}\x{ff01}-\x{ff5f}]/ue',
            // 編碼轉換
            // 0x3000是空格，特殊處理，其他全形字元編碼-0xfee0即可以轉為半形
            '($unicode=char2Unicode(\'\0\')) == 0x3000 ? " " : (($code=$unicode-0xfee0) > 256 ? unicode2Char($code) : chr($code))',
            $str
        );
    }
}