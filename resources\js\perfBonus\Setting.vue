<template>
  <Banner :names="['功能權限', '核定通知']"/>
  <SettingHierarchy v-if="!currentTab" :employeeList="employeeList"></SettingHierarchy>
  <SettingNotification v-else-if="currentTab" ></SettingNotification>
</template>
  
<script>
import Banner from "@/common/Banner.vue";
import SettingHierarchy from '@/perfBonus/SettingHierarchy.vue'
import SettingNotification from '@/perfBonus/SettingNotification.vue'

export default {
  components: {
    Banner,
    SettingHierarchy,
    SettingNotification,
  },
  data() {
    return {
      currentTab: 0,
      apiURL: "/api/acc/perf-bonus",
      subsidiary: '',
      employeeList: []
    };
  },
  mounted() {
    this.subsidiary = localStorage.getItem('company_code')
    this.fetchEmployeeList()
  },
  methods: {
    fetchEmployeeList() {
      axios
      .get(this.apiURL + '/f/employees/' + this.subsidiary)
      .then(res => {
        this.employeeList = res.data.data
      })
      .catch(err => {
        console.log(err)
      })
    },
  },
};
</script>