name: Deploy
run-name: ${{ github.actor }} is testing out GitHub Actions 🚀

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  deploy-project:
    env:
      DEPLOY_PATH: /mnt/nfs/nginx/fdmc-accap
      PHP_EXEC: /usr/bin/php8.2
      COMPOSER_EXEC: /usr/local/bin/composer
    runs-on: gamma
    steps:
      - uses: actions/checkout@v4

      - name: 系統資訊與資源庫檔案
        run: |
          echo "User: $(whoami)"
          echo "Shell: $SHELL"
          echo "Home directory: $HOME"
          id
          env
          ls ${{ github.workspace }}

      - name: 依據根目錄下的 ".node-version" 或 ".nvmrc" 檔案安裝並使用NodeJS版本
        run: |
          FNM_PATH="/home/<USER>/.local/share/fnm"
          export PATH="$FNM_PATH:$PATH"
          eval "`fnm env`"
          fnm use --install-if-missing

      - name: 安裝依賴
        run: npm install

      - name: 編譯前端資源
        run: npm run build

      - name: 安裝 PHP 依賴
        run: $PHP_EXEC $COMPOSER_EXEC install

      - name: 部署專案
        run: |
          pwd
          echo "create deploy path: $DEPLOY_PATH"
          mkdir -p $DEPLOY_PATH

          echo "rm -rf $DEPLOY_PATH/node_modules $(pwd)/node_modules"
          rm -rf $DEPLOY_PATH/node_modules $(pwd)/node_modules

          echo "rsync -av --no-perms --no-owner --no-group --remove-source-files --exclude='.git' --exclude='.vscode' --exclude='.gitea' ./ $DEPLOY_PATH/"
          rsync -av --no-perms --no-owner --no-group --remove-source-files --exclude='.git' --exclude='.vscode' --exclude='.gitea' ./ $DEPLOY_PATH/

          echo "deploy path ($DEPLOY_PATH) successfully !"

      - name: 重啟排程
        run: |
          echo "重啟排程 ..."
          ps aux | grep "artisan queue:work" | grep -i accap | grep dev | awk '{print $2}' | xargs -r kill || true
