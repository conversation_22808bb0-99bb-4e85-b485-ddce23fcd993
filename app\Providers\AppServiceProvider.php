<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Model;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        if (config("app.debug")) {
            // \Log::info(__class__);
            // \Log::info('class : '.__CLASS__.' line : '.__LINE__);
            Model::setEventDispatcher($this->app['events']);

            $processId = rand(10000, 99999);
            \DB::listen(function ($query) use ($processId) {
                $sql = $query->sql;
                $time = $query->time;
                $bindings = $query->bindings;

                foreach ($bindings as $index => $binding) {
                    if (is_bool($binding)) {
                        $bindings[$index] = ($binding) ? ('1') : ('0');
                    } elseif (is_string($binding)) {
                        $bindings[$index] = "'$binding'";
                    }
                }

                $sql = preg_replace_callback('/\?/', function () use (&$bindings) {
                    return array_shift($bindings);
                }, $sql);

                \Log::channel('query')->info('Process:' . $processId . ' Time: ' . $time . ' Query: ' . $sql);
            });

        }
    }
}
