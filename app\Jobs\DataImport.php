<?php

namespace App\Jobs;

use App\Modules\acc\models\Email;
use App\Modules\acc\models\PjPay;
use App\Modules\acc\models\PjPayDetail;
use App\Modules\acc\models\Venbud;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class DataImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 180;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Log::info('sql start');
       
        $this->data();

        \Log::info('sql end');
    }


    public function data()
    {
        try {
            // PjPay
            $this->pjPay();
            
            PjPayDetailImportJob::dispatch();
            // email
            $this->email();
        } catch (\Exception $e) {
            \Log::error($e);
        }

    }

    public function pjPay()
    {
        $query = DB::connection('sqlsrv')
            ->table('pjpay')
            ->selectRaw('*')
            ->whereBetween('epjyn', ['20', '60']);

        PjPay::truncate();
        $pjpay = $query->cursor();
        $pjpay->chunk(700)->each(function ($chunked) {

            $chunk = [];
            // erp 的sql版本不支援 trim()
            $chunked->each(function ($item) use (&$chunk) {
                $payload = [
                    'epjacc' => trim($item->epjacc),
                    'epjno' => trim($item->epjno),
                    'payload' => json_encode([
                        'epjna' => trim($item->epjna),
                        'pdno' => trim($item->pdno ?? ''),
                        'pmno' => trim($item->pmno ?? ''),
                        'pcno' => trim($item->pcno ?? ''),
                        'pleper' => doubleval($item->pleper),
                        'venpayper' => doubleval($item->venpayper),
                        'difamt' => doubleval($item->difamt),
                        'sdate' => $item->sdate,
                        'odate' => $item->odate,
                        'atypena' => trim($item->atypena),
                        'qupamtc' => intval($item->qupamtc),
                        'qupamtad' => intval($item->qupamtad),
                        'qupamt' => intval($item->qupamt),
                        'tqurece' => intval($item->tqurece),
                        'tqurecn' => intval($item->tqurecn),
                        'budamt' => intval($item->budamt),
                        'vencamt' => intval($item->vencamt),
                        'tvpay' => intval($item->tvpay),
                        'nacpay' => intval($item->nacpay),
                    ])
                ];

                array_push($chunk, (array) $payload);
            });

            DB::connection('pgsql')->table('pj_pays')->insert($chunk);
            unset($chunk, $chunked, $payload);
            gc_collect_cycles();
        });
        unset($pjpay);
    }
    public function email()
    {
        $mail_query = DB::connection('sqlsrv')
            ->table('email')
            ->selectRaw('*')
            // ->get();
        ;

        Email::truncate();
        $mail = $mail_query->cursor();
        $mail->chunk(700)->each(function ($chunked) {
            $chunk = [];

            $chunked->each(function ($item) use (&$chunk) {
                $payload = [
                    'venno' => trim($item->venno),
                    'venna' => trim($item->venna),
                    'email' => trim($item->email),
                    'faxno' => trim($item->faxno),
                ];

                array_push($chunk, (array) $payload);
            });

            DB::connection('pgsql')->table('emails')->insert($chunk);
            unset($chunk);
        });
        unset($email);
    }
}
