<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use App\Modules\acc\models\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PerfPermission
 *
 * @property int $id 該欄位代表權限的唯一識別碼。
 * @property string $name 該欄位代表權限的名稱。
 * @property string $slug 該欄位代表權限的唯一標識符。
 * @property string $description 該欄位代表權限的描述。
 * @property \Illuminate\Support\Carbon $created_at 該欄位代表權限的建立時間。
 * @property \Illuminate\Support\Carbon $updated_at 該欄位代表權限的更新時間。
 *
 * @property \Illuminate\Database\Eloquent\Collection|\App\Modules\PerfBonus\Models\Employee[] $employees 該欄位代表擁有此權限的員工集合。
 * @property int|null $employees_count 該欄位代表擁有此權限的員工數量。
 */
class PerfPermission extends Model
{
    use HasFactory;

    protected $table = 'perf_permissions';

    protected $fillable = [
        'name',
        'slug',
        'description',
    ];

    /**
     * Get the employees associated with the permission.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function employees()
    {
        return $this->hasManyThrough(
            Employee::class,
            PerfEmployeePermission::class,
            'permission_id',
            'id',
            'id',
            'employee_id'
        );
    }
}
