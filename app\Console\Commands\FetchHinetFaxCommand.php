<?php

namespace App\Console\Commands;

use App\Modules\acc\Enums\SendStatusenum;
use App\Modules\acc\models\SendLog;
use GuzzleHttp\Client;
use Illuminate\Console\Command;

class FetchHinetFaxCommand extends Command
{

    // 最終狀態
    const FINAL_STATUS = [3, 6, 7, 8, 9];
    // 成功
    const SUCCESS = [8, 9];
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-fax-result';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '取得 Hinet 傳真資料，回寫結果到log中記錄';

    /**
     * Execute the console command.
     */
    public function handle()
    {
       $this->updateFaxStatus();
    }
    /**
     * 更新傳真的結果
     * @return void
     */
    public function updateFaxStatus()
    {
        $sendLogs = SendLog::query()
            ->where('sendStatus', SendStatusenum::SEND_FAX)
            // ->where('sendStatus', '<>','send-email')
            ->where('created_at', '>', now()->subDay())
            ->limit(200)
            ->get();

        $sendLogs->each(function ($log) {
            $payload = $log->payload;
            // 中華只能單筆
            $result = $this->fetchHinetFax(
                faxNo: $payload->get('faxNo'),
                subject: $payload->get('subject'),
                createdYmd: $log->created_at
            );

            // 還沒結束，不處理
            if (!in_array($result['status'], self::FINAL_STATUS))
                return;

            // 成功 or 失敗
            $faxReport = in_array($result['status'], self::SUCCESS) ? SendStatusenum::FAX_SUCCESS : SendStatusenum::FAX_FAILED;


            $log->setAttribute('sendStatus', $faxReport)
                ->setAttribute('payload->status', $result['status'])
                ->setAttribute('payload->statusText', $result['statusText'])
                ->save();

        });
    }

    /**
     * 取得 Hinet 傳真資料
     * @param string $faxNo
     * @param string $subject
     * @param string $createdYmd
     * @return string[]
     */
    protected function fetchHinetFax(
        string $faxNo,
        string $subject,
        string $createdYmd,
    ) {
        /** @var Client $client */
        $client = app(Client::class);
        $ymd = date('Ymd', strtotime($createdYmd));
        $response = $client->request('GET', 'https://service.hinetfax.hinet.net/Fax2003/retrieve_q41_fsync.asp', [
            'query' => [
                'f1' => $ymd,
                'f2' => $ymd,
                'f3' => $subject, // subject
                'f4' => $faxNo, // 傳真號碼
                'f5' => '86000789', // fixed
                'f6' => 'funcsync', // fixed
            ],
        ]);

        $content = (string) $response->getBody()->getContents();

        $decodeStr = mb_convert_encoding($content, 'UTF-8', 'BIG-5');
        $result = explode('^', $decodeStr);

        /** 
         * 詳情請去看 
         * 回報代碼.txt
         * 回報-某資訊.txt
         * 回報補充.txt
         */
        // [0] => 帳密正確
        // [1] => 1 // 回報次數
        // [2] => 886228983766 // 收件號碼
        // [3] => 2024/9/25 上午 11:52:20 // 上傳時間
        // [4] => 2024/9/25 上午 11:52:20 // 下傳時間
        // [5] => 86000789 // HN
        // [6] => acc20240925034907VTO0031 // subject
        // [7] => OK // 傳真結果
        // [8] => 0
        // [9] => 2 // 應傳頁數
        // [10] => 0
        // [11] => 8  // FAXSTATUS
        // [12] => 2 //  FAXED
        // [13] => &lt;br&gt;<br>
        // 成功: faxstatus = 8 or faxstatus = 9 
        // FAXED = PAGES (設定已傳頁數=傳真頁數)
        // FAXED 沒有傳成功前是 0 , 

        return [
            'verify' => $result[0],
            'faxNo' => $faxNo,
            'subject' => $subject,
            'created' => $ymd,
            'upload' => $result[3] ?? '-',
            'down' => $result[4] ?? '-',
            'status' => $result[11] ?? '-',
            'statusText' => $result[7] ?? '-',
            'upload_page' => $result[9] ?? '-',
            'down_page' => $result[12] ?? '-',
        ];
    }
}
