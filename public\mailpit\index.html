<!DOCTYPE html>
  <head>
    <title>測試用郵件信箱</title>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <script src="./vue2-dev.js"></script>
    <script src="./axios.min.js"></script>
    <script src="./moment.min.js"></script>
    <style>
        .message
        {
            border: 1px solid #006699;
            border-radius: 5px;
            margin: 5px 0;

            -webkit-box-shadow: 1px 1px 3px #333333;
	        -moz-box-shadow: 1px 1px 3px #333333;
	        box-shadow: 1px 1px 3px #333333;
        }

        .message .header
        {
            margin: 0;
            padding: 0;
            background-color: #006699;
            color: #ffffff;
            font-size: 0.9rem;
        }

        .message .header
        {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-between;
        }

        .message .header .icon
        {
            display: flex;
            align-items: center;

            padding: 0 5px;
            border-right: 1px solid #cccccc;
        }

        .message .header .metadata
        {
            width: 100%;
            padding: 4px 10px;
        }

        .message .header .metadata .recipient
        {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .message .header .metadata .recipient .to
        {
            font-size: 0.7rem;
        }

        .message .header .metadata .recipient .timestamp
        {
            font-size: 0.7rem;
            color: #cccccc;
        }

        .message .header .metadata .recipient .subject
        {
            font-weight: bold;
        }

        .message .loading
        {
            width: 100%;
            text-align: center;
            color: #006699;
        }

        .message .body
        {
            padding: 5px;
        }

        .message .body .container
        {
            width: 100%;
        }

        .message .body .error
        {
            width: 100%;
            text-align: center;
            color: #990000;
            font-weight: bold;
        }

        .menu
        {
            text-align: right;
        }

        .menu button
        {
            height: 32px;
        }

        .menu button .svg-icon
        {
            width: 20px;
            height: 20px;
            vertical-align: middle;
        }

        .menu button:disabled .svg-icon path
        {
            fill: #999999;
        }

        .parent_container
        {
            border: 1px #000000 solid;
            border-radius: 3px;
            background-color: #f0f0f0;
            margin: 10px 0;
            padding: 5px 10px;

            -webkit-box-shadow: 1px 1px 3px #333333;
	        -moz-box-shadow: 1px 1px 3px #333333;
	        box-shadow: 1px 1px 3px #333333;
        }

        [v-cloak] { display: none; }

        .svg-icon {
            width: 32px;
            height: 32px;
        }
    </style>
  </head>
  <body>
      <div v-cloak id="app">
        <div class="menu">
            <button @click="fetchMessageList()" :disabled="isFetching">
                <svg class="svg-icon" viewBox="0 0 20 20">
                    <path fill="#000000" d="M19.305,9.61c-0.235-0.235-0.615-0.235-0.85,0l-1.339,1.339c0.045-0.311,0.073-0.626,0.073-0.949c0-3.812-3.09-6.901-6.901-6.901c-2.213,0-4.177,1.045-5.44,2.664l0.897,0.719c1.053-1.356,2.693-2.232,4.543-2.232c3.176,0,5.751,2.574,5.751,5.751c0,0.342-0.037,0.675-0.095,1l-1.746-1.39c-0.234-0.235-0.614-0.235-0.849,0c-0.235,0.235-0.235,0.615,0,0.85l2.823,2.25c0.122,0.121,0.282,0.177,0.441,0.172c0.159,0.005,0.32-0.051,0.44-0.172l2.25-2.25C19.539,10.225,19.539,9.845,19.305,9.61z M10.288,15.752c-3.177,0-5.751-2.575-5.751-5.752c0-0.276,0.025-0.547,0.062-0.813l1.203,1.203c0.235,0.234,0.615,0.234,0.85,0c0.234-0.235,0.234-0.615,0-0.85l-2.25-2.25C4.281,7.169,4.121,7.114,3.961,7.118C3.802,7.114,3.642,7.169,3.52,7.291l-2.824,2.25c-0.234,0.235-0.234,0.615,0,0.85c0.235,0.234,0.615,0.234,0.85,0l1.957-1.559C3.435,9.212,3.386,9.6,3.386,10c0,3.812,3.09,6.901,6.902,6.901c2.083,0,3.946-0.927,5.212-2.387l-0.898-0.719C13.547,14.992,12.008,15.752,10.288,15.752z"></path>
                    <animateTransform v-if="isFetching" attributeType="xml"
                        attributeName="transform"
                        type="rotate"
                        from="360 0 0"
                        to="0 0 0"
                        dur="0.6s"
                        repeatCount="indefinite"/>
                </svg>
                <span v-if="!isFetching">
                    檢查新郵件
                </span>
                <span v-else>
                    處理中
                </span>
            </button>
        </div>
        <div class="parent_container" v-if="messages.length > 0">
            <div class="message" :id="'message-' + message.id" :key="message.id" v-for="message in messages">
                <div class="header" @click="switchMessage(message.id)">
                    <div class="icon">
                        <svg v-if="message.is_read == false" class="svg-icon" viewBox="0 0 20 20">
                            <path fill="#f7d204" d="M9.997,13.867c-0.388,0-0.702,0.315-0.702,0.702v4.335c0,0.387,0.314,0.702,0.702,0.702c0.388,0,0.702-0.315,0.702-0.702v-4.335C10.698,14.182,10.384,13.867,9.997,13.867z"></path>
                            <path fill="#f7d204" d="M9.997,6.133c0.388,0,0.702-0.315,0.702-0.702V1.096c0-0.386-0.314-0.702-0.702-0.702c-0.388,0-0.702,0.316-0.702,0.702v4.335C9.295,5.818,9.609,6.133,9.997,6.133z"></path>
                            <path fill="#f7d204" d="M12.89,13.604c-0.193-0.334-0.621-0.449-0.958-0.256c-0.335,0.193-0.45,0.623-0.256,0.958l1.568,2.719c0.129,0.224,0.364,0.35,0.607,0.35c0.119,0,0.24-0.03,0.351-0.094c0.336-0.193,0.451-0.624,0.257-0.958L12.89,13.604z"></path>
                            <path fill="#f7d204" d="M7.107,6.394c0.129,0.225,0.366,0.351,0.607,0.351c0.119,0,0.239-0.031,0.35-0.095c0.336-0.193,0.451-0.623,0.256-0.958L6.753,2.976C6.561,2.639,6.13,2.527,5.796,2.72C5.46,2.913,5.345,3.344,5.54,3.678L7.107,6.394z"></path>
                            <path fill="#f7d204" d="M6.13,10c0-0.389-0.314-0.702-0.702-0.702H1.096c-0.388,0-0.702,0.312-0.702,0.702c0,0.386,0.314,0.702,0.702,0.702h4.333C5.816,10.702,6.13,10.386,6.13,10z"></path>
                            <path fill="#f7d204" d="M18.901,9.299h-4.335c-0.388,0-0.702,0.312-0.702,0.702c0,0.386,0.314,0.702,0.702,0.702h4.335c0.388,0,0.702-0.316,0.702-0.702C19.602,9.611,19.289,9.299,18.901,9.299z"></path>
                            <path fill="#f7d204" d="M9.997,6.755c-1.789,0-3.244,1.455-3.244,3.245c0,1.789,1.455,3.244,3.244,3.244c1.79,0,3.245-1.455,3.245-3.244C13.242,8.211,11.786,6.755,9.997,6.755z M9.997,11.842c-1.015,0-1.841-0.826-1.841-1.841c0-1.017,0.826-1.842,1.841-1.842c1.015,0,1.842,0.825,1.842,1.842C11.839,11.016,11.012,11.842,9.997,11.842z"></path>
                            <path fill="#f7d204" d="M17.021,13.245l-2.716-1.567c-0.334-0.192-0.765-0.077-0.958,0.258c-0.195,0.334-0.079,0.764,0.256,0.958l2.716,1.567c0.111,0.064,0.232,0.094,0.351,0.094c0.241,0,0.478-0.126,0.607-0.351C17.472,13.867,17.356,13.439,17.021,13.245z"></path>
                            <path fill="#f7d204" d="M2.973,6.755l2.716,1.568C5.8,8.386,5.921,8.416,6.04,8.416c0.241,0,0.478-0.126,0.607-0.35c0.194-0.334,0.079-0.765-0.256-0.958L3.675,5.54C3.341,5.349,2.91,5.462,2.717,5.797C2.522,6.133,2.637,6.561,2.973,6.755z"></path>
                            <path fill="#f7d204" d="M13.347,8.066c0.128,0.224,0.366,0.35,0.607,0.35c0.119,0,0.24-0.03,0.351-0.093l2.716-1.568c0.335-0.194,0.451-0.622,0.256-0.959c-0.193-0.337-0.623-0.45-0.958-0.257l-2.716,1.568C13.268,7.301,13.152,7.731,13.347,8.066z"></path>
                            <path fill="#f7d204" d="M6.647,11.935c-0.192-0.337-0.622-0.452-0.958-0.258l-2.716,1.567c-0.335,0.194-0.45,0.622-0.256,0.959c0.129,0.224,0.366,0.351,0.607,0.351c0.119,0,0.24-0.03,0.351-0.094l2.716-1.567C6.726,12.699,6.841,12.269,6.647,11.935z"></path>
                            <path fill="#f7d204" d="M11.931,6.65c0.111,0.064,0.232,0.095,0.351,0.095c0.241,0,0.478-0.126,0.607-0.351l1.567-2.716c0.194-0.334,0.079-0.765-0.257-0.958c-0.333-0.192-0.764-0.079-0.958,0.256l-1.568,2.716C11.481,6.026,11.596,6.457,11.931,6.65z"></path>
                            <path fill="#f7d204" d="M8.065,13.348c-0.33-0.191-0.763-0.079-0.958,0.256l-1.57,2.719c-0.194,0.334-0.079,0.764,0.256,0.958c0.109,0.064,0.232,0.094,0.351,0.094c0.241,0,0.477-0.126,0.607-0.35l1.57-2.719C8.516,13.971,8.401,13.541,8.065,13.348z"></path>
                        </svg>
                        <svg v-else-if="loadingMessage == message.id" class="svg-icon" viewBox="0 0 20 20">
                            <path fill="#ffffff" d="M13.962,8.885l-3.736,3.739c-0.086,0.086-0.201,0.13-0.314,0.13S9.686,12.71,9.6,12.624l-3.562-3.56C5.863,8.892,5.863,8.611,6.036,8.438c0.175-0.173,0.454-0.173,0.626,0l3.25,3.247l3.426-3.424c0.173-0.172,0.451-0.172,0.624,0C14.137,8.434,14.137,8.712,13.962,8.885 M18.406,10c0,4.644-3.763,8.406-8.406,8.406S1.594,14.644,1.594,10S5.356,1.594,10,1.594S18.406,5.356,18.406,10 M17.521,10c0-4.148-3.373-7.521-7.521-7.521c-4.148,0-7.521,3.374-7.521,7.521c0,4.147,3.374,7.521,7.521,7.521C14.148,17.521,17.521,14.147,17.521,10"></path>
                        </svg>
                        <svg v-else class="svg-icon" viewBox="0 0 20 20">
                            <path fill="#ffffff" d="M12.522,10.4l-3.559,3.562c-0.172,0.173-0.451,0.176-0.625,0c-0.173-0.173-0.173-0.451,0-0.624l3.248-3.25L8.161,6.662c-0.173-0.173-0.173-0.452,0-0.624c0.172-0.175,0.451-0.175,0.624,0l3.738,3.736C12.695,9.947,12.695,10.228,12.522,10.4 M18.406,10c0,4.644-3.764,8.406-8.406,8.406c-4.644,0-8.406-3.763-8.406-8.406S5.356,1.594,10,1.594C14.643,1.594,18.406,5.356,18.406,10M17.521,10c0-4.148-3.374-7.521-7.521-7.521c-4.148,0-7.521,3.374-7.521,7.521c0,4.147,3.374,7.521,7.521,7.521C14.147,17.521,17.521,14.147,17.521,10"></path>
                        </svg>

                    </div>
                    <div class="metadata">
                        <div class="recipient">
                            <span class="to">收件者：{{ message.to_name }}<{{ message.to_email }}></span>
                            <span class="timestamp">{{ moment(message.sent_at).format('YYYY-MM-DD HH:mm:ss') }}</span>
                        </div>
                        <div>
                            <span class="subject">主旨：{{ message.subject }}</span>
                        </div>
                    </div>
                </div>
                <div class="loading" v-if="loadingMessage == message.id && selectedMessage != message.id">
                    <svg class="svg-icon" version="1.1" id="loader-1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 40 40" enable-background="new 0 0 40 40" xml:space="preserve">
                        <path opacity="0.2" fill="#000000" d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"/>
                        <path fill="#000000" d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0C22.32,8.481,24.301,9.057,26.013,10.047z">
                            <animateTransform attributeType="xml"
                            attributeName="transform"
                            type="rotate"
                            from="0 20 20"
                            to="360 20 20"
                            dur="0.5s"
                            repeatCount="indefinite"/>
                        </path>
                    </svg>
                </div>
                <div class="body" v-if="loadingMessage == message.id && selectedMessage == message.id">
                    <div class="container" v-if="content.html != null" v-html="content.html"></div>
                    <div class="container" v-else-if="content.txt != null" v-html="content.txt"></div>
                    <div v-else class="error">
                        發生錯誤，請聯繫系統管理員。
                    </div>
                </div>
            </div>
        </div>
        <div class="parent_container" v-else style="text-align: center; font-size: 1.1rem; padding: 10px;">
            （沒有郵件）
        </div>
      </div>

      <script src="./main.js"></script>
  </body>
  <style>
    .hidden-class {
        display: none;
    }
  </style>
</html>
