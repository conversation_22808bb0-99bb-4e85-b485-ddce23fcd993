<?php

namespace App\Modules\acc\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class CheckAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $name): Response
    {
        if (empty(Session::get('auth')) || !in_array($name, Session::get('auth'))) {
            return redirect('/logout');
        }
        return $next($request);
    }
}
