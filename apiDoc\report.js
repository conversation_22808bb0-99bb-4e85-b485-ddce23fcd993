
專案付款單列表
/**
 *
 * @api {get} /acc/report/pjpay/list
 * @apiName getPayReport
 *
 * @apiParam  {String, required} startYm - 開始年月
 * @apiParam  {String, required} endYm - 結束年月
 * @apiParam  {String, required} pbatch 批次
 * @apiParam  {Int, required} page 頁數
 * @apiParam  {Int, required} per 筆數
 * @apiParam  {String} epjacc 會計代號
 * @apiParam  {Bool} fetchDiff 撈取差額
 *
 *
 * @apiParamExample  {type} Request-Example:
 * {
 *      startYm : 202303,
 *      endYm : 202305,
 *      pbatch : 0,
 *      epjacc : 2021023,
 *      fetchDiff : true,
 *      page : 1,
 *      per : 50,
 * }
 *
 *
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     property : value
 *      data (array):
 *          (object)
                epjacc - 會計代號
                epjno - 專案代號
                epjna - 專案簡稱
                sdate - 開工日期
                odate - 完工日期

                venno - 廠商代號
                venna - 廠商簡稱
                payrmk - 付款摘要
                vencamt - 發包總價
                tvenpay - 發包已付
                balance - 發包結餘
                npay - 本期請款
                ppay - 本期預付
                hpay - 本期保留
                dpay - 本期折讓
                venpay - 本期實付
                tax - 稅金
                eamount - 工程主管核准金額
                ermk - 工程主管備註
                accamount - 財務主管核准金額
                accrmk - 財務主管備註
                cpmamount - CPM核准金額
                cpmrmk - CPM備註
                pdamount - PD核准金額
                pdrmk - PD備註
 *      current_page:1
 *      per_page:50
 *      total:5
 *      lastPage:5
 * }
 *
 * ---------------------------------------------------------------------------
 */


匯出專案請款單
/**
 *
 * @api {post} /acc/report/pjpay/export
 * @apiName payExport
 *
 * @apiParam  {array_object, required} payList - 選擇的列表
 * @apiParam  {string, required} payList - titla
 *
 *
 * @apiParamExample  {type} Request-Example:
 * {
 *      titla
        payList (array):
                (object)
                    tax": 0,
                    "dpay": 0,
                    "ermk": "",
                    "hpay": 0,
                    "npay": 66400,
                    "ppay": 0,
                    "pdrmk": "",
                    "venna": "展暘工程行",
                    "venno": "VTC0001-1",
                    "accrmk": "",
                    "cpmrmk": "",
                    "payrmk": "111/12/5-112/1/16天花.出風口.壁面油漆修補工資",
                    "venpay": 66400,
                    "balance": 66400,
                    "eamount": 66400,
                    "tvenpay": 0,
                    "vencamt": 66400,
                    "pdamount": 66400,
                    "accamount": 66400,
                    "cpmamount": 66400,
                    "endYm": "202304",
                    "epjna": "PRADA 昇松機",
                    "epjno": "*********",
                    "epjacc": "B23065G",
                    "startYm": "202304",
                    "pbatchno": "1"
 * }
 *
 *
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     1
 * }
 *
 * ---------------------------------------------------------------------------
 */


廠商付款憑單列表
/**
 *
 * @api {get} /acc/report/venpay/list
 * @apiName getVenReport
 *
 * @apiParam  {String, required} startYm - 開始年月
 * @apiParam  {String, required} endYm - 結束年月
 * @apiParam  {String, required} pbatch 批次
 * @apiParam  {Int, required} page 頁數
 * @apiParam  {Int, required} per 筆數
 * @apiParam  {String} venno 廠商代號
 * @apiParam  {String, required} sendStatus 發送狀態
 *
 *
 * @apiParamExample  {type} Request-Example:
 * {
 *      startYm : 202303,
 *      endYm : 202305,
 *      pbatch : 0,
 *      venno : VTD0003,
 *      fetchDiff : true,
 *      page : 1,
 *      per : 50,
 * }
 *
 *
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     property : value
 *      data (array):
 *          (object)
 *              venno - 廠商代號(table 辨識用)
 *              id - as_id
 *              pkey
 *              email - email
 *              faxno - 傳真
 *              info (object):
    *                   startYm - 開始年月
                        endYm - 結束年月
                        pbatchno - 批次
                        epjacc - 會計代號
                        epjno - 專案代號
                        epjna - 專案簡稱
                        venno - 廠商代號
                        venna - 廠商簡稱
                        total - 總計
                list (array):
 *                  (object)
                        hash_id -id
                        startYm - 開始年月
                        endYm - 結束年月
                        pbatchno - 批次
                        epjacc - 會計代號
                        epjno - 專案代號
                        epjna - 專案簡稱
                        venno - 廠商代號
                        venna - 廠商簡稱
                        payrmk - 付款摘要
                        vencamt - 發包總價
                        tvenpay - 發包已付
                        balance - 發包結餘
                        npay - 本期請款
                        ppay - 本期預付
                        hpay - 本期保留
                        dpay - 本期折讓
                        venpay - 本期實付
                        tax - 稅金
                        eamount - 工程主管核准金額
                        ermk - 工程主管備註
                        accamount - 財務主管核准金額
                        accrmk - 財務主管備註
                        cpmamount - CPM核准金額
                        cpmrmk - CPM備註
                        pdamount - PD核准金額
                        pdrmk - PD備註
                        status - 發送狀態
                        created_at - 送出日期
 *      current_page:1
 *      per_page:50
 *      total:5
 *      lastPage:5
 * }
 *
 * ---------------------------------------------------------------------------
 */



匯出-廠商付款憑單
/**
 *
 * @api {post} /acc/report/pjpay/export
 * @apiName payExport
 *
 * @apiParam  {array_object, required} payList - 選擇的列表
 *
 *
 * @apiParamExample  {type} Request-Example:
 * {
        payList (array):
                (object)
                    hash_id -id
                    startYm - 開始年月
                    endYm - 結束年月
                    pbatchno - 批次
                    epjacc - 會計代號
                    epjno - 專案代號
                    epjna - 專案簡稱
                    venno - 廠商代號
                    venna - 廠商簡稱
                    payrmk - 付款摘要
                    vencamt - 發包總價
                    tvenpay - 發包已付
                    balance - 發包結餘
                    npay - 本期請款
                    ppay - 本期預付
                    hpay - 本期保留
                    dpay - 本期折讓
                    venpay - 本期實付
                    tax - 稅金
                    eamount - 工程主管核准金額
                    ermk - 工程主管備註
                    accamount - 財務主管核准金額
                    accrmk - 財務主管備註
                    cpmamount - CPM核准金額
                    cpmrmk - CPM備註
                    pdamount - PD核准金額
                    pdrmk - PD備註
                    status - 發送狀態
                    created_at - 送出日期
 * }
 *
 *
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     1
 * }
 *
 * ---------------------------------------------------------------------------
 */



傳真/寄信-廠商付款憑單
/**
 *
 * @api {post} /acc/report/pjpay/export
 * @apiName payExport
 *
 * @apiParam  {string, required} sendType - 傳送類別
 * @apiParam  {array_object, required} payList - 選擇的列表
 * @apiParam  {array_object, required} sendInfo - 傳送資訊
 *
 *
 * @apiParamExample  {type} Request-Example:
 * {
 *      sendType - 傳真:fax /email:email
 *      sendInfo(array):
                (object)
                venno - 廠商代號
                email
                fax
        payList (array):
                (object)
                    hash_id -id
                    startYm - 開始年月
                    endYm - 結束年月
                    pbatchno - 批次
                    epjacc - 會計代號
                    epjno - 專案代號
                    epjna - 專案簡稱
                    venno - 廠商代號
                    venna - 廠商簡稱
                    payrmk - 付款摘要
                    vencamt - 發包總價
                    tvenpay - 發包已付
                    balance - 發包結餘
                    npay - 本期請款
                    ppay - 本期預付
                    hpay - 本期保留
                    dpay - 本期折讓
                    venpay - 本期實付
                    tax - 稅金
                    eamount - 工程主管核准金額
                    ermk - 工程主管備註
                    accamount - 財務主管核准金額
                    accrmk - 財務主管備註
                    cpmamount - CPM核准金額
                    cpmrmk - CPM備註
                    pdamount - PD核准金額
                    pdrmk - PD備註
                    status - 發送狀態
                    created_at - 送出日期
 * }
 *
 *
 * @apiSuccessExample {application/json} Success-Response:
 * {
 *     1
 * }
 *
 * ---------------------------------------------------------------------------
 */
