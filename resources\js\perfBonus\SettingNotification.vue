<template>
  <div class="mx-auto p-6 custom-height" style="max-width: 1620px;">
    <div class="shadow rounded-lg overflow-hidden bg-white flex flex-col">
      <!-- header -->
      <div class="p-6 flex items-center justify-between">
        <div class="text-xl font-bold grow-[1]">核定提醒</div>
        <div class="grow-[2]">
          <InputSwitch v-model="enable"  />
        </div>
        <div class="grow-[1] text-right"> 
          <Button label="儲存" class="custom-button " @click="saveChange"/>
        </div>
      </div>
      <!-- content -->
      <div v-show="enable" class="px-6 flex flex-col gap-6">
        <div>
          <div class="text-gray-400 text-sm">提醒通知頻率</div>
          <InputNumber v-model="frequency" inputId="minmax-buttons" mode="decimal" showButtons :min="1" :max="30" />
        </div>
        <div class="pb-6">
          <span class="mr-3 font-bold">提醒內容設定</span>
          <span class="text-red-500 text-sm mr-3">*需帶出數量時，請填寫參數:{x}</span>
          <span class="text-red-500 text-sm">*需帶出名字時，請填寫參數:{name}</span>
          <textarea v-model="content" class="mt-1 block w-full h-64 border border-gray-500 rounded p-2" rows="4" cols="50"></textarea>
        </div>
      </div>
    </div>
  </div>
  <Toast position="top-center" />
</template>
  
<script>
import Button from 'primevue/button';
import InputNumber from 'primevue/inputnumber';
import InputSwitch from 'primevue/inputswitch';
export default {
  components: {
    Button,
    InputNumber,
    InputSwitch,
  },
  data() {
    return {
      currentTab: 0,
      apiURL: "/api/acc/perf-bonus",
      subsidiary: '',
      enable: false,
      frequency: 1,
      content: ""
    };
  },
  mounted() {
    this.subsidiary = localStorage.getItem('company_code')
    this.fetchContent()
  },
  methods: {
    fetchContent() {
      axios
        .get(this.apiURL + `/setting/${this.subsidiary}/notification`)
        .then(res => {
          this.enable = res.data.data.find(el => el.key === 'status').value*1===1 ? true : false
          this.content = res.data.data.find(el => el.key === 'text').value
          this.frequency = res.data.data.find(el => el.key === 'frequencyDay').value*1
        })
        .catch(err => {
          console.warn(err)
          this.$toast.add({ severity: 'warn', summary: 'Warn Message', detail: '獲取失敗', life: 3000 });
        })
    },
    saveChange() {
      const data = {
        status: this.enable ? 1 : 0,
        data: {
            text: this.content,
            frequencyDay: this.frequency*1
        }
      }
      axios.patch(this.apiURL + `/setting/${this.subsidiary}/notification`, data)
        .then(response => {
          this.$toast.add({ severity: 'success', summary: 'Success Message', detail: '編輯成功', life: 3000 });
        })
        .catch(error => {
          this.$toast.add({ severity: 'error', summary: 'Error Message', detail: '編輯失敗', life: 3000 });
        });
      }
  },
};
</script>

<style scoped>
.custom-button {
  padding: 8px 24px;
}
.custom-height {
  min-height: calc(100vh - 160px);
}
@media (min-width: 768px) {
  .custom-height {
    height: calc(100vh - 84px);
  }
}
</style>