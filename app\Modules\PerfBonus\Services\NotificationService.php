<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Services;
use App\Modules\PerfBonus\Models\PerfBonusSetting;


class NotificationService
{
    public function getPerfNotificationSetting()
    {
        $tmp = PerfBonusSetting::where('key', 'like', 'notification.%')
            ->get([
                'company_id',
                'key',
                'value',
            ])
            ->transform(function ($it) {
                $it->key = str_replace('notification.', '', $it->key);
                return $it;
            })
            ->groupBy('company_id')
            ->transform(function ($it) {
                return $it->pluck('value', 'key');
            })
            ->toArray();

        return $tmp;
    }
}
