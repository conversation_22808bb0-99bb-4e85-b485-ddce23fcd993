function showDetail() {
    document.getElementById('btn-show').nextElementSibling.classList.toggle('hidden-class');
}

// import Vue from 'vue';
// window.Vue = Vue;

// import 'promise-polyfill/src/polyfill';
// import axios from 'axios';

// import moment from 'moment';
Vue.prototype.moment = moment;

const request = axios.create({
    baseURL: '/mailpit/release.php?',
});


const app = new Vue({
    el: '#app',
    data: {
        messages: [],
        selectedMessage: null,
        loadingMessage: null,
        content: {
            html: null,
            txt: null
        },
        isFetching: false
    },
    methods: {
        fetchMessageList() {
            this.isFetching = true;

            request.get('api/mailtrap/api/v1/inboxes/messages', { params: {}})
                .then(response => {
                    this.messages = response.data;
                })
                .catch(error => {
                    console.error(error);
                })
                .finally(() => {
                    this.isFetching = false;
                });
        },
        deleteMessage(id) {
            request.delete('api/mailtrap/api/v1/inboxes/messages/' + id)
                .then(response => {})
                .catch(error => { console.error(error);});
        },
        setRead(id) {
            this.messages.find((message) => message.id == id).is_read = true;

            request.patch('api/mailtrap/api/v1/inboxes/messages/' + id, {
                message: {
                    is_read: true
                },
            }, { params: {} })
                .then(response => {})
                .catch(error => {
                    console.error(error);
                    this.messages.find((message) => message.id == id).is_read = false;
                });
        },
        switchMessage(id) {
            if (id != this.selectedMessage) {
                this.selectedMessage = null;
                this.fetchMessage(id);
            }
            else {
                if (this.loadingMessage == null) {
                    this.loadingMessage = id;
                    setTimeout(() => {
                        document.querySelector("#message-" + id).scrollIntoView({
                            behavior: 'smooth'
                        });
                    }, 100);
                }
                else {
                    this.loadingMessage = null;
                }
            }
        },
        fetchMessage(id) {
            this.loadingMessage = id;
            let message = this.messages.find((message) => message.id == id);

            let actions = [];

            if (message.html_body_size > 0) {
                actions.push(
                    request.get('api/mailtrap' + message.html_path, {
                            params: {}
                        })
                        .then(response => {
                            console.log(response);
                            this.content.html = response.data;
                        })
                        .catch(error => {
                            console.error(error);
                            this.content.html = null;
                        })
                );
            }
            else {
                this.content.html = null;
            }

            if (message.text_body_size > 0) {
                actions.push(
                    request.get('api/mailtrap' + message.txt_path, {})
                        .then(response => {
                            this.content.txt = response.data;
                        })
                        .catch(error => {
                            console.error(error);
                            this.content.txt = null;
                        })
                )
            }
            else {
                this.content.txt = null;
            }

            Promise.all(actions)
                .then(() => {
                    if (message.is_read == false) {
                        this.setRead(id);
                    }
                    this.selectedMessage = id;
                    setTimeout(() => {
                        document.querySelector("#message-" + id).scrollIntoView({
                            behavior: 'smooth'
                        });
                    }, 100);
                })
                .catch();
        }
    },
    computed: {
        //
    },
    mounted() {
        this.fetchMessageList();
    }
})
