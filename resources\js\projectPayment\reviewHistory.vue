<template>
  <span class="p-input-icon-right" v-if="action == 0">
    <i class="pi pi-search" @click="fetchEpjacc()" />
    <InputText
      class="mr-2 xl:w-48 lg:w-44 mb-2"
      placeholder="搜尋會計代號"
      type="text"
      v-model="epjacc"
      :useGrouping="false"
      @input="epjacc = $event.target.value.toUpperCase()"
      @keyup.enter="fetchEpjacc()"
    />
    <!-- @input="getLists(1, 10)" -->
  </span>
  <br />
  <div v-if="action == 0">
    <div class="text-center" v-if="projects.length == 0">
      <div class="w-full relative top-1/2 translate-y-2/3">
        <img
          class="mx-auto"
          v-if="!loading && projects.length == 0"
          src="../../images/page_state/search.png"
          alt=""
        />
        <img
          v-else-if="!loading"
          src="../../images/page_state/search_empty.png"
          alt=""
        />
        <!-- <p v-if="!loading && projects.length == 0">輸入您要查詢的資料</p> -->
        <p v-if="!loading && projects.length == 0">
          無任何查詢結果，請再次查詢
        </p>
      </div>
    </div>
    <div v-else>
      <div>
        <DataTable
          :value="projects"
          @row-click="Row_dblclick"
        >
          <Column field="epjacc" header="會計代號"></Column>
          <Column field="epjno" header="專案代號"></Column>
          <Column field="epjna" header="專案簡稱"></Column>
          <Column field="startYm" header="付款年月"></Column>
          <Column field="created_at" header="送出審核日期"></Column>
          <Column field="status" header="審核進度">
            <template #body="slotProps">
              <div
                :class="this.$parent.changeState(slotProps.data.status)"
                class="w-16 my-auto rounded-full py-1.5 border text-sm text-center"
              >
                {{ slotProps.data.status == 1 ? "審核中" : "駁回" }}
              </div>
            </template>
          </Column>
        </DataTable>
      </div>
    </div>
  </div>
  <div v-if="action == 1">
    <span @click="goBack()" class="cursor-pointer my-auto">
      <p><font-awesome-icon :icon="['fa', 'arrow-left']" /> 返回</p>
    </span>

    <br />
    <div class="p-6 w-full bg-white rounded-xl shadow">
      <div>
        <span class="text-xl text-900 font-bold">專案資訊</span>
      </div>
      <div class="flex flex-wrap justify-between">
        <div>
          <label class="text-sm text-gray-400">會計代號</label>
          <p>{{ project.epjacc }}</p>
        </div>
        <div>
          <label class="text-sm text-gray-400">專案代號</label>
          <p>{{ project.epjno }}</p>
        </div>
        <div>
          <label class="text-sm text-gray-400">專案簡稱</label>
          <p>{{ project.epjna }}</p>
        </div>
        <div>
          <label class="text-sm text-gray-400">付款年月</label>
          <p>{{ project.startYm }}</p>
        </div>
        <div>
          <label class="text-sm text-gray-400">送出審核日期</label>
          <p>{{ project.created_at }}</p>
        </div>
        <div>
          <label class="text-sm text-gray-400">審核進度</label>
          <div
            :class="this.$parent.changeState(project.status)"
            class="w-16 my-auto rounded-full py-1.5 border text-sm text-center"
          >
            {{ project.status == 1 ? "審核中" : "駁回" }}
          </div>
        </div>
        <ConfirmPopup class="confirmLayout"></ConfirmPopup>
        <Button label="取消申請" @click="cancelSubmit($event)" />
      </div>
    </div>
    <div class="p-6 my-6 w-full bg-white rounded-xl shadow">
      <div>
        <span class="text-xl text-900 font-bold">審核進度</span>
      </div>
      <Timeline :value="project.sign_roles" layout="horizontal" align="top">
        <template #marker="slotProps">
          <span
            class="border-2 rounded-full w-4 h-4 mx-1"
            :class="
              this.$parent.stateTimeLineMarker(slotProps.item.apply_status)
            "
          >
          </span>
        </template>
        <template #connector="slotProps">
          <div
            class="w-full h-0.5"
            :class="
              this.$parent.stateTimeLineConnector(slotProps.item.apply_status)
            "
          ></div>
        </template>
        <template #content="slotProps">
          <div class="flex">
            <div class="p-4 w-full bg-gray-100 rounded-xl h-40 shadow">
              <div class="flex justify-between">
                <h3 class="font-semibold">
                  {{ slotProps.item.self_name }}&ensp;{{ slotProps.item.name }}
                </h3>
                <p
                  class="text-sm"
                  :class="this.$parent.stateColor(slotProps.item.apply_status)"
                >
                  {{
                    this.$parent.statusReturnName(slotProps.item.apply_status)
                  }}
                </p>
              </div>
              <p>{{ slotProps.item.timestamp }}</p>
              <p>{{ slotProps.item.remark }}</p>
            </div>
            &emsp;
          </div>
        </template>
      </Timeline>
    </div>
    <div
      class="p-6 my-6 w-full bg-white rounded-xl shadow whitespace-nowrap overflow-x-auto flex"
    >
      <DataTable
        :value="project.details"
        :metaKeySelection="false"
        :scrollable="true"
        scrollHeight="600px"
        responsiveLayout="scroll"
        stripedRows
      >
        <template #header>
          <div class="flex align-items-center justify-between gap-2">
            <p class="text-xl text-900 font-bold">專案付款</p>
          </div>
        </template>
        <!-- <Column
            v-if="isEdit"
            frozen
            selectionMode="multiple"
            headerStyle="width: 3rem"
          ></Column> -->
        <Column field="venno" header="廠商代號"></Column>
        <Column field="venna" header="廠商簡稱"></Column>
        <Column field="payrmk" header="付款摘要" footer="小計"></Column>
        <Column
          field="vencamt"
          header="發包總價"
          :footer="this.$parent.computedDetailTotal(project.details, 'vencamt')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.vencamt) }}
          </template>
        </Column>
        <Column
          field="tvenpay"
          header="發包已付"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'tvenpay')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.tvenpay) }}
          </template>
        </Column>
        <Column
          field="balance"
          header="發包結餘"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'balance')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.balance) }}
          </template>
        </Column>
        <Column
          field="npay"
          header="本期請款"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'npay')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.npay) }}
          </template>
        </Column>
        <Column
          field="ppay"
          header="本期預付"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'ppay')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.ppay) }}
          </template>
        </Column>
        <Column
          field="hpay"
          header="本期保留"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'hpay')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.hpay) }}
          </template>
        </Column>
        <Column
          field="dpay"
          header="本期折讓"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'dpay')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.dpay) }}
          </template>
        </Column>
        <Column
          field="venpay"
          header="本期實付"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'venpay')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.venpay) }}
          </template>
        </Column>
        <Column
          field="eamount"
          header="工程主管核准金額"
          style="text-align: right"
          :footer="this.$parent.computedDetailTotal(project.details, 'eamount')"
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.eamount) }}
          </template>
        </Column>
        <Column field="ermk" header="工程主管備註"></Column>
        <Column
          field="accamount"
          header="財務主管核准金額"
          style="text-align: right"
          :footer="
            this.$parent.computedDetailTotal(project.details, 'accamount')
          "
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.accamount) }}
          </template>
        </Column>
        <Column field="accrmk" header="財務主管備註"></Column>
        <Column
          field="cpmamount"
          header="CPM核准金額"
          style="text-align: right"
          :footer="
            this.$parent.computedDetailTotal(project.details, 'cpmamount')
          "
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.cpmamount) }}
          </template>
        </Column>
        <Column field="cpmrmk" header="CPM備註"></Column>
        <Column
          field="pdamount"
          header="PD核准金額"
          style="text-align: right"
          :footer="
            this.$parent.computedDetailTotal(project.details, 'pdamount')
          "
        >
          <template #body="slotProps">
            {{ this.$parent.FormatValue(slotProps.data.pdamount) }}
          </template>
        </Column>
        <Column field="pdrmk" header="PD備註"></Column>
      </DataTable>
    </div>
  </div>
  <Toast position="top-center" />
  <ProgressSpinner
    v-if="loading"
    style="position: absolute; top: 50%; left: 50%; z-index: 1001"
  />
</template>
<script>
export default {
  data: function () {
    return {
      action: 0,
      epjacc: null,
      loading: false,
      projects: [
        // {
        //   epjacc: "A22014G",
        //   epjno: "202106201",
        //   epjna: "M.K.台中三井",
        //   payDate: "2023/03 - 2023/03",
        //   submitTime: "2023/02/03 10:00",
        //   state: "審核中",
        // },
      ],
      project: null,
      project_index: null,
      apiURL: "/api/acc/pjpay",
      popup: null,
    };
  },
  mounted() {
    this.get_on_going();
  },
  methods: {
    fetchEpjacc() {
      this.get_on_going(this.epjacc);
    },
    get_on_going(epjacc = null) {
      this.loading = true;
      axios
        .get(this.apiURL + "/on_going", {
          params: {
            epjacc: epjacc,
          },
        })
        .then((response) => {
          this.projects = response.data;
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    Row_dblclick(event) {
      this.project_index = event.index;
      this.project = this.projects[event.index];
      this.action = 1;
    },
    goBack() {
      this.action = 0;
      this.project = null;
      this.project_index = null;
    },
    cancelSubmit(event) {
      this.$confirm.require({
        target: event.currentTarget,
        message: "是否確定取消申請?",
        acceptLabel: "確定",
        rejectLabel: "取消",
        accept: () => {
          axios
            .delete(this.apiURL + "/cancel_pay/" + this.project.as_id)
            .then((response) => {
              this.get_on_going();
              this.action = 0;
              this.$toast.add({
                severity: "success",
                summary: "取消成功",
                life: 3000,
              });
            })
            .catch((error) => {
              console.error(error);
              this.$toast.add({
                severity: "error",
                summary: "取消失敗",
                life: 3000,
              });
            })
            .finally(() => {});
        },
      });
    },
  },
};
</script>
<style scoped>
</style>
