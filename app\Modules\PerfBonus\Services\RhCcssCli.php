<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Services;

use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Throwable;

class RhCcssCli
{
    protected Client $httpCli;

    public function __construct(
        protected EmployeeService $employeeService
    ) {
        $this->httpCli = new Client([
            'base_uri' => config('rh.host'),
            'timeout' => config('rh.timeout'),
            'headers' => [
                'X-Internal-System' => config('rh.internal_key', 'fdmc-acc'),
            ],
        ]);
    }

    /**
     * 會需要公司 id 的原因想要拿到對應案件中的公司員工編號，使用公司來限縮避免撞名的問題。
     * 因為評鑑系統中並無相關.
     * @param array $projectCompanyIdMap 專案id 與 公司id 的對照陣列,example: ['********* ' => 1]
     *
     * @return array
     */
    public function getProjects(array $projectCompanyIdMap = []): array
    {
        if (empty($projectCompanyIdMap)) {
            return [];
        }

        $epjnoArr = array_keys($projectCompanyIdMap);
        $uri = '/api/perf-bonus/' . implode(',', $epjnoArr);

        try {
            $rawResponse = (string) $this->httpCli->get($uri)->getBody();
            $result = json_decode($rawResponse, true);

            $projectQuestions = $result['projectQuestions'] ?? [];
            $projects = $result['projects'] ?? [];

            $tmp = [];
            foreach ($epjnoArr as $epjno) {
                $projectQuestion = $projectQuestions[$epjno] ?? [];
                $projectEmployees = $projects[$epjno] ?? []; // 專案參與人員
                // $projectEvaluation = $evaluations[$epjno] ?? []; // 評鑑資料

                if (empty($projectQuestion) || empty($projectEmployees)) {
                    $tmp[$epjno] = [];
                    continue;
                }

                // 找到該專案對應到公司的所有員工
                $tmpProjectEmployees = [];

                // PM 或 CPM 資訊引入
                $managers = Arr::pluck($projectEmployees, 'member_type', 'pm_no');
                foreach ($managers as $empno => $memberType) {
                    $foundEmployee = $this->employeeService->getEmployeeByEmpno($empno);

                    // 找不到人可能已經離職了
                    if (empty($foundEmployee)) {
                        continue;
                    }

                    $alreadyInManager = Arr::first($tmpProjectEmployees, fn ($it) => $it['empno'] === $empno);
                    if ($alreadyInManager) {
                        continue;
                    }

                    $tmpProjectEmployees[] = [
                        'empno' => $empno,
                        'empna' => $foundEmployee['name'],
                        'code' => $foundEmployee['code'],
                        'memberType' => $memberType,
                        'showTitle' => match ($memberType) {
                            0, 2 => 'CPM(工程經理)', // 工程
                            1 => 'PM(專案經理)',
                            default => 'unknown',
                        },
                        'commonType' => match ($memberType) {
                            0, 2 => 'E', // 工程
                            default => 'P',
                        },
                        'pm' => null,
                        'pd' => null,
                        'pmScores' => null,
                        'pdScore' => null,
                    ];
                }

                foreach ($projectEmployees as [
                    'empna' => $empna,
                    'empno' => $empno,
                    'member_type' => $memberType,
                    'answers' => $answers,
                    'pm_name' => $pmName,
                    'pm_no' => $pmNo,
                    'pd_name' => $pdName,
                    'pd_no' => $pdNo,
                    'pd_total' => $pdTotal,
                    'eva' => $evaluation,
                ]) {
                    $foundEmployee = $this->employeeService->getEmployeeByEmpno($empno);

                    // 找不到人可能已經離職了
                    if (empty($foundEmployee)) {
                        continue;
                    }

                    $tmpProjectEmployees[] = [
                        'empno' => $empno,
                        'empna' => $empna,
                        'rating' => $evaluation['name'] ?? null,
                        'code' => $foundEmployee['code'],
                        'memberType' => $memberType,
                        'showTitle' => match ($memberType) {
                            0 => '工程師',
                            1 => '設計師',
                            2 => '實施設計師',
                            default => '',
                        },
                        'commonType' => match ($memberType) {
                            0, 2 => 'E', // 工程
                            default => 'P',
                        },
                        'pm' => [
                            'empna' => $pmName,
                            'empno' => $pmNo,
                        ],
                        'pd' => [
                            'empna' => $pdName,
                            'empno' => $pdNo,
                        ],
                        'pmScores' => $answers,
                        'pdScore' => floatval($pdTotal),
                    ];
                }
                $tmp[$epjno]['employees'] = $tmpProjectEmployees;
            }

            return $tmp;
        } catch (Throwable $th) {
            Log::error('RH_CCSS CLI ERROR: ' . $th->getMessage());
            return [];
        }
    }
}
