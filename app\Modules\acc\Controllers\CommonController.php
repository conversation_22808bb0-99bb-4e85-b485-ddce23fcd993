<?php

namespace App\Modules\acc\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\acc\models\Employee;
use App\Modules\acc\models\PjPay;
use App\Modules\acc\models\PjPayDetail;
use App\Traits\CompanyTrait;
use App\Traits\FormatDate;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class CommonController extends Controller
{
    use CompanyTrait;
    protected $company_id;
    protected $user_id;

    public function __construct()
    {
        $this->user_id = Session::get('employee_id');
    }

    public static function fetchEmployees($includeTrashed =false)
    {
        $company_id = Session::get('CompanyId');

        $employees = Employee::where('company_id', $company_id)
            ->with('orgs:org_unit_members.employee_id,org_units.id,org_units.payload->name as org_name')
            ->select('id', 'payload->name as name', 'payload->job_title as job_title')
            ->when($includeTrashed, function ($query) {
                return $query->withTrashed();
            })
            ->get();

        return $employees->map(function ($employee) {
            return [
                'id' => $employee->id,
                'job_title' => $employee->job_title,
                'name' => $employee->name,
                'org_id' => $employee->orgs->pluck('id')->first(),
                'org_name' => $employee->orgs->pluck('org_name')->first(),

            ];
        });
    }
//* 員工個人資訊
public function fetchEmployeeData(Request $request)
    {
        if (empty($this->user_id)) {
            return null;
        }
        $employee = Cache::get("employee_{$this->user_id}");
        if (empty($employee)) {
            $employee = Employee::with(['orgs', 'company:id,payload->name as company_name'])->findOrFail($this->user_id)->toArray();
            Cache::put("employee_{$this->user_id}", $employee, 60 * 5);
        }
        $inSessionCompanyName = $this->getCompanyName((int)Session::get('CompanyId'));
        $payload = $employee['payload'];
        $result = [
            'id' => $this->user_id,
            'dep' => count($employee['orgs']) > 0 ? $employee['orgs'][0]['payload']['fullname'] : '',
            'name' => $payload['name'],
            'no' => $payload['employee_number'],
            'title' => $payload['job_title'],
            'photo'=>isset($payload['imgUrl'])?$payload['imgUrl']:'/storage/images/nobody.jpg',
            'company_code' => $employee['company']['company_name'],
            'session_company_code'  => $inSessionCompanyName,
        ];
        return $result;
    }
    public function fetchBatch()
    {
        return PjPayDetail::select(DB::raw('distinct pbatchno as code,pbatchno||\'_\'||pbatchna as name'))->orderby('code')->get();
    }
}
