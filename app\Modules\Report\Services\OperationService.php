<?php

namespace App\Modules\Report\Services;

use App\Modules\acc\models\Code;
use App\Traits\ExcelColumnTrait;
use App\Traits\FormatDate;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use OpenSpout\Common\Entity\Style\CellAlignment;
use OpenSpout\Common\Entity\Style\Style;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Rap2hpoutre\FastExcel\FastExcel;
use Symfony\Component\HttpFoundation\StreamedResponse;

class OperationService {

    use FormatDate, ExcelColumnTrait;
    const GROUP_KEYWORDS = '營運表單';

    protected int $companyId;

    protected $timezone;

    protected int $employeeId;

    public function __construct()
    {
        $this->companyId = Session::get('CompanyId') ?? 1;
        $this->timezone = Session::get('timezone') ?? 'asia\taipei';
        $this->employeeId = Session::get('employee_id') ?? 0;
    }

    /**
     * 取得 layout 列表
     *
     * @return \Illuminate\Support\Collection
     */
    public function getLayouts()
    {
        return DB::connection('asap')
            ->table('demand_groups')
            ->join('demand_layouts', 'demand_layouts.group_id', '=', 'demand_groups.id')
            ->where('company_id', $this->companyId)
            ->where('demand_groups.name', 'like', '%' . self::GROUP_KEYWORDS . '%')
            ->whereNull('demand_layouts.deleted_at')
            ->whereNull('demand_groups.deleted_at')
            ->get([
                'demand_layouts.id as layout_id',
                'demand_layouts.name',
                DB::raw("cast(demand_layouts.payload->>'sort' as integer) as sort"),
                'demand_layouts.open',
                'demand_layouts.priv_id',
            ])
            ->filter(fn ($it) => !!$it->open)
            ->sortBy('sort')
            ->values();
    }

    /**
     * 取得 layout 的歷史版本
     *
     * @param null|array<int> $layoutIds
     * @return array [
     *      ['name' => '用印-投標作業', 'ids' => [972, 967, 960, ...]]
     *      ['name' => '用印-其他', 'ids' => [963, 959, 958, ...]]
     * ]
     */
    public function getLayoutHistories($layoutIds = null)
    {
        $groupLayouts = DB::connection('asap')
            ->table('demand_groups')
            ->select(['demand_layouts.id', 'demand_layouts.name', 'demand_layouts.priv_id', 'demand_layouts.open'])
            ->join('demand_layouts', 'demand_layouts.group_id', '=', 'demand_groups.id')
            ->where('demand_groups.name', 'like', '%' . self::GROUP_KEYWORDS . '%')
            ->whereNull('demand_groups.deleted_at')
            ->orderBy('demand_layouts.id', 'desc')
            ->get();

        $tempDataset = $groupLayouts->groupBy('name')
            ->transform(function ($it) {
                return [
                    'name' => $it->first()->name,
                    'ids' => $it->pluck('id')->toArray(),
                ];
            })
            ->values()
            ->toArray();

        // 全部資料找出後再做篩選比較簡單。
        if (!empty($layoutIds)) {
            return array_filter($tempDataset, function ($it) use ($layoutIds) {
                foreach ($layoutIds as $layoutId) {
                    if (in_array($layoutId, $it['ids'])) {
                        return true;
                    }
                }
                return false;
            });
        }
        return $tempDataset;
    }

    protected function getEmpName(int $employeeId)
    {
        if (Cache::has('operation_employee')) {
            $cacheData = Cache::get('operation_employee');
            return $cacheData[$employeeId] ?? '';
        }

        $cacheData = DB::table('employees')
            ->select(['id', "payload->name as name"])
            ->get()
            ->pluck('name', 'id');

        Cache::put('operation_employee', $cacheData, 60 * 5);
        return $cacheData[$employeeId] ?? '';
    }

    /**
     * 取得 layout 的資料
     *
     * @param array|null $layoutId is layout id
     * @param array $condition is where condition about get data 搜索條件
     * @param bool $retBuilder is return builder ? 回傳是否為 builder
     * @param bool $isExpr 是否為匯出(不分頁)
     * @return \Illuminate\Support\Collection | \Illuminate\Database\Query\Builder | array
     */
    public function getLayoutData($layoutIds = null, $condition = [], $retBuilder = false, $isExpr = false, array $extraSearch = [])
    {
        $historyLayouts = $this->getLayoutHistories($layoutIds);
        $layoutIds = array_merge(...Arr::pluck($historyLayouts, 'ids'));

        if (!empty($extraSearch)) {
            // 附加搜尋條件
            $tempDemandNos = [];
            foreach ($extraSearch as $key => $value) {
                $result = match ($key) {
                    // 找事件
                    '需求日' => $this->searchWithPayloadItem('需求日期', $value),
                    '會計代號' => $this->searchWithPayloadItem('會計代號', $value),
                    '專案代號' => $this->searchWithPayloadItem('專案代號', $value),
                    '專案簡稱' => $this->searchWithPayloadItem('專案名稱', $value),
                    '用途' => $this->searchWithPayloadItem('用途', $value),
                    '公司(用印單位)' => $this->searchWithPayloadItem('公司(用印單位)', $value),
                    '簽章種類' => $this->searchWithPayloadItem('簽章種類', $value),
                    '部門' => $this->searchWithPayloadItem('部門', "name: $value"),
                    '專技人員' => $this->searchWithPayloadItem('專技人員', $value),
                    '防火管理人/勞安人員' => $this->searchWithPayloadItem('防火管理人/勞安人員', $value),
                    '政府-室裝文件' => $this->searchWithPayloadItem('政府-室裝文件', $value), // 未確認
                    '需求文件' => $this->searchWithPayloadItem('需求文件', $value), // 未確認
                    '備註' => $this->searchWithPayloadItem('備註', $value),

                    // 找人員
                    'PM簽核' => $this->searchWithSignRole('PM', $value),
                    'PD簽核' => $this->searchWithSignRole('PD', $value),
                    '總管理處簽核' => $this->searchWithSignRole('總管理處', $value),
                    '會簽人員簽核' => $this->searchWithSignRole('會簽人員', $value), // 未確認
                    '財務初審' => $this->searchWithSignRole('財務初審', $value),
                    '財務覆核' => $this->searchWithSignRole('財務覆核', $value),
                    '財務核決' => $this->searchWithSignRole('財務核決', $value),
                    '財務用印' => $this->searchWithSignRole('財務用印', $value),
                    '財務初審/用印' => $this->searchWithSignRole('財務初審/用印', $value),
                    '財務用印時間' => $this->searchRawTime($value),
                    default => collect(),
                };

                $tempDemandNos[] = $result->pluck('no')->unique()->filter()->values()->toArray();

            }
        }

        // 這邊先做一次查詢結果，在與原先的資料集做合併搜尋，可以撈出簽核節點都已經完成的單（簽核節點數量 = 簽核完成數量
        $linkQuery = DB::connection('asap')->table('demands_query')
            ->select(['demand_id', DB::raw('max(created_by) as created_by')])
            ->whereNotNull('demand_id')
            ->whereIn('layout_id', $layoutIds)
            // 找人員，使用下面 switch case 會發生異常，所以先做一次查詢
            ->when(isset($condition['employee']), fn ($query) => $query->where('created_by', $condition['employee']))
            ->orderBy('demand_id', 'desc')
            ->groupBy('demand_id')
            // 2 是完成 | 5 是會簽 | 4 是指定重送
            ->having(DB::raw('count(demand_id)'), '=', DB::raw('count(demand_id) filter (where apply_status::integer = 2 or apply_status::integer = 4  or apply_status::integer = 5)'));

        $builder = DB::connection('asap')
            ->table($linkQuery, 'temp1')
            ->select([
                DB::raw('distinct temp2.demand_id'),
                'temp2.no',
                'temp2.demand_id',
                'temp2.payload',
                'temp2.created_by',
                'temp2.created_at',
                'temp2.updated_at',
            ])
            ->join('demands_query as temp2', function ($join) {
                $join->on('temp1.demand_id', '=', 'temp2.demand_id');
            });

        // 這邊會這樣做的原因是因為上方的 distinct 在做分頁器時會有異常，所以這邊先做一次查詢，再將結果放入 builder 內
        $builder = DB::connection('asap')->table($builder, 'temp3');

        // extra search
        if (isset($tempDemandNos)) {
            foreach ($tempDemandNos as $demandNos) {
                $builder->whereIn('temp3.no', $demandNos);
            }
        }

        // 有搜索條件的話，加入對應的 case 去做搜索
        $builder->when(!empty($condition), function ($query) use ($condition) {
            foreach ($condition as $key => $value) {
                switch (true) {
                    case $key === 'no': // for 匯出使用
                        if (is_array($value)) {
                            $query->whereIn('no', $value);
                        } else {
                            $query->where('no', 'like', "%{$value}%");
                        }
                        break;
                    case $key === 'start' && !empty($value):
                        $query->where('created_at', '>=', Carbon::parse($value)->setTimezone('Asia/Taipei')->startOfDay()->toISOString());
                        break;
                    case $key === 'end' && !empty($value):
                        $query->where('created_at', '<=', Carbon::parse($value)->setTimezone('Asia/Taipei')->endOfDay()->toISOString());
                        break;
                    default:
                        break;
                }
            }
        });

        if ($retBuilder) {
            return $builder;
        }

        $perPage = $condition['per_page'] ?? 10;
        $builder = $isExpr ? $builder->get() : $builder->paginate($perPage);

        $noArr = $builder->pluck('no')->toArray();
        $latestDemandDocuments = $this->getLatestDemandDocument($noArr);
        $dataset = $builder->map(function ($it) use (&$latestDemandDocuments) {
            return $this->formatGetLayoutData($it, $latestDemandDocuments);
        });

        $customLists = $this->getCustomList($dataset->pluck('demand_id')->values()->toArray());
        $dataset->transform(function ($it) use ($customLists) {
            $demandId = $it->demand_id;
            $demandListData = $customLists[$demandId] ?? [];
            $idxMap = Arr::pluck($it->custom_list, 'idx');

            foreach ($it->custom_list as &$customData) {
                $foundIdx = array_search($customData['idx'], $idxMap);

                if ($foundIdx === false) {
                    continue;
                }

                if (!isset($demandListData[$foundIdx])) { // 這種資料會有問題，直接回傳空值
                    return null;
                } else {
                    $customData['value'] = $demandListData[$foundIdx];
                }
            }
            return $it;
        });
        $retData = [
            'data' => $dataset,
        ];
        if (! $isExpr) {
            $retData['paginate'] = [
                'total' => $builder->total(),
                'last_page' => $builder->lastPage(),
            ];
        }
        return $retData;
    }

    /**
     * 格式化 layout 的資料 -> 針對一筆資料去做格式化。
     *
     * @param object $dataset
     * @param array $latestDemandDocuments -> 這個是為了抓到最新的文件
     * @return object
     */
    protected function formatGetLayoutData($dataset, $latestDemandDocuments = [])
    {
        // 將時間格式轉成使用者時間
        $dataset->created_at = Carbon::parse($dataset->created_at)->timezone($this->timezone)->format('Y/m/d H:i:s');
        $dataset->updated_at = Carbon::parse($dataset->updated_at)->timezone($this->timezone)->format('Y/m/d H:i:s');

        $payload = json_decode($dataset->payload, true);
        unset($dataset->payload);

        // 簽核人相關資訊
        $dataset->applicant_name = $payload['applicant']['name'] ?? '';
        $dataset->applicant_title = $payload['applicant']['title'] ?? '';
        $dataset->applicant_dep = $payload['applicant']['dep'] ?? '';
        $dataset->company_id = $payload['company_id'];
        $dataset->layout_name = $payload['layout_name'];

        // 簽核角色相關資訊 (簽核人、簽核時間、簽核意見)
        $dataset->sign_roles = array_map(function ($signData) {
            $rawTime = $signData['raw_time'] ?? '';
            $timestamp = $signData['timestamp'] ?? '';
            if (!empty($rawTime)) {
                $rawTime = Carbon::parse($rawTime)->timezone($this->timezone)->format('Y/m/d H:i:s');
            }
            return [
                'self_name' => $signData['self_name'],
                'role_id' => $signData['role_id'],
                'role_name' => $this->getEmpName($signData['role_id']),
                'remark' => $signData['remark'] ?? '',
                'raw_time' =>$rawTime,
                'timestamp' => $timestamp,
                'column_name' => $signData['column_name'] ?? '',
                'apply_status' => $signData['apply_status'],
            ];
        }, $payload['sign_roles']);

        // 欄位相關資訊
        $colCollection = collect($payload['forms'][0]['columns']);
        $dataset->cols = $colCollection->only(['name', 'type', 'mul', 'must']);
        // $dataset->cols = $colCollection;
        $dataset->values = $colCollection
            ->filter(fn ($itDatasetVal) => !in_array($itDatasetVal['type'], ['customList']))
            ->map(function ($itDatasetVal) use (&$latestDemandDocuments, $dataset) {
                $fileLinks = [];

                if ($itDatasetVal['type'] === 'document' && !empty($latestDemandDocuments[$dataset->no])) {
                    $latestDemandDocument = $latestDemandDocuments[$dataset->no] ?? null;
                    if (isset($latestDemandDocument['file'][0]['URL'])) {

                        foreach ($latestDemandDocument['file'] as $file) {
                            $fileLinks[] = "<a href=\"" . config('app.fdmc_sys.asap') . '/storage' . $file['URL'] . "\" target=\"_blank\" rel=\"noreferrer noopener\">" . $file['name'] . "</a>";
                        }
                    }
                }

                return [
                    'name' => $itDatasetVal['name'],
                    'value' => match($itDatasetVal['type']) {
                        'dropdown' => isset($itDatasetVal['value']['name']) ? $itDatasetVal['value']['name']: $itDatasetVal['value'][0], // 這邊原始資料有問題。
                        'date' => Carbon::parse($itDatasetVal['value'])->timezone($this->timezone)->format('Y/m/d'),
                        'money' => bcadd($itDatasetVal['value'] ?? 0, 0),
                        'document' => implode('<br>', $fileLinks),
                        default => $itDatasetVal['value'] ?? '',
                    },
                ];
            });

        // 撈出自定義清單的資料
        $dataset->custom_list = $colCollection
            ->filter(fn ($dataset) => in_array($dataset['type'], ['customList']))
            ->map(function ($dataset, $key) {
                // 將 欄位的索引 加進資料集內，方便與 custom list 的資料做對應
                $dataset['idx'] = $key;
                return $dataset;
            })
            ->values()
            ->toArray();

        $dataset->last_time = $latestDemandDocuments[$dataset->no]['lastTime'] ?? null;
        return $dataset;
    }

    /**
     * 取得最新檔案從(從簽合節點中取得最新的文件)
     * @param array $noArr
     * @return array
     */
    public function getLatestDemandDocument($noArr)
    {
        $joinQuery = DB::connection('asap')
            ->table('demands_query')
            ->select([
                DB::raw('max(sign_id) as sign_id'),
                'no',
            ])
            ->whereIn('no', $noArr)
            ->groupBy('no');

        $data =  DB::connection('asap')
            ->table($joinQuery, 'condition_1')
            ->join('demands_query', function ($join) {
                $join->on('condition_1.sign_id', '=', 'demands_query.sign_id');
                $join->on('condition_1.no', '=', 'demands_query.no');
            })
            ->select(['demands_query.no', 'demands_query.payload->sign_roles as sign_roles'])
            ->get();

        return $data->pluck('sign_roles', 'no')
            ->transform(function ($it) {
                $it = json_decode($it, true);
                rsort($it);

                $lastTime = Arr::first($it, fn ($itSignData) => isset($itSignData['timestamp']))['timestamp'] ?? null;
                $file = Arr::first($it, fn ($itSignData) => isset($itSignData['document_info']));

                if ($file) {
                    $file = $file['document_info'];
                }

                return [
                    'lastTime' => $lastTime,
                    'file' => $file,
                ];

            })
            ->toArray();
    }

    protected function getCustomList($demandIds)
    {
        $tmpIdx = 0;
        $data =  DB::connection('asap')
            ->table('custom_lists')
            ->select([
                'demand_id',
                'column_index',
                DB::raw("payload->>'form_setting' as form_setting"),
                'list',
            ])
            ->whereIn('demand_id', $demandIds)
            ->whereNull('deleted_at')
            ->orderBy('demand_id', 'asc')
            ->orderBy('column_index', 'asc')
            ->get()
            ->groupBy('demand_id')
            ->transform(function ($itGroup) use (&$tmpIdx) {
                return $itGroup->transform(function ($it) use (&$tmpIdx) {
                    $data = [];
                    $formSetting = json_decode($it->form_setting, true);
                    $formSetting = array_map(fn ($it) => $it['name'], $formSetting);
                    $list = json_decode($it->list, true);

                    foreach ($list as $idx => &$value) {
                        $data[] = array_combine($formSetting, $value);
                    }
                    $tmpIdx++;
                    return $data;
                });
            })
            ->toArray();

        return $data;
    }

    public function export($layoutId = null, $condition = [], $showColumns = [], $extraSearch = [])
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        foreach ($showColumns as $idx => $column) {
            // 將第一列的儲存格設定為粗體、黃色背景
            $position = $this->calculateColumn($idx, 0);
            $sheet->getCell($position)->getStyle()->getFont()->setBold(true);
            $sheet->getCell($position)->getStyle()->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()
                ->setRGB('FFFF00');
            // 填入第一列的資料 (表頭名稱)
            $sheet->setCellValue($position, $column);
        }

        /** @var \Illuminate\Database\Query\Builder $builder */
        $builder = $this->getLayoutData(
            $layoutId,
            $condition,
            true,
        );

        // 將需要的資料打包進入 tempData 內
        $tempData = [];
        $builder->orderBy('created_at', 'desc');
        $builder->chunk(10, function ($dataset) use (&$showColumns, &$tempData) {
            $noArr = $dataset->pluck('no')->toArray();
            $latestDemandDocuments = $this->getLatestDemandDocument($noArr);
            $dataset->transform(function ($it) use ($latestDemandDocuments) {
                return $this->formatGetLayoutData($it, $latestDemandDocuments);
            });

            $customLists = $this->getCustomList($dataset->pluck('demand_id')->values()->toArray());
            $dataset->transform(function ($it) use ($customLists) {
                $demandId = $it->demand_id;
                $demandListData = $customLists[$demandId] ?? [];;
                $idxMap = Arr::pluck($it->custom_list, 'idx');

                foreach ($it->custom_list as &$customData) {
                    $foundIdx = array_search($customData['idx'], $idxMap);

                    if ($foundIdx === false) {
                        continue;
                    }
                    $customData['value'] = $demandListData[$foundIdx];
                }
                return $it;
            });

            // 將資料封裝為 fast excel 使用格式，並且將欄位資訊轉成欲顯示格式， 如 申請日 => 2021/01/01(星期一)
            $dataset->each(function ($it) use (&$showColumns, &$tempData) {
                $data = [
                    '需求名稱' => $it->layout_name,
                    '用印單號' => $it->no,
                    '申請日' => $this->dateFormatter($it->created_at),
                    '需求日' => '',
                    '會計代號' => '',
                    '專案代號' => '',
                    '專案簡稱' => '',
                    '用途' => '',
                    '申請人' => $it->applicant_name,
                    '公司(用印單位)' => '',
                    '簽章種類' => '',
                    '部門' => $it->applicant_dep,
                    '專技人員' => '',
                    '防火管理人/勞安人員' => '',
                    '政府-室裝文件' => '',
                    '需求文件' => '',
                    '備註' => '',
                    'PM簽核' => '',
                    'PD簽核' => '',
                    '總管理處簽核' => '',
                    '會簽人員簽核' => '',
                    '財務初審' => '',
                    '財務覆核' => '',
                    '財務核決' => '',
                    '財務用印' => '',
                    '財務初審/用印' => '',
                    '申請時間' => $this->dateFormatter($it->created_at),
                ];
                foreach ($it->values as $value) {
                    switch ($value['name']) {
                        case '專案名稱':
                            $data['專案簡稱'] = $value['value'];
                            break;
                        case '需求日期':
                            $data['需求日'] = $this->dateFormatter($value['value'], withTime: false);
                            break;
                        default:
                            $data[$value['name']] = $value['value'];
                            break;
                    }
                }

                // 自訂表單
                foreach ($it->custom_list as $value) {
                    // 直接使用表單的第一個元素來判斷他是哪種表單
                    $kind1 = $value['value'][0]['文件名稱'] ?? false;
                    $kind2 = $value['value'][0]['用印文件'] ?? false;
                    $kind3 = $value['value'][0]['需求文件'] ?? false;

                    $tempDataset = [];
                    if ($kind1) {
                        $tempDataset = array_map(fn ($it) => ['name' => $it['文件名稱'], 'num' => intval($it['份數'])], $value['value']);
                    } else if ($kind2) {
                        $tempDataset = array_map(fn ($it) => ['name' => $it['用印文件'], 'num' => intval($it['份數'])], $value['value']);
                    } else if ($kind3) {
                        $tempDataset = array_map(fn ($it) => ['name' => $it['需求文件'], 'num' => intval($it['份數'])], $value['value']);
                    }

                    $data[$value['name']] = implode("\n", Arr::pluck(array_filter($tempDataset, fn ($it) => $it['num'] > 0), 'name'));
                }

                // 審核人員，將審核資料提取出來
                foreach ($it->sign_roles as $signRole) {

                    if ('PM/APM' === $signRole['self_name']) {
                        $data['PM簽核'] = $signRole['role_name'] . $signRole['timestamp'] . PHP_EOL . $signRole['remark'] ?? '';
                    } else if ('PD' === $signRole['self_name']) {
                        $data['PD簽核'] = $signRole['role_name'] . $signRole['timestamp'] . PHP_EOL . $signRole['remark'] ?? '';
                    } else {
                        $data[$signRole['self_name']] = $signRole['role_name'] . $signRole['timestamp'] . PHP_EOL . $signRole['remark'] ?? '';
                    }
                }

                $tempData[] = array_filter($data, function ($key) use (&$showColumns) {
                    return in_array($key, $showColumns);
                }, ARRAY_FILTER_USE_KEY);
                return $data;
            });
        });

        $headerStyle = (new Style())
            ->setFontBold()
            ->setBackgroundColor("FFFF00")
            ->setCellAlignment(CellAlignment::CENTER)
            ->setShouldWrapText();

        $opPath = storage_path('use_seals/'. 'op' . time() . '.xlsx');
        $exportPath = (new FastExcel($tempData))->headerStyle($headerStyle)->export($opPath);

        $spreadsheet = IOFactory::load($exportPath);
        $spreadsheet->getActiveSheet()->freezePane('A1');
        $spreadsheet->getActiveSheet()->freezePane('A2');
        $spreadsheet->getActiveSheet()->freezePane('B1');
        $spreadsheet->getActiveSheet()->freezePane('C1');
        $spreadsheet->getActiveSheet()->freezePane('D1');
        $spreadsheet->getActiveSheet()->freezePane('E1');
        $spreadsheet->getActiveSheet()->getColumnDimension('A')->setWidth(20);
        $spreadsheet->getActiveSheet()->getColumnDimension('B')->setWidth(21);
        $spreadsheet->getActiveSheet()->getColumnDimension('C')->setWidth(20);
        $spreadsheet->getActiveSheet()->getColumnDimension('D')->setWidth(15);

        return new StreamedResponse(function () use ($spreadsheet) {
            $writer = new Xlsx($spreadsheet);
            $writer->save('php://output');
        }, 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="test.xlsx"; filename*=utf8', // html回傳excel需寫標頭,訂定格式為UTF-8
            'Cache-Control' => 'max-age=0',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    protected function dateFormatter($date = '', $lang = 'zh_tw', $withTime = true)
    {
        $weekIdx = null;

        switch (true) {
            case gettype($date) == 'string':
                $carbon = Carbon::parse($date);

                if (preg_match('/^[0-9+\-]+(T.+Z)/', $date)) { // 如果字串是 ISO 時間格式的話，就轉成使用者時區
                    $carbon->timezone($this->timezone);
                }
                $weekIdx = $carbon->dayOfWeek;
                break;
            case ($date instanceof Carbon): /** @var Carbon $date */
                $carbon = $date;
                $weekIdx = $date->dayOfWeek;
                break;
            default:
                return 'parse err !';
        }

        $formatStr = $carbon->format('Y/m/d') . '({{ nm }})';
        if ($withTime) {
            $formatStr .= $carbon->format('H:i');
        }
        $cacheKey = sprintf('day_map_%s', $lang);
        if (Cache::has($cacheKey)) {
            $cacheData = Cache::get($cacheKey);
            return str_replace('{{ nm }}', $cacheData[$weekIdx] ?? '', $formatStr);
        }

        $langRawSQL = match ($lang) {
            'zh_tw' => DB::raw('nm_zh_tw as nm'),
            'en_us' => DB::raw('nm_en_us as nm'),
            default => DB::raw('nm_zh_tw as nm'),
        };

        $dataset = Code::where('code_kind', 'AA')->get([
            DB::raw('cast(substring(code_id from 2) as integer) as week_idx'),
            $langRawSQL,
        ])->pluck('nm', 'week_idx')->toArray();
        Cache::put($cacheKey, $dataset, 60 * 10);
        return str_replace('{{ nm }}', $dataset[$weekIdx] ?? '', $formatStr);;
    }

    protected function searchWithPayloadItem($itemName, $itemValue)
    {
        $q = DB::connection('asap')
            ->table(
                DB::connection('asap')
                    ->table('demands_query')
                    ->select([
                        'demand_type',
                        'demand_id',
                        'role_id',
                        'no',
                        'created_at',
                        DB::raw("jsonb_array_elements(payload->'forms'->0->'columns')->>'name' as item_name"),
                        DB::raw("jsonb_array_elements(payload->'forms'->0->'columns')->>'type' as item_type"),
                        DB::raw("jsonb_array_elements(payload->'forms'->0->'columns')->>'value' as item_value"),
                    ])
            , 'temp1');


        return $q->where('item_name', $itemName)->where('item_value', 'like', "%{$itemValue}%")->get();
    }

    protected function searchWithSignRole($itemName, $itemValue)
    {
        $employeeIds = DB::table('employees')
            ->select(['id'])
            ->where('payload->name', 'like', "%{$itemValue}%")
            ->get()
            ->pluck('id');

        $q = DB::connection('asap')
            ->table('demands_query')
            ->select(['no'])
            ->where('role_name', $itemName)
            ->whereIn('role_id', $employeeIds);

        return $q;
    }

    protected function searchRawTime($date)
    {
        // 如果是日期格式的話才做搜尋
        $date = Carbon::parse($date)->timezone('Asia/Taipei')->format('Y-m-d');

            $subQuery = DB::connection('asap')
                ->table('demands_query')
                ->select([
                    'no',
                    DB::raw('max(sign_id) as sign_id')
                ])
                ->groupBy('no');

            return DB::connection('asap')
                ->table($subQuery, 'temp1')
                ->join('demands_query', 'temp1.sign_id', '=', 'demands_query.sign_id')
                ->where('raw_time', 'like', "%{$date}%")
                ->get(['demands_query.no', 'demands_query.raw_time']);
    }
}
