<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class OrgUnit extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['id', 'company_id', 'payload', 'metadata'];
    protected $casts = ['payload' => 'collection', 'metadata' => 'collection'];

    public function childs()
    {
        return $this->hasMany(OrgUnit::class, 'metadata->parent');
    }
    public function employees()
    {
        return $this->hasManyThrough(Employee::class, OrgUnitMember::class, 'org_unit_id', 'id', 'id', 'employee_id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
