/* 專案付款 */
CREATE VIEW dbo.pjpay
AS 
 with tmp_epjemp (epjno,empno,sno)
as (select epjno,empno,sno
  	from bellerpdbf.dbo.epjemp
  	where cdoyn <>'N' 
)
select 
pp.epjyn,
case 
	when pp.epjacc='' then  pp.epjno
	else pp.epjacc
	end as epjacc,
pp.epjno,
epjna,
pdno,
pmno,
pcno,
case  
--	pc.pleper when null then 100 
	when atypena='刪除' then 0
	when pc.pleper is null then 100 
	else pc.pleper 
	end as pleper,
pv.venpayper,
fb.difamt,
pp.pd30 as sdate,
pp.pd31 as odate,
atypena,
qupamtc,
qupamtad,
qupamt,
tqurece,
tqurecn,
budamt,
vencamt,
tvpay,
nacpay,
pjctrno
from (
	select * 
	from bellerpdbf.dbo.pj_profit pp 
	where pp.pjctrno in ('A','F') and pp.accym ='' 
	and ((epjyn between '20' and '60') or (epjyn ='' and atype <>'8'))
) pp
left join (
 	select epjno,empno as pdno
 	from bellerpdbf.dbo.epjmail
 	where pmno='PD'
 ) e on pp.epjno =e.epjno 
 left join(
  	select epjno,empno as pmno
  	from tmp_epjemp
  	where sno='PM'
 )  e2 on pp.epjno =e2.epjno 
 left join(
  	select epjno,empno as pcno
  	from tmp_epjemp
  	where sno='PC'
 )  e3 on pp.epjno =e3.epjno 
 left join (
 	select  epjno,epjacc ,case sum(qupamt)  when 0 then 0.00 else ROUND( cast(sum(trecepay+nrecep1+nrecep2) /sum(qupamt) as decimal(12,5) ) * 100 , 2)  end as pleper
	from bellerpdbf.dbo.pj_cusrect 
	where epjyn between '20' and '60'
	group by epjno,epjacc 
 ) pc on pc.epjacc =pp.epjacc and pp.epjno =pc.epjno
left join (
  	select 
	accasno,epjno
	,case sum(vencamt) when 0 then 0.00 else  ROUND( cast(sum(venpay)/sum(vencamt) as decimal(12,5) ) * 100 , 2)  end as venpayper
	from bellerpdbf.dbo.pj_ven
	where pjctrno in ('A','F') 
	and ((epjyn between '20' and '60') or epjyn ='')
	group by accasno ,epjno
 ) pv on pp.epjacc =pv.accasno and pp.epjno =pv.epjno
 left join (
 	select epjacc,epjno,sum(recamt)- sum(payamt)  as difamt
  	from bellerpdbf.dbo.l_billepjrate	
  	where (cusvenno like 'VT%' OR cusvenno LIKE 'VF%') 
  	and epjyn between '20' and '60'
  	group by epjacc,epjno 
 ) fb on fb.epjacc =pp.epjacc and pp.epjno =fb.epjno;


/* 專案付款明細 */
-- CREATE VIEW dbo.pjpay_detail
-- --動態抓取最近一年的資料
-- AS select pv.symno,pv.pbatchno,a.pbatchna ,pv.epjacc,pv.epjno,pv.venno,pv.copname ,pv.venna,pv.payrmk
-- 	,pv.vencamt,pv.tvenpay,pv.balance,pv.npay,pv.ppay,pv.hpay,pv.dpay,pv.venpay
-- 	,pv.unkey --unkey 排序用，不可刪
-- 	,a.pjctrno -- 疑似判斷哪間公司
-- 	,pv.po -- 判斷是項目還是說明
--  from bellerpdbf.dbo.pj_venpay pv 
--  left join  bellerpdbf.dbo.apbatch a on a.pbatchno =pv.pbatchno  
--  where pv.symno >convert(varchar, DATEADD(month, -13, GETDATE()) , 112) and a.pjctrno in ('A','F');


/* 發包% */
CREATE VIEW dbo.v_epjvenbud
AS select *
 from bellerpdbf.dbo.v_epjvenbud ve;


/* email */
CREATE VIEW dbo.email
AS 
select 
cusvenno as venno,cusvenna as venna,email,faxno
 from bellerpdbf.dbo.p_bven pb2
 where cusvenno like 'VT%' OR cusvenno LIKE 'VF%'
 and stopyn = ''





 /* 專案付款明細 */
/**
 * 因user提供參考的view表(pj_vnepay)中，缺少_unkey欄位，laravel倒資料時會因條件不足而將說明刪除
 * 這些是說明->垃圾清運:扣清潔分攤1.5%
   		  ->水電分攤:扣水電分攤1%
 * 經考慮直接抓取來源表，並添加_unkey欄位
 */
CREATE VIEW dbo.pjpay_detail
--動態抓取最近一年的資料
AS SELECT
  tmp_epjvenpaygui.symno,
  tmp_epjvenpaygui.pbatchno,
  a.pbatchna,
  tmp_epjvenpaygui.venno,
  ISNULL(bellerpdbf.dbo.bcusvenemp.cusvenna, ' ') AS venna,
  ISNULL(bellerpdbf.dbo.bcusvenemp.copname, ' ') AS copname,
  ISNULL(bellerpdbf.dbo.bcusvenemp.faxno, ' ') AS faxno,
  tmp_epjvenpaygui.epjno,
  ISNULL(bellerpdbf.dbo.bepj_n.epjacc, tmp_epjvenpaygui.epjno) AS epjacc,
  ISNULL(bellerpdbf.dbo.bepj_n.epjna, ' ') AS epjna,
  tmp_epjvenpaygui.copno,
  ISNULL(bellerpdbf.dbo.fcompany.copna, tmp_epjvenpaygui.copno) AS copna,
  CASE
    WHEN po = 'P' THEN ISNULL(bellerpdbf.dbo.t_pjvenbtpay.vencamt, 0)
    ELSE 0
  END AS vencamt,
  CASE
    WHEN po = 'P' AND ISNULL(bellerpdbf.dbo.t_pjvenbtpay.vencamt, 0) <> 0  
        THEN tvenpay - (npay + ppay + hpay)
    ELSE 0
  END AS tvenpay,
-- , CASE WHEN tvenpay <> 0 
-- 		THEN balance + npay + ppay + hpay
-- 		ELSE balance
-- 	END AS balance
-- 與下方balance 同等
CASE when 
        CASE
            WHEN po = 'P' AND ISNULL(bellerpdbf.dbo.t_pjvenbtpay.vencamt, 0) <> 0  
                THEN tvenpay - (npay + ppay + hpay)
            ELSE 0
        END <> 0
    then
         CASE
            WHEN po = 'P' THEN ISNULL(
            bellerpdbf.dbo.t_pjvenbtpay.vencamt - bellerpdbf.dbo.t_pjvenbtpay.tvenpay,
            0
            )
            ELSE 0
        END  + npay + ppay + hpay
  ELSE
    CASE
      WHEN po = 'P' THEN ISNULL(
        bellerpdbf.dbo.t_pjvenbtpay.vencamt - bellerpdbf.dbo.t_pjvenbtpay.tvenpay,
        0
      )
      ELSE 0
    END
  END AS balance,
  tmp_epjvenpaygui.npay,
  tmp_epjvenpaygui.ppay,
  tmp_epjvenpaygui.hpay,
  tmp_epjvenpaygui.dpay,
  tmp_epjvenpaygui.npay + tmp_epjvenpaygui.ppay + tmp_epjvenpaygui.hpay + tmp_epjvenpaygui.dpay AS venpay,
  tmp_epjvenpaygui.invtax,
  tmp_epjvenpaygui.payrmk,
  ISNULL(bellerpdbf.dbo.bepj_n.epjname, ' ') AS epjname,
  tmp_epjvenpaygui.unkey,
   tmp_epjvenpaygui._unkey,
  CASE WHEN tmp_epjvenpaygui.bno = ' ' 
		THEN 'N'
		ELSE 'Y'
	END AS bnoyn,
  tmp_epjvenpaygui.po,
  tmp_epjvenpaygui.pb12,
  tmp_epjvenpaygui.pjctrno
FROM
  (
    SELECT
      symno,
      pbatchno,
      venno,
      epjno,
      unkey,
      0  AS _unkey,
      copno,
      npay,
      ppay,
      hpay,
      dpay,
      cast(0 AS int) AS invtax,
      payrmk AS payrmk,
      'P' AS po,
      bno,
      pb12,
      pjctrno
    FROM
     bellerpdbf.dbo.epjvenpay
    where symno >convert(varchar, DATEADD(month, -13, GETDATE()) , 112)
    UNION ALL
    SELECT
      bellerpdbf.dbo.epjvenpd.symno,
      bellerpdbf.dbo.epjvenpd.pbatchno,
      bellerpdbf.dbo.epjvenpd.venno,
      bellerpdbf.dbo.epjvenpd.epjno,
      bellerpdbf.dbo.epjvenpd.unkey,
      bellerpdbf.dbo.epjvenpd.[_unkey] ,
      '',
      0 AS npay,
      0 AS ppay,
      0 AS hpay,
      0 AS dpay,
      CAST(0 AS int) AS invtax,
      RTRIM(ISNULL(bellerpdbf.dbo.adac.dacna, '')) + ':' + LTRIM(bellerpdbf.dbo.epjvenpd.docrmk) AS payrmk,
      'D' AS po,
      '',
      bellerpdbf.dbo.epjvenpd.pb12,
      bellerpdbf.dbo.epjvenpd.pjctrno
    FROM
      bellerpdbf.dbo.epjvenpd
      LEFT OUTER JOIN bellerpdbf.dbo.adac ON bellerpdbf.dbo.epjvenpd.dacno = bellerpdbf.dbo.adac.dacno
    where symno >convert(varchar, DATEADD(month, -13, GETDATE()) , 112)
    UNION ALL
    SELECT
      symno,
      pbatchno,
      cusvenno AS venno,
      '進項稅額 ' AS epjno,
      ********* AS unkey,
      ********* AS _unkey,
      copno,
      0,
      0,
      0,
      0,
      invtax,
      ' ' + invdate + invoice + '$:' + LTRIM(CAST(invamt AS char(12))) AS payrmk,
      'G' AS po,
      ' ' AS bno,
      pb12,
      pjctrno
    FROM
      bellerpdbf.dbo.guitaxd
    WHERE
      (guik12 IN ('V', 'F', '3')) and symno >convert(varchar, DATEADD(month, -13, GETDATE()) , 112)
  )tmp_epjvenpaygui
LEFT OUTER JOIN bellerpdbf.dbo.fcompany ON tmp_epjvenpaygui.copno = bellerpdbf.dbo.fcompany.copno
LEFT OUTER JOIN bellerpdbf.dbo.bepj_n ON tmp_epjvenpaygui.epjno = bellerpdbf.dbo.bepj_n.epjno
LEFT OUTER JOIN bellerpdbf.dbo.bcusvenemp ON tmp_epjvenpaygui.venno = bellerpdbf.dbo.bcusvenemp.cusvenno
LEFT OUTER JOIN bellerpdbf.dbo.t_pjvenbtpay ON tmp_epjvenpaygui.unkey = bellerpdbf.dbo.t_pjvenbtpay.unkey AND tmp_epjvenpaygui.epjno = bellerpdbf.dbo.t_pjvenbtpay.epjno
LEFT JOIN  bellerpdbf.dbo.apbatch a on a.pbatchno =tmp_epjvenpaygui.pbatchno  
where a.pjctrno in ('A','F') ;