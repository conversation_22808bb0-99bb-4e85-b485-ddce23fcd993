<?php

declare(strict_types=1);

namespace App\Modules\acc\Services;
use App\Modules\acc\models\Employee;
use App\Modules\acc\models\PerfDepReview;
use App\Traits\CompanyTrait;
use Illuminate\Support\Facades\Cache;

class EmployeeService
{
    use CompanyTrait;

    /**
     * 快取得員工資料的集合. (靠 deleted_at 去把離職員工排除掉).
     * @return \Illuminate\Database\Eloquent\Collection<Employee>
     */
    public function getEmployeesCache()
    {
        $data = Cache::get('employees');
        if (! empty($data)) {
            return $data;
        }

        $data = Employee::query()
            ->join('org_unit_members', function ($it) {
                $it->on('org_unit_members.employee_id', 'employees.id');
                $it->whereNull('org_unit_members.deleted_at');
            })
            ->join('org_units', function ($it) {
                $it->on('org_units.id', 'org_unit_members.org_unit_id');
                $it->whereNull('org_units.deleted_at');
            })
            ->get([
                'employees.id',
                'employees.company_id as companyId',
                'employees.payload->name as name',
                'employees.payload->employee_number as number',
                'employees.payload->rank as rank',
                'employees.payload->job_title as jobTitle',
                'org_units.payload->code as code',
                'org_units.payload->name as orgName',
                'org_unit_members.employee_id',
                'org_unit_members.org_unit_id',
            ])
            ->toBase()
            ->unique(function ($it) {
                return $it->employee_id . '-' . $it->company_id;
            })
            ->transform(function ($it) {
                unset($it->employee_id, $it->org_unit_id);

                $it->type = PerfDepReview::COMMON_TYPE_PM;
                if  (strpos($it->jobTitle, "工程") !== false) {
                    $it->type = PerfDepReview::COMMON_TYPE_CPM;
                }
                return $it;
            });

        Cache::put('employees', $data, 60 * 5);
        return $data;
    }

    /**
     * 取得對應公司的員工資訊.
     * @param string $company 公司名稱: RHQ, RHT, RHF, RHY, FuncSync
     * -- @return array example: [["id" => 7, "company_id" => 1, "name" => "嚴小萍", "number" => "A20101766", "rank" => 3]]
     * @return \Illuminate\Support\Collection
     */
    public function getEmployees(string $company)
    {
        $companyId = $this->getCompanyId($company);

        return $this->getEmployeesCache()
            ->where('companyId', $companyId)
            ->values();
    }

    public function getEmployeeByEmpno(?string $empno)
    {
        if (empty($empno)) {
            return null;
        }
        return $this->getEmployeesCache()->first(fn ($itEmployee) => $empno === $itEmployee->number, null);
    }

    /**
     * @param int $employeeId 員工 id > 資料庫存儲的自增 id
     * @return Employee|null 該 orm 為最上方存儲資料，請自行往上翻閱
     */
    public function getEmployeeById(int $employeeId)
    {
        if ($employeeId <= 0) {
            return null;
        }
        return $this->getEmployeesCache()->first(fn ($itEmployee) => $employeeId === $itEmployee->id, null);
    }
}
