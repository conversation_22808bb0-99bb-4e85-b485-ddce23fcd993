<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('perf_erp_projects', function (Blueprint $table) {
            $table->unsignedBigInteger('sponsor_employee_id')->nullable()->comment('績效獎金的發起人');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('perf_erp_projects', function (Blueprint $table) {
            $table->dropColumn('sponsor_employee_id');
        });
    }
};
