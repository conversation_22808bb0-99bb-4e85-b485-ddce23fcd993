<?php

namespace App\Modules\acc\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PayDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // dd($this->payload);
        return   [
            'id' => $this->id,
            'pkey' => $this->pkey,
            'epjacc' => $this->epjacc,
            'epjno' => $this->epjno,
            'symno' => $this->symno,
            'pbatchno' => $this->pbatchno,
            'pbatchna' => $this->pbatchna,
            'venno' => $this->payload->get('venno'),
            'venna' => $this->payload->get('venna'),
            'copname' => $this->payload->get('copname'),
            'payrmk' => $this->payload->get('payrmk'),
            'balance' => $this->payload->get('balance'),
            'dpay' => $this->payload->get('dpay'),
            'hpay' => $this->payload->get('hpay'),
            'npay' => $this->payload->get('npay'),
            'ppay' => $this->payload->get('ppay'),
            'tvenpay' => $this->payload->get('tvenpay'),
            'vencamt' => $this->payload->get('vencamt'),
            'venpay' => $this->payload->get('venpay'),
            'tax' => $this->payload->get('tax'),
            'pjctrno' => $this->payload->get('pjctrno'),
            'po' => $this->payload->get('po'),
        ];
    }
}
