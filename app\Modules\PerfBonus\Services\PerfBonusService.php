<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Services;

use App\Modules\PerfBonus\Handlers\SubmitPerfBonusWithADepHandler;
use App\Modules\PerfBonus\Handlers\SubmitPerfBonusWithADepHasTwoRolesHandler;
use App\Modules\PerfBonus\Models\PerfBonusSetting;
use App\Modules\PerfBonus\Models\PerfDepGroup;
use App\Modules\PerfBonus\Models\PerfDepReview;
use App\Modules\PerfBonus\Models\PerfEmployeePermission;
use App\Modules\PerfBonus\Models\PerfErpProject;
use App\Modules\PerfBonus\Models\PerfErpProjectEmployee;
use App\Modules\PerfBonus\Models\PerfLog;
use App\Modules\PerfBonus\Models\PerfPermission;
use App\Traits\CompanyTrait;
use App\Traits\ExcelColumnTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Throwable;

class PerfBonusService
{
    use ExcelColumnTrait, CompanyTrait;

    public function __construct(
        protected EmployeeService $employeeService,
        protected RhCcssCli $rhCcssCli,
    ) {
    }

    /**
     * 取得部門主管資料.
     *
     * @return Collection example: collect([['code' => 'B', 'empno' => 'B8905441', 'empna' => '王大明'], ...])
     */
    public function getDepManagerCache(): Collection
    {
        $data = Cache::get('dep_manager');
        if (! empty($data)) {
            return $data;
        }

        $data = PerfDepGroup::with(['employee:payload'])->whereRaw('gen_code = code')->get(['code', 'empno']);
        $mapData = $data->map(function ($it) {
            return [
                'code' => $it->code,
                'empno' => $it->empno,
                'empna' => $it->employee->payload['name'] ?? null,
            ];
        });
        Cache::put('dep_manager', $mapData, 60 * 5);
        return $mapData;
    }

    /**
     * 取得對應公司的部門代碼對應表(給前端顯示使用，績效獎金的上方可選部門的顯示).
     *
     * @param string $company 公司名稱: RHQ, RHT, RHF, RHY, FuncSync
     */
    public function getCode(string|int $company)
    {
        $companyId = $this->getCompanyId($company);
        $cacheKey = "dep_group_{$companyId}";

        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $dataset = PerfDepGroup::select(['display_name as displayName', 'code', 'sort'])
            ->where('company_id', $companyId)
            ->where('hidden', false)
            ->orderBy('sort', 'asc')
            ->get()
            ->toBase();

        Cache::put($cacheKey, $dataset, 60 * 5);
        return $dataset;
    }

    /**
     * 取得對應公司的部門代碼對應表(給前端顯示使用，績效獎金的上方可選部門的顯示).
     *
     * @param string $company 公司名稱: RHQ, RHT, RHF, RHY, FuncSync
     * @return array
     */
    public function getCodeName(string|int $company, string $code): string
    {
        $dataset = $this->getCode($company)->first(fn ($it) => $it->code == $code);
        return $dataset->displayName ?? '-';
    }

    /**
     * 以 id 去搜索對應專案的資訊.
     *
     * @param int $id
     * @return PerfErpProject
     */
    public function getProjectById(int $id)
    {
        return PerfErpProject::with(['reviews'])
            ->find($id);
    }

    /**
     * 取得專案列表.(給欲發行且高層的人使用，發送績效獎金).
     *
     * @param string $company 公司名稱: RHQ, RHT, RHF, RHY, FuncSync
     * @param array|null $filterCondition 查詢條件
     * @param bool $force 強制刷新
     * @return array
     */
    public function getProjects(string $company, array $filterCondition = [], bool $force = false)
    {
        $companyId = $this->getCompanyId($company);
        $queryBuilder = PerfErpProject::where('company_id', '=', $companyId);

        // 搜索條件
        // --------------------
        // 只顯示未發行的專案: 如果 $force 為 false 則就塞選出未發行的專案
        (! $force) && $queryBuilder->where('status', PerfErpProject::STATUS_NOT_APPLIED);
        isset($filterCondition['epjno']) && $queryBuilder->where('epjno', $filterCondition['epjno']);
        isset($filterCondition['closeYm']) && $queryBuilder->where('closeym', str_replace('/', '', $filterCondition['closeYm']));

        // 排序
        $queryBuilder->orderBy('epjno', 'desc');
        $queryBuilder->select([
            'id',
            'epjno',
            'accasno as accId',
            'closeym as closeYm',
            'project_name as projectName',
            'pc_empno as pcEmpno',
            'pc_code as pcCode',
            'pm_empno as pmEmpno',
            'pm_code as pmCode',
            'total_pay as totalPay',
            'status',
        ]);

        /** @var \Illuminate\Pagination\LengthAwarePaginator $paginationData */
        $paginationData = $queryBuilder->paginate(
            perPage: $this->perPageLimiter($filterCondition['perPage'] ?? null),
        );

        // 轉換 pc, pm code 變成系統使用的代號...
        $transformCodeMap = $this->getTransformCodeMapCache();
        $paginationData->getCollection()->transform(function ($it) use (&$transformCodeMap) {
            if ($it->pcCode) {
                $it->pcCode = $transformCodeMap[$it->pcCode];
            }

            if ($it->pmCode) {
                $it->pmCode = $transformCodeMap[$it->pmCode];
            }
            return $it;
        });

        return [
            'page' => $paginationData->currentPage(), // 當前頁數
            'totalPage' => $paginationData->lastPage(), // 總共頁數
            'data' => $paginationData->items(), // 資料集合
            'total' => $paginationData->total(), // 總共資料筆數
        ];
    }

    /**
     * 取得專案中有參與的部門代號.
     *
     * @param array $projects 專案代碼(epjno)
     * @return array example: ['202303068 ' => ['B', 'TD], '202303083' => ['CC', 'RB']]
     */
    public function getProjectOrgs(array $projects = []): array
    {
        return PerfErpProjectEmployee::leftJoin('perf_dep_groups', 'perf_erp_project_employee.perf_dep_code', '=', 'perf_dep_groups.code')
            ->whereIn('epjno', $projects)
            ->get(['epjno', 'gen_code', 'perf_erp_project_employee.empno'])
            // 過濾已經離職的員工
            ->filter(fn ($it) => $this->employeeService->getEmployeeByEmpno($it->empno))
            ->groupBy('epjno')
            ->transform(fn ($it) => $it->pluck('gen_code')->unique()->values())
            ->toArray();
    }

    /**
     * 發布分配給各單位並且有參與該專案的績效獎金給主管去評斷員工的績效，
     * 專案可能會有很多參與的單位，假定有一筆專案資料要發放，裡面涉及到的部門可能有多個，
     * 那就會有多個部門主管需要判斷員工的績效.
     *
     * @param array $postData projects orgs perf bonus data
     * @return bool 成功與否
     *
     * @throws Exception
     */
    public function appendPerfBonusToOrgs(array $postData = [], int $sponsorEmployeeId): void
    {
        if (empty($postData)) {
            throw new Exception('錯誤：傳入空的資料');
        }

        Log::channel('perf-bonus')->info('新增績效獎金發放資料: ' . json_encode($postData, JSON_UNESCAPED_UNICODE));
        $projectIds = Arr::pluck($postData, 'projectId');
        Log::channel('perf-bonus')->info('傳入要發放績效獎金的專案代號有: ' . implode(', ', $projectIds));

        // 確認每一筆是否都是唯一的專案代碼
        if (count($projectIds) !== count(array_unique($projectIds))) {
            throw new Exception('錯誤：專案代碼重複');
        }

        // 這邊確認多選資料中有無已經審核的資料
        $projectCollection = PerfErpProject::with(['employees'])
            ->whereIn('epjno', $projectIds)
            ->get()
            ->transform(function ($itQueryResult) {
                $itQueryResult->employees->transform(function ($itEmployee) { // 將 erp 的部門資料轉化成系統的部門代號
                    $itEmployee->trans_code = $this->getTransformCodeMapCache()[$itEmployee->perf_dep_code] ?? $itEmployee->perf_dep_code;
                    return $itEmployee;
                });
                return $itQueryResult;
            });

        // 如果已經有審核過的資料，則 clone 一份新的資訊。
        $projectCollection->each(fn ($it) => $this->projectCompleteAndClone($it->id, $it->epjno, $it->status));

        // 專案id 與 參與部門 的對照資訊
        $projectDepCodesMap = $projectCollection
            ->pluck('employees', 'epjno')
            ->map(fn ($itCollection, $epjno) => [
                'epjno' => $epjno,
                'codes' => $itCollection->pluck('trans_code')->unique(),
            ])
            ->values()
            ->pluck('codes', 'epjno')
            ->toArray();

        if (count($projectIds) !== $projectCollection->count()) {
            Log::channel('perf-bonus')->error('錯誤：選取要發放的專案有問題，有問題的專案編號為: ' . implode(', ', array_diff($projectIds, $projectCollection->pluck('epjno')->toArray())));
            // throw new Exception('選取要發放的專案有問題，有問題的專案編號為: ' . implode(', ', array_diff($projectIds, $projectCollection->pluck('epjno')->toArray())));
        }

        // 評鑑系統的資料
        $rhProjects = $this->rhCcssCli->getProjects(projectCompanyIdMap: $projectCollection->pluck('company_id', 'epjno')->toArray());
        // 把初審人員的員工編號取出來，用於下方檢查是否有無離職的員工
        $preliminaryReviewers = array_values(array_filter(array_unique(Arr::flatten(Arr::pluck($postData, 'organizations.*.preliminaryReviewers')))));

        DB::beginTransaction();
        foreach ($postData as [
            'projectId' => $projectErpId,
            'organizations' => $organizations,
        ]) {
            // 評鑑系統裡面的資料
            $rhData = $rhProjects[$projectErpId]['employees'] ?? [];
            if (empty($rhData)) {
                throw new Exception('評鑑系統尚未有資料 ! ... ' . $projectErpId);
            }

            // 取得對應專案有參與的部門 example: ['B', 'TD']
            $supportDeps = $projectDepCodesMap[$projectErpId];
            // project model 的實例，用於更新專案的狀態
            $projectModel = $projectCollection->first(fn ($it) => $it->epjno === $projectErpId);

            foreach ($organizations as [
                'code' => $code, // 部門代號
                'amount' => $amount, // 實際金額
                'suggestAmount' => $suggestAmount, // 推薦金額 = 0.7 * 實際金額
                'preliminaryReviewers' => $preliminaryReviewers // 評核人的員工編號(s) 複數， 原因: 關於同部門可能擁有 工程人員 或是 設計人員 的問題存在
            ]) {
                // 績效額度無值或為0時，不核定，結果查詢查無該專案或該部門
                if (empty($amount)) {
                    continue;
                }

                // 如果專案參與部門沒有找到的話，則略過設定的部門資訊
                if (! in_array($code, $supportDeps)) {
                    Log::channel('perf-bonus')->error(sprintf('專案代碼: %s 預核發部門代碼: %s, 但可配給部門只有: %s', $projectErpId, $code, implode('|', $supportDeps)));
                    continue;
                }

                if ($suggestAmount > $amount * 0.7) {
                    Log::channel('perf-bonus')->warning('推薦金額高於實際金額七成； 專案代碼: ' . $projectErpId . ' - 設定金額為: ' . $amount . '、推薦金額為: ' . $suggestAmount);
                }

                if (count($preliminaryReviewers) > 2) {
                    Log::channel('perf-bonus')->error('設定有問題 專案代碼: ' . $projectErpId . ' - 初核人員不可大於兩個');
                    throw new Exception('設定有問題 專案代碼: ' . $projectErpId . ' - 初核人員不可大於兩個');
                }

                // 合併專案參與人員資料
                $projectModel->employees->transform(function ($itEmp) use (&$rhData) {
                    $rh = Arr::first($rhData, fn ($it) => $it['empno'] === $itEmp->empno);
                    $itEmp->question_extra = null;

                    switch (true) {
                        case $itEmp->sno === 'PC':
                            $itEmp->member_type = '0'; // 工程
                            $itEmp->common_type = PerfDepReview::COMMON_TYPE_CPM;
                            $itEmp->show_title = 'CPM(工程經理)';
                            break;
                        case $itEmp->sno === 'PM':
                            $itEmp->member_type = '1'; // 設計
                            $itEmp->common_type = PerfDepReview::COMMON_TYPE_PM;
                            $itEmp->show_title = 'PM(專案經理)';
                            break;
                        case ! empty($rh):
                            $itEmp->member_type = $rh['memberType'];
                            $itEmp->common_type = $rh['commonType'];
                            $itEmp->show_title = $rh['showTitle'];
                            $itEmp->question_extra = json_encode($rh, JSON_UNESCAPED_UNICODE);
                            // no break
                        default:
                            $itEmp->member_type = $rh['memberType'] ?? '1';
                            $itEmp->common_type = $rh['commonType'] ?? PerfDepReview::COMMON_TYPE_PM;
                            $itEmp->show_title = str_starts_with($itEmp->sno, 'C') || str_starts_with($itEmp->sno, 'S') ? '工程師' : '設計師';
                    }

                    return $itEmp;
                });

                // 工程人員數量
                $projectEngineers = $projectModel->employees
                    ->filter(fn ($it) => str_starts_with($it->sno, 'C') || str_starts_with($it->sno, 'S') || in_array($it->sno, ['PC', 'PA']))
                    ->filter(fn ($it) => $it->trans_code === $code);
                $engineerCount = $projectEngineers->count();
                // 設計人員數量
                $projectDesigners = $projectModel->employees
                    ->filter(fn ($it) => str_starts_with($it->sno, 'D') || in_array($it->sno, ['PM', 'PP']))
                    ->filter(fn ($it) => $it->trans_code === $code);
                $designerCount = $projectDesigners->count();

                switch (true) {
                    case $engineerCount > 0 && $designerCount > 0:
                        SubmitPerfBonusWithADepHasTwoRolesHandler::handle(
                            code: $code,
                            amount: $amount,
                            suggestionAmount: $suggestAmount,
                            reviewers: $preliminaryReviewers,
                            projectModelId: $projectModel->id,
                            sponsorEmployeeId: $sponsorEmployeeId,
                            projectEmployees: $projectModel->employees->filter(fn ($it) => $it->trans_code === $code)->toArray(),
                            closeym: $projectModel->closeym,
                        );
                        break;
                    case $engineerCount > 0 && $designerCount == 0:
                        SubmitPerfBonusWithADepHandler::handle(
                            code: $code,
                            amount: $amount,
                            suggestionAmount: $suggestAmount,
                            reviewers: $preliminaryReviewers,
                            projectModelId: $projectModel->id,
                            sponsorEmployeeId: $sponsorEmployeeId,
                            projectEmployees: $projectModel->employees->filter(fn ($it) => $it->trans_code === $code)->toArray(),
                            commonType: PerfDepReview::COMMON_TYPE_CPM,
                            closeym: $projectModel->closeym,
                        );
                        break;
                    case $engineerCount == 0 && $designerCount > 0:
                        SubmitPerfBonusWithADepHandler::handle(
                            code: $code,
                            amount: $amount,
                            suggestionAmount: $suggestAmount,
                            reviewers: $preliminaryReviewers,
                            projectModelId: $projectModel->id,
                            sponsorEmployeeId: $sponsorEmployeeId,
                            projectEmployees: $projectModel->employees->filter(fn ($it) => $it->trans_code === $code)->toArray(),
                            commonType: PerfDepReview::COMMON_TYPE_PM,
                            closeym: $projectModel->closeym,
                        );
                        break;
                }
            }
        }

        DB::commit();
    }

    protected function projectCompleteAndClone(int $projectId, string $epjno, string $projectStatus)
    {
        if ($projectStatus !== PerfErpProject::STATUS_COMPLETED) {
            return ;
        }

        // 需要知道最新的 epjno 是多少
        $latest = PerfErpProject::where('epjno', 'like', "{$epjno}%")->latest()->first()->epjno;

        $latestNum = 1;
        if (preg_match('/.+_(?<num>\d+)/', $latest, $matches)) {
            $latestNum = $matches['num'] + 1;
        }
        $review = PerfDepReview::where('prj_id', $projectId)->first();
        $project = PerfErpProject::with(['reviews'])->find($projectId);
        $newProject = $project->replicate();
        $newProject->epjno = sprintf('%s_%d', $epjno, $latestNum);
        $newProject->closeym = $review->closeym ?? '';
        $newProject->created_at = now();
        $newProject->save();
        $project->update(['status' => PerfErpProject::STATUS_IN_PROGRESS]);

        PerfDepReview::where('prj_id', $projectId)->update(['prj_id'=> $newProject->id]);
    }

    /**
     * 取得正在等待各部門主管的審核資料. (pagination).
     *
     * @param string $compony 可傳入 id 或是 字串 example: RHY, RHT, RHQ, RHF, FuncSync, or 1, 2, 3, 4, 5
     * @param array $filterCondition 搜索條件，目前支援 id, epjno
     */
    public function getProcessing(string $company, array $filterCondition = [])
    {
        $companyId = $this->getCompanyId($company);

        /** @var \Illuminate\Database\Eloquent\Builder $queryBuilder */
        $queryBuilder = PerfErpProject::with([
                // 審核事件: 初/覆核人員的審核事件
                'reviews' => function ($reviewerQuery) {
                    $reviewerQuery->with([
                        // 審核的詳細資訊: 被審核人員展開
                        'logs' => function ($logQuery) {
                            $logQuery->select([
                                'review_id',
                                'sno',
                                'empno',
                                'empna',
                                'is_new as isNew',
                                'first_reviewer_payload as firstReviewerPayload',
                                'finally_reviewer_payload as finallyReviewerPayload',
                                'member_type as memberType',
                                'question_extra as questionExtra',
                                'show_title as showTitle',
                            ]);
                        }])->select([
                            'code',
                            'status',
                            'amount',
                            'suggestion_amount',
                            'first_empno',
                            'first_finished_at as firstFinishedAt',
                            'second_empno',
                            'second_finished_at as secondFinishedAt',
                            'common_type as commonType',
                            'prj_id',
                            'id',
                        ]);
                },
            ])
            ->where(function ($q) use ($companyId, &$filterCondition) {
                $q->where('status', PerfErpProject::STATUS_IN_PROGRESS);
                $q->where('company_id', $companyId);

                if (! empty($filterCondition['id'])) {
                    $q->where('id', $filterCondition['id']);
                }

                if (! empty($filterCondition['epjno'])) {
                    $q->where('epjno', $filterCondition['epjno']);
                }
            });

        $queryBuilder->select([
            'id',
            'epjno',
            'company_id as companyId',
            'accasno as accId',
            'pc_empno as pcEmpno',
            'pm_empno as pmEmpno',
            'project_name as projectName',
            'total_pay as totalPay',
            'closeym as closeYm',
            'status',
        ]);

        /** @var \Illuminate\Pagination\LengthAwarePaginator $paginationData */
        $paginationData = $queryBuilder->paginate(
            perPage: $this->perPageLimiter($filterCondition['perPage'] ?? null),
        );

        // 資料轉換
        $result = $paginationData->getCollection()->transform(function ($it) use ($company) {
            $it->reviews->transform(function ($itReview) use ($company) {
                $itReview->prjId = $itReview->prj_id;
                $itReview->codeName = $this->getCodeName($company, $itReview->code);
                $itReview->firstReviewer = $this->employeeService->getEmployeeByEmpno($itReview->first_empno);
                $itReview->secondReviewer = $this->employeeService->getEmployeeByEmpno($itReview->second_empno);
                $itReview->suggestionAmount = $itReview->suggestion_amount;

                // 初核 覆核 完成
                switch (true) {
                    case empty($itReview->firstFinishedAt):
                        $itReview->showStatus = 1;
                        break;
                    case empty($itReview->secondFinishedAt):
                        $itReview->showStatus = 2;
                        break;
                    default:
                        $itReview->showStatus = 0;
                        break;
                }

                if ($itReview->logs) {
                    $itReview->logs->transform(function ($singleLog) {
                        $singleLog->reviewId = $singleLog->review_id;
                        if ($singleLog->questionExtra) {
                            $singleLog->questionExtra = json_decode($singleLog->questionExtra, true);
                        }

                        unset($singleLog->review_id);
                        return $singleLog;
                    });
                }

                unset(
                    $itReview->prj_id,
                    $itReview->suggestion_amount,
                    $itReview->first_empno,
                    $itReview->second_empno
                );
                return $itReview;
            });
            $it->totalPay = floatval($it->totalPay);
            $it->amount = array_sum($it->reviews->pluck('amount')->toArray());
            $it->suggestionAmount = array_sum($it->reviews->pluck('suggestionAmount')->toArray());
            return $it;
        });

        return [
            'page' => $paginationData->currentPage(), // 當前頁數
            'totalPage' => $paginationData->lastPage(), // 總共頁數
            'data' => $result, // 資料集合
            'total' => $paginationData->total(), // 總共資料筆數
        ];
    }

    /**
     * 取消績效獎金 只要取消則專案所有的部門都會取消.
     *
     * @param string $company 公司名稱: RHQ, RHT, RHF, RHY, FuncSync
     * @param array $epjnoArr 專案代碼
     * @return array
     */
    public function cancelPerfBonus(string $company, array $epjnoArr): array
    {
        $companyId = $this->getCompanyId($company);
        $tmpData = [];

        // 進行刪除作業時，不希望有人在異動這些資料；因此進行整個鎖定。
        DB::beginTransaction();
        try {
            // 進行中的專案才可以做取消
            $projectInstance = PerfErpProject::whereIn('epjno', $epjnoArr)
                ->where('company_id', $companyId)
                ->where('status', PerfErpProject::STATUS_IN_PROGRESS)
                ->lockForUpdate()
                ->get();
            $projectReviews = PerfDepReview::whereIn('prj_id', $projectInstance->pluck('id'))->lockForUpdate()->get();
            $projectLogs = PerfLog::whereIn('review_id', $projectReviews->pluck('id'))->lockForUpdate()->get();

            $projectInstance->each(fn ($it) => $it->update([
                'status' => PerfErpProject::STATUS_NOT_APPLIED,
                'sponsor_employee_id' => null,
            ]));

            $tmpData['prj_id'] = $projectInstance->map(function ($it) {
                $it->update([
                    'status' => PerfErpProject::STATUS_NOT_APPLIED,
                    'sponsor_employee_id' => null,
                ]);
                return $it->id;
            });
            $tmpData['review_id'] = $projectReviews->map(function ($it) {
                $it->delete();
                return $it;
            });
            $tmpData['logs_id'] = $projectLogs->map(function ($it) {
                $it->delete();
                return $it;
            });

            DB::commit();
        } catch (Throwable $th) {
            DB::rollBack();
            Log::channel('perf-bonus')->info('刪除失敗: ' . $th->getMessage());
            throw $th;
        }

        return $tmpData;
    }

    /**
     * 取得以"員工"為單位的績效獎金資料.
     *
     * @TODO 目前沒有遇到瓶頸，但是後續如果要重構的話；需要將這個方法拆成兩個，一個是取得列表資料，一個是取得單一員工的資料
     *
     * @param array $filterCondition 塞選條件 Condition
     * @param bool $isExport 是否為匯出
     * @return array|\Illuminate\Database\Eloquent\Collection|static[]
     */
    public function getEmployeesResult(array $filterCondition = [])
    {
        $queryBuilder = DB::table('perf_logs')
            ->join('perf_dep_reviews', fn ($q) => $q->on('perf_logs.review_id', '=', 'perf_dep_reviews.id')->whereNull('perf_dep_reviews.deleted_at'))
            ->join('perf_erp_projects', 'perf_dep_reviews.prj_id', '=', 'perf_erp_projects.id')
            ->whereNull('perf_logs.deleted_at')
            ->where('perf_erp_projects.status', PerfErpProject::STATUS_COMPLETED)
            ->select([
                'perf_logs.id as logId',
                'perf_logs.empno',
                'perf_logs.empna',
                'perf_logs.is_new as isNew',
                'perf_erp_projects.epjno',
                'perf_erp_projects.project_name as projectName',
                'perf_erp_projects.accasno as accId',
                'perf_erp_projects.closeym as closeYm',
                DB::raw("case when perf_logs.finally_reviewer_payload->>1 is null then '0' else perf_logs.finally_reviewer_payload->1 end as suggestionAmount"),
                DB::raw("case when perf_logs.finally_reviewer_payload->>2 is null then '0' else perf_logs.finally_reviewer_payload->2 end as finalAmount"),
            ]);

        // 搜索條件
        isset($filterCondition['closeYm']) && $queryBuilder->where('perf_erp_projects.closeym', str_replace('/', '', $filterCondition['closeYm']));
        isset($filterCondition['empno']) && $queryBuilder->whereIn('perf_logs.empno', explode(',', $filterCondition['empno']));

        // 先以人為單位去將他的專案資料分類
        $lastDataset = $queryBuilder->get()->groupBy('empno')->transform(function ($empnoData) {
            // 之後再以專案結束時間為單位去做分類
            return $empnoData->groupBy('closeYm')->transform(function ($closeYmData) {
                return [
                    'closeYm' => $closeYmData->first()->closeYm,
                    'empno' => $closeYmData->first()->empno,
                    'empna' => $closeYmData->first()->empna,
                    'totalAmount' => $closeYmData->sum('finalamount'),
                    'projectCount' => $closeYmData->count(),
                    'info' => $this->employeeService->getEmployeeByEmpno($closeYmData->first()->empno),
                    'projects' => $closeYmData->map(function ($it) {
                        $it->suggestionAmount = $it->suggestionamount;
                        $it->finalAmount = $it->finalamount;
                        unset($it->empno, $it->empna, $it->suggestionamount, $it->finalamount);
                        return $it;
                    }),
                ];
            })->values();
        })->values();

        return $lastDataset;
    }

    /**
     * 匯出以"員工"為單位的績效獎金資料.
     *
     * @param array $filterCondition 塞選條件
     * @return \PhpOffice\PhpSpreadsheet\Writer\IWriter
     */
    public function exportByEmployees(array $filterCondition = [])
    {
        $spreadsheet = new Spreadsheet();
        $spreadsheet
            ->getProperties()
            ->setCreator('FDMC-ACCAP')
            ->setLastModifiedBy('SYSTEM')
            ->setTitle('匯出資料 - 專案')
            ->setSubject('Office 2007 XLSX Test Document')
            ->setDescription('Test document for Office 2007 XLSX, generated using PHP classes.')
            ->setKeywords('office 2007 openxml php')
            ->setCategory('result file');

        $dataset = $this->getEmployeesResult(
            filterCondition: $filterCondition,
        );
        $maxColsNum = $dataset->map(function ($employeeData) {
            return $employeeData->max('projectCount');
        })->max();

        $spreadsheet
            ->setActiveSheetIndex(0)
            ->setCellValue('A1', '績效年月')
            ->setCellValue('B1', '人員')
            ->setCellValue('C1', '部門')
            ->setCellValue('D1', '職稱')
            ->setCellValue('E1', '總績效獎金')
            ->setCellValue('F1', '績效調整')
            ->setCellValue('G1', '實發績效');

        for ($i = 0; $i < $maxColsNum; ++$i) {
            $spreadsheet->setActiveSheetIndex(0)->setCellValue($this->calculateColumn($i * 2 + 7, 0), sprintf('專案%d', $i + 1));
            $spreadsheet->setActiveSheetIndex(0)->setCellValue($this->calculateColumn($i * 2 + 8, 0), sprintf('金額%d', $i + 1));
        }

        $beginLine = 2;
        $dataset->each(function ($employeeData) use (&$spreadsheet, &$beginLine) {
            $employeeData->each(function ($closeYmData) use (&$spreadsheet, &$beginLine) {
                $formula = sprintf('=SUM(%s,%s)', "E{$beginLine}", "F{$beginLine}");
                $employeeInfo = $closeYmData['info'];

                $spreadsheet->setActiveSheetIndex(0)
                    ->setCellValue("A{$beginLine}", $closeYmData['closeYm'])
                    ->setCellValue("B{$beginLine}", $employeeInfo->name ?? '-')
                    ->setCellValue("C{$beginLine}", $employeeInfo->orgName ?? '-')
                    ->setCellValue("D{$beginLine}", $employeeInfo->jobTitle ?? '-')
                    ->setCellValue("E{$beginLine}", $closeYmData['totalAmount'])
                    ->setCellValue("F{$beginLine}", '-')
                    ->setCellValue("G{$beginLine}", $formula);

                if ($closeYmData['projects'] instanceof Collection) {
                    $closeYmData['projects']->each(function ($data, $idx) use (&$spreadsheet, $beginLine) {
                        $spreadsheet->setActiveSheetIndex(0)
                            ->setCellValue($this->calculateColumn((int) $idx * 2 + 7, $beginLine - 1), $data->projectName ?? '-')
                            ->setCellValue($this->calculateColumn((int) $idx * 2 + 8, $beginLine - 1), $data->finalAmount ?? '-');
                    });
                }
                ++$beginLine;
            });
        });

        $spreadsheet->getActiveSheet()->setTitle('根據人員匯出');
        $spreadsheet->setActiveSheetIndex(0);
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        return $writer;
    }

    /**
     * @TODO 這邊要重購，太醜
     * 取得以"專案"為單位的績效獎金資料.
     *
     * @param array $filterCondition 塞選條件
     * @param bool $isExport 是否為匯出
     * @return array|\Illuminate\Database\Eloquent\Collection|static[]
     */
    public function getProjectsResult(array $filterCondition = [], bool $isExport = false)
    {
        $queryBuilder = PerfErpProject::with(['reviews' => function ($q) {
            $q->with(['logs' => function ($q) {
                $q->select([
                    'id',
                    'review_id',
                    'sno',
                    'empno',
                    'empna',
                    'is_new as isNew',
                    'first_reviewer_payload as firstReviewerPayload',
                    'finally_reviewer_payload as finallyReviewerPayload',
                    DB::raw('finally_reviewer_payload->>1 as finalAmount'),
                    'member_type as memberType',
                    'show_title as showTitle',
                ]);
            }])->select([
                'code',
                'status',
                'amount',
                'suggestion_amount as suggestionAmount',
                'first_empno as firstEmpno',
                'first_finished_at as firstFinishedAt',
                'second_empno as secondEmpno',
                'second_finished_at as secondFinishedAt',
                'common_type as commonType',
                'prj_id',
                'id',
            ]);
        }]);
        $queryBuilder->where('status', PerfErpProject::STATUS_COMPLETED);
        isset($filterCondition['epjno']) && $queryBuilder->where('epjno', 'like', $filterCondition['epjno'] . '%');
        isset($filterCondition['closeYm']) && $queryBuilder->where('closeym', str_replace('/', '', $filterCondition['closeYm']));
        $queryBuilder->select([
            'id',
            'epjno',
            'company_id as companyId',
            'accasno as accId',
            'project_name as projectName',
            'total_pay as totalPay',
            'closeym as closeYm',
        ]);

        if ($isExport) {
            $dataset = $queryBuilder->get();
        } else {
            /** @var \Illuminate\Pagination\LengthAwarePaginator $paginationData */
            $paginationData = $queryBuilder->paginate(
                perPage: $this->perPageLimiter($filterCondition['perPage'] ?? null),
            );
            $dataset = $paginationData->getCollection();
        }

        // 資料整理
        $dataset->transform(function ($itProject) {
            $companyId = $itProject->companyId;
            $itProject->reviews->transform(function ($itReview) use ($companyId) {
                $itReview->prjId = $itReview->prj_id;
                $itReview->codeName = $this->getCodeName($companyId, $itReview->code);
                $itReview->firstEmpna = $this->employeeService->getEmployeeByEmpno($itReview->firstEmpno)->name ?? '-';
                $itReview->secondEmpna = $this->employeeService->getEmployeeByEmpno($itReview->secondEmpno)->name ?? '-';

                $itReview->logs->transform(function ($itLog) {
                    $itLog->reviewId = $itLog->review_id;
                    (! empty($itLog->firstReviewerPayload)) && $itLog->firstReviewerPayload = json_decode($itLog->firstReviewerPayload, true);
                    (! empty($itLog->finallyReviewerPayload)) && $itLog->finallyReviewerPayload = json_decode($itLog->finallyReviewerPayload, true);
                    unset($itLog->review_id);
                    return $itLog;
                });
                unset($itReview->prj_id);
                return $itReview;
            });
            return $itProject;
        });

        if ($isExport) {
            return $dataset;
        }

        return [
            'page' => $paginationData->currentPage(), // 當前頁數
            'totalPage' => $paginationData->lastPage(), // 總共頁數
            'data' => $dataset, // 資料集合
            'total' => $paginationData->total(), // 總共資料筆數
        ];
    }

    /**
     * 匯出以"專案"為單位的績效獎金資料.
     *
     * @param array $filterCondition 塞選條件
     * @return \PhpOffice\PhpSpreadsheet\Writer\IWriter
     */
    public function exportByProjects(array $filterCondition = [])
    {
        $spreadsheet = new Spreadsheet();
        $spreadsheet
            ->getProperties()
            ->setCreator('FDMC-ACCAP')
            ->setLastModifiedBy('SYSTEM')
            ->setTitle('匯出資料 - 專案')
            ->setSubject('Office 2007 XLSX Test Document')
            ->setDescription('Test document for Office 2007 XLSX, generated using PHP classes.')
            ->setKeywords('office 2007 openxml php')
            ->setCategory('result file');

        $dataset = $this->getProjectsResult(
            filterCondition: $filterCondition,
            isExport: true,
        );

        // 抓到最多人員參予的專案
        $maxColsNum = $dataset->map(function ($itProject) {
            return $itProject->reviews->map(function ($itReview) {
                return $itReview->logs->count();
            })->sum();
        })->max();

        // 人員n 人員n核定金額
        $spreadsheet
            ->setActiveSheetIndex(0)
            ->setCellValue('A1', '專案代號')
            ->setCellValue('B1', '會計代號')
            ->setCellValue('C1', '專案簡稱')
            ->setCellValue('D1', '合約總價')
            ->setCellValue('E1', '績效年月')
            ->setCellValue('F1', '績效額度')
            ->setCellValue('G1', '建議額度')
            ->setCellValue('H1', '核定金額');

        for ($i = 0; $i < $maxColsNum; ++$i) {
            $spreadsheet->setActiveSheetIndex(0)->setCellValue($this->calculateColumn($i * 2 + 8, 0), sprintf('人員%d', $i + 1));
            $spreadsheet->setActiveSheetIndex(0)->setCellValue($this->calculateColumn($i * 2 + 9, 0), sprintf('人員%d核定金額', $i + 1));
        }

        // beginLine 是 excel 的紀錄指針，負責 offset 到對應的行數。
        $beginLine = 2;
        // 設定內容
        $dataset->each(function ($it) use (&$spreadsheet, &$beginLine) {
            $tmpAmount = '0';
            $tmpSuggestionAmount = '0';

            $it->reviews->each(function ($itReview) use (&$tmpAmount, &$tmpSuggestionAmount) {
                $tmpAmount = bcadd($tmpAmount, $itReview->amount);
                $tmpSuggestionAmount = bcadd($tmpSuggestionAmount, $itReview->suggestionAmount);
            });

            $spreadsheet->setActiveSheetIndex(0)
                ->setCellValue("A{$beginLine}", $it->epjno)
                ->setCellValue("B{$beginLine}", $it->accId)
                ->setCellValue("C{$beginLine}", $it->projectName)
                ->setCellValue("D{$beginLine}", $it->totalPay)
                ->setCellValue("E{$beginLine}", $it->closeYm)
                ->setCellValue("F{$beginLine}", $tmpAmount)
                ->setCellValue("G{$beginLine}", $tmpSuggestionAmount);

            $totalFinalAmount = 0;
            $tmpSheetIdx = 0;
            $it->reviews->each(function ($itReview) use (&$spreadsheet, &$beginLine, &$tmpSheetIdx, &$totalFinalAmount) {
                // 人員n 人員n核定金額
                $itReview->logs->each(function ($itLog) use (&$tmpSheetIdx, &$spreadsheet, &$beginLine, &$totalFinalAmount) {
                    ++$tmpSheetIdx;
                    $totalFinalAmount += intval($itLog->finalamount);
                    $spreadsheet->setActiveSheetIndex(0)
                        ->setCellValue($this->calculateColumn((int) $tmpSheetIdx * 2 + 6, $beginLine - 1), $itLog->empna)
                        ->setCellValue($this->calculateColumn((int) $tmpSheetIdx * 2 + 7, $beginLine - 1), $itLog->finallyReviewerPayload[1] ?? 0);
                });

                // 回填總金額
                $spreadsheet->setActiveSheetIndex(0)
                        ->setCellValue($this->calculateColumn(7, $beginLine - 1), $totalFinalAmount);
            });
            ++$beginLine;
        });

        // excel 欄位寬度調整
        $spreadsheet->setActiveSheetIndex(0)->getColumnDimension('A')->setWidth(10); // 績效年月 10
        $spreadsheet->setActiveSheetIndex(0)->getColumnDimension('B')->setWidth(10); // 會計代號 10
        $spreadsheet->setActiveSheetIndex(0)->getColumnDimension('C')->setWidth(13); // 專案代號 13
        $spreadsheet->setActiveSheetIndex(0)->getColumnDimension('D')->setWidth(15); // 專案簡稱 15
        $spreadsheet->setActiveSheetIndex(0)->getColumnDimension('E')->setWidth(10); // 績效年月 10
        $spreadsheet->setActiveSheetIndex(0)->getColumnDimension('F')->setWidth(10); // 績效額度 10
        $spreadsheet->setActiveSheetIndex(0)->getColumnDimension('G')->setWidth(10); // 建議額度 10
        $spreadsheet->getActiveSheet()->setTitle('根據專案匯出');
        $spreadsheet->setActiveSheetIndex(0);
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        return $writer;
    }

    /**
     * 更新績效獎金通知的相關設定. (該設定為email通知).
     *
     * @param string $company 公司代號或是 id
     * @param bool $status 狀態
     * @param string $text 通知文字
     * @param int $frequencyDay 通知頻率x天
     */
    public function notification(
        string $company,
        bool $status,
        string $text = null,
        int $frequencyDay = null
    ): void {
        $companyId = $this->getCompanyId($company);
        if ($status === false) {
            PerfBonusSetting::where('company_id', $companyId)
                ->whereRaw('key = \'notification.status\'')
                ->update(['value' => 0, 'update_by' => Session::get('employee_id')]);
            return;
        }
        PerfBonusSetting::where('company_id', $companyId)
            ->whereRaw('key = \'notification.status\'')->update(['value' => $status, 'update_by' => Session::get('employee_id')]);
        PerfBonusSetting::where('company_id', $companyId)
            ->whereRaw('key = \'notification.text\'')->update(['value' => $text, 'update_by' => Session::get('employee_id')]);
        PerfBonusSetting::where('company_id', $companyId)
            ->whereRaw('key = \'notification.frequencyDay\'')->update(['value' => $frequencyDay, 'update_by' => Session::get('employee_id')]);
    }

    /**
     * 取得績效獎金通知的相關設定. (該設定為email通知).
     *
     * @param string $company 公司代號或是 id
     * @return array
     */
    public function getNotification(string $company): array
    {
        $companyId = $this->getCompanyId($company);
        return PerfBonusSetting::where('key', 'like', 'notification.%')
            ->where('company_id', $companyId)
            ->get(['key', 'value'])
            ->transform(fn ($it) => [
                'key' => str_replace('notification.', '', $it->key),
                'value' => $it->value,
            ])
            ->toArray();
    }

    public function getSetting(string $company)
    {
        $companyId = $this->getCompanyId($company);
        $data = PerfPermission::with(['employees' => function (HasManyThrough $query) use ($companyId) {
            $query->select(['employees.id', 'payload->name as name'])
                ->where('employees.company_id', $companyId);
        }])->get(['id', 'name', 'slug', 'description']);
        return $data;
    }

    /**
     * @param string $id 權限列表的id
     * @param string|int $company 公司代號或是公司id
     * @param int $nowUserCompanyId 呼叫此方法的使用者的公司id
     * @param array $employeeIdArr 員工ids
     */
    public function putSetting(string $id, string|int $company, int $nowUserCompanyId, array $employeeIdArr = [])
    {
        $companyId = $this->getCompanyId($company);

        // 從資料庫中取得目前已有該權限的員工 id
        $result = PerfEmployeePermission::where('permission_id', $id)->get(['employee_id'])->pluck('employee_id');
        $diff = $result->diff($employeeIdArr);

        // 這邊的 count($data) == $result->count() 是為了避免新進如員比對過往資料時並沒有差異。
        if ($diff->isEmpty() && count($employeeIdArr) == $result->count()) {
            return;
        }

        $companyEmployeesIds = $this->employeeService->getEmployees($company)->pluck('id')->toArray();
        // 針對公司別刪除該權限(目的為下方只有更新、新增) 但是沒有剃除權限的功能。
        // 因此整個公司別的哪個權限直接刪除，然後再新增。
        if ($companyId == $nowUserCompanyId) {
            PerfEmployeePermission::where('permission_id', $id)
                ->whereIn('employee_id', $companyEmployeesIds)
                ->delete();
        }

        $data = collect($employeeIdArr)
            // 過濾傳入的 id 是否為該公司的員工
            ->filter(fn ($employee) => in_array($employee, $companyEmployeesIds))
            // 封裝成需要的格式
            ->map(fn ($employeeId) => ['permission_id' => $id, 'employee_id' => $employeeId])
            // INSERT 進資料庫
            ->each(fn ($singleData) => PerfEmployeePermission::firstOrCreate($singleData));

        return $data;
    }

    /**
     * 重新計算專案狀態，如果專案已經沒有需要審核的資料，則將專案狀態改為已完成.
     *
     * @param int $projectId
     * @return bool 專案有更新的話，回傳 true
     */
    public function reCalcProjectStatus(int $projectId): bool
    {
        $project = $this->getProjectById($projectId);

        $pendingReviews = $project->reviews->filter(function ($itReview) {
            return (! empty($itReview->first_empno) && empty($itReview->first_finished_at))
                || (! empty($itReview->second_empno) && empty($itReview->second_finished_at));
        });

        if ($pendingReviews->count() === 0) {
            $project->update(['status' => PerfErpProject::STATUS_COMPLETED]);
            return true;
        }

        return false;
    }

    /**
     * 取得需要核定的專案資料(personal).
     *
     * @param string $empno
     * @param array $with
     *
     * @return array
     */
    public function getShouldReviewProjectByEmployeeId(int $employeeId, array $with = [], array $filterCondition = [])
    {
        $employee = $this->employeeService->getEmployeeById($employeeId);
        $empno = $employee->number;

        if (in_array('logs', $with)) {
            $idx = array_search('logs', $with, true);
            unset($with[$idx]);

            $with['logs'] = function ($query) {
                $query->select([
                    'review_id',
                    'sno',
                    'empno',
                    'empna',
                    'is_new as isNew',
                    'append_by as appendBy',
                    'first_reviewer_payload as firstReviewerPayload',
                    'finally_reviewer_payload as finallyReviewerPayload',
                    'question_extra as questionExtra',
                    'show_title as showTitle',
                ])
                ->orderBy('id', 'asc');
            };
        }

        /** @var \Illuminate\Database\Eloquent\Builder $queryBuilder */
        $queryBuilder = PerfDepReview::with($with)->join('perf_erp_projects', 'perf_dep_reviews.prj_id', '=', 'perf_erp_projects.id');
        $queryBuilder->where(function ($query) use ($empno) {
            // 有初次核定人員，但是他還沒有核定完；所以檢查初次核定人員
            $query->orWhere('first_empno', $empno)
                ->whereNull('first_finished_at');

            // 無初次核定人員，所以直接檢查二次核定人員
            $query->orWhere(function ($query) use ($empno) {
                $query->whereNull('first_empno')
                    ->where('second_empno', $empno)
                    ->whereNull('second_finished_at');
            });

            // 有初次核定人員，並且他已經核定完了；所以檢查二次核定人員
            $query->orWhere(function ($query) use ($empno) {
                $query->whereNotNull('first_empno')
                    ->whereNotNull('first_finished_at')
                    ->where('second_empno', $empno)
                    ->whereNull('second_finished_at');
            });
        });

        // 搜索條件
        (! empty($filterCondition['id'])) && $queryBuilder->where('perf_dep_reviews.id', $filterCondition['id']);
        (! empty($filterCondition['epjno'])) && $queryBuilder->where('perf_erp_projects.epjno', $filterCondition['epjno']);
        (! empty($filterCondition['closeYm'])) && $queryBuilder->where('perf_erp_projects.closeym', str_replace('/', '', $filterCondition['closeYm']));
        (! empty($filterCondition['accId'])) && $queryBuilder->where('perf_erp_projects.accasno', $filterCondition['accId']);

        $queryBuilder->select([
            // --- 專案資料
            'perf_erp_projects.id as projectId',
            'perf_erp_projects.epjno',
            'perf_erp_projects.company_id as companyId',
            'perf_erp_projects.accasno as accId',
            'perf_erp_projects.project_name as projectName',
            'perf_erp_projects.s_date as sDate',
            'perf_erp_projects.e_date as eDate',
            'perf_erp_projects.total_pay as totalPay',
            'perf_erp_projects.closeym as closeYm',

            // --- 原表
            'perf_dep_reviews.id',
            'perf_dep_reviews.first_finished_at as firstFinishedAt',
            'perf_dep_reviews.second_finished_at as secondFinishedAt',
            'perf_dep_reviews.status',

            'perf_dep_reviews.first_finished_at as firstFinishedAt',
            'perf_dep_reviews.second_finished_at as secondFinishedAt',
            'perf_dep_reviews.amount',
            'perf_dep_reviews.suggestion_amount as suggestionAmount',
            DB::raw("case when perf_dep_reviews.common_type = 'E' then '0' else '1' end as commonType"),
        ]);

        $paginationData = $queryBuilder->paginate(
            perPage: $this->perPageLimiter($filterCondition['perPage'] ?? null),
        );

        $data = $paginationData->getCollection()->map(function ($it) use (&$with) {
            if (! empty($it->sDate)) {
                $it->sDate = date('Y/m/d', strtotime($it->sDate));
            }
            if (! empty($it->eDate)) {
                $it->eDate = date('Y/m/d', strtotime($it->eDate));
            }

            if (isset($with['logs'])) {
                $it->logs->transform(function ($it) {
                    (! empty($it->firstReviewerPayload)) && $it->firstReviewerPayload = json_decode($it->firstReviewerPayload, true);
                    (! empty($it->finallyReviewerPayload)) && $it->finallyReviewerPayload = json_decode($it->finallyReviewerPayload, true);
                    (! empty($it->questionExtra)) && $it->questionExtra = json_decode($it->questionExtra, true);

                    unset($it->review_id);
                    return $it;
                });
            }

            unset($it->prj_id, $it->erpProject);
            return $it;
        });

        return [
            'page' => $paginationData->currentPage(), // 當前頁數
            'totalPage' => $paginationData->lastPage(), // 總共頁數
            'data' => $data, // 資料集合
            'total' => $paginationData->total(), // 總共資料筆數
        ];
    }

    /**
     * 將對應的 review 表更新資訊。
     * 當他的 confirm 為 true 時，則需要將他的完成日期押上；並透過 ORM 去 trigger erp project 的狀態、資訊.
     *
     * @param int $reviewId 審核id
     * @param int $modifyEmployeeId 當前操作的人員是誰
     * @param array $data 資料
     * @param bool $confirm 是否確定送審，不送審則不更新他的完成日期
     *
     * @return void
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException<\Illuminate\Database\Eloquent\Model>
     */
    public function updateApprove(int $reviewId, int $modifyEmployeeId, array $data, bool $confirm = false)
    {
        $employee = $this->employeeService->getEmployeeById($modifyEmployeeId);
        $empno = $employee->number;

        // 找不到拋出異常
        $reviewData = PerfDepReview::with(['logs'])->where('id', $reviewId)->firstOrFail();

        // 判斷當前操作的人員是初次核定人員還是二次核定人員
        $firstApprove = $reviewData->first_empno === $empno && empty($reviewData->first_finished_at); // 初次核定人員未完成填寫
        $secondApprove = $reviewData->second_empno === $empno && empty($reviewData->second_finished_at); // 二次核定人員未完成填寫

        // 如果是初核階段，把這個 flag 設定為 false，這樣才不會觸發二次核定人員的審核
        if ($firstApprove) {
            $secondApprove = false;
        }

        // $reviewData->logs 原始想要審核的人員
        $passEmpnoArr = Arr::pluck($data, 'empno');
        $willDeleteEmpno = $reviewData->logs->filter(fn ($t) => ! in_array($t->empno, $passEmpnoArr));
        $willDeleteEmpno->each(fn ($log) => $log->delete());

        // 預先處理塞入資訊，將 payload 進入 logs 資料表
        foreach ($data as $formData) {
            $targetEmpno = $formData['empno'] ?? null;
            $payload = $formData['payload'] ?? [];
            $isNew = $formData['isNew'] ?? false;

            // 如果傳入的 form 表單不存在 empno 則略過處理。
            if (empty($targetEmpno)) {
                continue;
            }

            $commonData = [
                'is_new' => $isNew,
                'append_by' => $modifyEmployeeId,
            ];
            // 有找到名稱的話再回推回去，不然就不更新該資料。(原因為可能該人員已經離職了，但是沒有下這條件的話，名字會被清空；到時會出 excel 會出問題。)
            $targetEmployee = $this->employeeService->getEmployeeByEmpno($targetEmpno);
            if (! empty($targetEmployee)) {
                $commonData['empna'] = $targetEmployee['name'];
            }
            switch (true) {
                case $firstApprove === true:
                    $updateData = array_merge($commonData, [
                        'sno' => '',
                        'first_empno' => $empno,
                        'first_reviewer_payload' => json_encode($payload, JSON_UNESCAPED_UNICODE),
                    ]);
                    break;
                case $secondApprove === true:
                    $updateData = array_merge($commonData, [
                        'sno' => '',
                        'second_empno' => $empno,
                        'finally_reviewer_payload' => json_encode($payload, JSON_UNESCAPED_UNICODE),
                    ]);
                    break;
                default:
                    throw new Exception('該筆資料不屬於你審核或是你已經處理過該筆資料了! 員工編號: ' . $empno . ' 審核id為: ' . $reviewId);
            }

            // 更新資料
            $reviewData->logs()->updateOrCreate([
                'review_id' => $reviewId,
                'empno' => $targetEmpno,
            ], $updateData);
        }

        if ($firstApprove && $confirm) {
            $reviewData->update(['first_finished_at' => now()]);
        }

        // 需要初次核定的人員核定過後，或是沒有初次核定的人員時，這個 flag 才會為 true
        $passToFinalFlag = empty($reviewData->first_empno) || ($reviewData->first_empno && $reviewData->first_finished_at);
        if ($passToFinalFlag && $secondApprove && $confirm) {
            $reviewData->update(['second_finished_at' => now(), 'status' => PerfDepReview::STATUS_FINISHED]);
        }
    }

    /**
     * 取得已經審核過的專案資料.
     *
     * @param int $employeeId 員工id 該 id 為 人資系統的id，非 erp 的 id
     * @param array $with 附加資訊? 目前資源logs
     * @param array $filterCondition 過濾條件
     */
    public function getAlreadyReviewProjectByEmployeeId(int $employeeId, array $with = [], array $filterCondition = [])
    {
        $employee = $this->employeeService->getEmployeeById($employeeId);
        $empno = $employee->number;

        if (in_array('logs', $with)) {
            $idx = array_search('logs', $with, true);
            unset($with[$idx]);

            $with['logs'] = function ($query) {
                $query->select([
                    'review_id',
                    'sno',
                    'empno',
                    'empna',
                    'is_new as isNew',
                    'append_by as appendBy',
                    'first_reviewer_payload as firstReviewerPayload',
                    'finally_reviewer_payload as finallyReviewerPayload',
                    'question_extra as questionExtra',
                    'show_title as showTitle',
                ])
                ->orderBy('id', 'asc');
            };
        }

        /** @var \Illuminate\Database\Eloquent\Builder $builder */
        $builder = PerfDepReview::with($with)->join('perf_erp_projects', 'perf_dep_reviews.prj_id', '=', 'perf_erp_projects.id');
        // 這邊已經把需要過濾的專案撈出來了(By 人員)；所以已經塞了很少的話，直接透過 collection 的 filterCondition 就可以了
        $builder->where(function ($query) use ($empno) {
            $query->where(fn ($q) => $q->where('first_empno', $empno)->whereNotNull('first_finished_at'));
            $query->orWhere(fn ($q) => $q->where('second_empno', $empno)->whereNotNull('second_finished_at'));
        });
        (! empty($filterCondition['id'])) && $builder->where('perf_dep_reviews.id', $filterCondition['id']);
        (! empty($filterCondition['epjno'])) && $builder->where('perf_erp_projects.epjno', $filterCondition['epjno']);
        (! empty($filterCondition['closeYm'])) && $builder->where('perf_erp_projects.closeym', str_replace('/', '', $filterCondition['closeYm']));
        (! empty($filterCondition['accId'])) && $builder->where('perf_erp_projects.accasno', $filterCondition['accId']);

        $builder->select([
            // --- 專案資料
            'perf_erp_projects.id as projectId',
            'perf_erp_projects.epjno',
            'perf_erp_projects.company_id as companyId',
            'perf_erp_projects.accasno as accId',
            'perf_erp_projects.project_name as projectName',
            'perf_erp_projects.s_date as sDate',
            'perf_erp_projects.e_date as eDate',
            'perf_erp_projects.total_pay as totalPay',
            'perf_erp_projects.closeym as closeYm',

            // --- 原表
            'perf_dep_reviews.id',
            'perf_dep_reviews.first_finished_at as firstFinishedAt',
            'perf_dep_reviews.second_finished_at as secondFinishedAt',
            'perf_dep_reviews.status',
            'perf_dep_reviews.amount',
            'perf_dep_reviews.suggestion_amount as suggestionAmount',
            DB::raw("case when perf_dep_reviews.common_type = 'E' then '0' else '1' end as commonType"),
        ]);

        $paginationData = $builder->paginate(
            perPage: $this->perPageLimiter($filterCondition['perPage'] ?? null),
        );

        $data = $paginationData->getCollection()->map(function ($it) use (&$with) {
            if (! empty($it->sDate)) {
                $it->sDate = date('Y/m/d', strtotime($it->sDate));
            }
            if (! empty($it->eDate)) {
                $it->eDate = date('Y/m/d', strtotime($it->eDate));
            }

            if (isset($with['logs'])) {
                $it->logs->transform(function ($it) {
                    if (! empty($it->firstReviewerPayload)) {
                        $it->firstReviewerPayload = json_decode($it->firstReviewerPayload, true);
                    }

                    if (! empty($it->finallyReviewerPayload)) {
                        $it->finallyReviewerPayload = json_decode($it->finallyReviewerPayload, true);
                    }

                    if (! empty($it->questionExtra)) {
                        $it->questionExtra = json_decode($it->questionExtra, true);
                    }

                    unset($it->review_id);
                    return $it;
                });
            }

            unset($it->prj_id, $it->erpProject);
            return $it;
        });

        return [
            'page' => $paginationData->currentPage(), // 當前頁數
            'totalPage' => $paginationData->lastPage(), // 總共頁數
            'data' => $data, // 資料集合
            'total' => $paginationData->total(), // 總共資料筆數
        ];
    }

    /**
     * 用途主要是專案的部門代碼中雜亂，要使用者個方法、並且透過 sql 的 map 表來查詢到最終的部門代號。
     * 詳情可見: database\migrations\2023_09_20_065804_create_erp_projects_table.php
     * 舉個例子: 如同 部門代號 AR 會對應到 A 部門(商空部).
     * example output => ['AR' => 'A', 'AT' => 'A', 'BP' => 'B', 'BS' => 'B' ...].
     *
     * @return array
     */
    protected function getTransformCodeMapCache(): array
    {
        $data = Cache::get('transform_code_map');

        if (! empty($data)) {
            return $data;
        }
        $data = PerfDepGroup::all(['code', 'gen_code'])->pluck('gen_code', 'code')->toArray();
        Cache::put('transform_code_map', $data, 60 * 5);
        return $data;
    }

    /**
     * 分頁器的每頁筆數限制.
     *
     * @param int|Closure|null $perPage
     * @return int
     */
    protected function perPageLimiter($perPage = 15)
    {
        return match ($perPage) {
            null,$perPage < 1 => 15,
            $perPage > 500 => 500,
            default => $perPage,
        };
    }
}
