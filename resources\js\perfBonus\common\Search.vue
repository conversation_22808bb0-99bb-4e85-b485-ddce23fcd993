<template>
  <div v-if="!action" class="p-6 mx-auto flex flex-col custom-height" style="max-width: 1620px;">
    <!-- searching bar -->
    <div class="bg-white p-6 flex flex-wrap items-center gap-5 rounded-lg shadow mb-6">
      <span>績效年月 :</span>
      <Calendar v-model="searchVariables.period" view="month" dateFormat="yy/mm"/>
      <template v-if="mode === 'historyByStaff'">
        <span>人員 :</span>
        <MultiSelect v-model="searchVariables.staff" @click="reshape" :options="employeeList" optionValue="number" filter :optionLabel="customOptionLabel" placeholder="請選擇人員" class="w-56 custom-multi-select" :pt="{closeButton: { class: '!hidden'}}"/>
      </template>
      <template v-else>
        <span>專案代號(選填) :</span>
        <span class="p-input-icon-right">
          <i class="pi pi-search" />
          <InputText v-model="searchVariables.projectCode" @keydown.enter="search" placeholder="請輸入"/>
        </span>
      </template>
      <div v-if="forcedFetch">
        <input v-model="searchVariables.force" type="checkbox" id="forcedFetch" class="mr-2 cursor-pointer">
        <label for="forcedFetch" class="cursor-pointer">強制撈取</label>
      </div>
      <Button label="重置" severity="secondary" outlined @click="resetVariables"></Button>
      <Button @click="search" label="查詢" icon="pi pi-search"></Button>
    </div>
    <!-- searching result -->
    <div class="bg-white rounded-lg overflow-hidden shadow grow w-full relative" >
      <template  v-if="!dataList.data?.length>0">
        <Fetching v-if="fetching"></Fetching>
        <DataStatus v-else :data="dataList.data" :searched="searched"></DataStatus>
      </template>
      <template  v-else>
        <Fetching v-if="fetching"></Fetching>
        <div class="flex flex-col h-full">
          <div class="h-20 p-6 flex justify-between items-center" >
            <slot name="dataHeader" :selectedProjects="selectedProjects" :confirm="confirm" :exportBonus="exportBonus"></slot>
          </div>
          <slot name="table" :dataList="dataList" :selectAll="selectAll" :verticalAlignTable="verticalAlignTable" :selectAllProjects="selectAllProjects" :selectProject="selectProject" :formatValue="formatValue" :currentIndex="currentIndex" :setAmount="setAmount" :validateRecommendedAmount="validateRecommendedAmount" :selectReviewer="selectReviewer" :reviewerName="reviewerName" :sumRow="sumRow" :openResultProject="openResultProject" :openResultDetail="openResultDetail" :expandRowIndex="expandRowIndex" :expandRow="expandRow"></slot>
          <template v-if="mode!=='historyByStaff'">
            <div v-if="selectedProjects.length>0" class="border-t px-6 py-4 bg-gray-50 flex flex-wrap whitespace-nowrap items-center justify-end">
              <span class="custom-span-for-selected">已選取{{selectedProjects.length}}個</span>
              <Button label="取消選取" text @click="selectAllProjects(false)"></Button>
            </div>
            <Paginator v-else class="border-t" :data="dataList.pagination" :per="paginationPer" :sPer="searchVariables.perPage" @page="searchPage"></Paginator>
          </template>
        </div>
      </template>
    </div>
    <!-- warning and etc. -->
    <template v-if="mode==='submit'">
      <Dialog v-model:visible="selectReviewerView" :draggable="false" modal :style="{width: '488px'}">
        <template #header>
          <span class="text-2xl font-bold">編輯初核人員</span>
        </template>
        <div class="text-sm mt-5 ml-1 mb-1" style="color: #98A2B3">初核人員</div>
        <MultiSelect v-model="selectedReviewer" @click="reshape" :options="employeeList" optionValue="number" filter :optionLabel="customOptionLabel" placeholder="請選擇" :selectionLimit="selectedLimit" class="w-4/6 md:w-20rem mb-16 custom-multi-select" :class="{'p-invalid': selectedReviewer.length === 0 && selectedReviewerSubmitted}" :pt="{closeButton: { class: '!hidden'}}"/>
        <template #footer>
          <Button label="取消" @click="cancelSelectReviewer" severity="secondary" outlined></Button>
          <Button label="確定" @click="confirmSelectReviewer" autofocus class="custom-button"></Button>
        </template>
      </Dialog>
      <Dialog v-model:visible="recommendedAmountWarningView" :draggable="false" modal :showHeader="false" :style="{ width: '384px'}" class="rounded overflow-hidden" >
        <div class="flex flex-col justify-center items-center pt-8 px-6">
          <img src="../../../images/popup_state/warning.svg" alt="warning" class="mb-8">
          <div class="text-xl font-bold mb-24">建議額度超出績效額度</div>
          <Button label="我知道了" @click="recommendedAmountWarningView = false"></Button>
        </div>
      </Dialog>
    </template>
  </div>
  <Detail v-else :mode="mode" :edit="false" :employeeList="employeeList" :detailData="detailData"></Detail>
</template>
  
<script>
import Button from 'primevue/button';
import Calendar from 'primevue/calendar';
import ConfirmPopup from 'primevue/confirmpopup';
import Dialog from 'primevue/dialog';
import InputNumber from 'primevue/inputnumber';
import InputText from 'primevue/inputtext';
import MultiSelect from 'primevue/multiselect';
import DataStatus from '../../common/DataStatus.vue'
import Detail from '@/perfBonus/common/Detail.vue';
import Fetching from '../../common/Fetching.vue'
import Paginator from '../../common/Paginator.vue';
export default {
  components: {
    // primevue
    Button,
    Calendar,
    ConfirmPopup,
    Dialog,
    InputNumber,
    InputText,
    MultiSelect,
    // self-designed
    DataStatus,
    Detail,
    Fetching,
    Paginator
  },
  props: {
    mode: {
      type: String,
      required: true
    },
    forcedFetch: {
      type: Boolean,
      default: false
    },
    employeeList: {
      required: true
    },
    subsidiary: {
      required: true
    }
  },
  data() {
    return {
      // default, status
      apiURL: "/api/acc/perf-bonus",
      action: 0,
      fetching: 0,
      searched: 0,
      dataList: null,
      detailData: null,
      paginationPer: [
                {name: '10 　 筆', value: 10},
                {name: '30 　 筆', value: 30},
                {name: '60 　 筆', value: 60},
                {name: '100　筆', value: 100},
      ],
      selectReviewerView: false,
      recommendedAmountWarningView: false,
      dataList: [],
      // user input
      searchVariables: {
        period: "",
        projectCode: null,
        force: null,
        perPage: 10,
        page: 1,
        staff: []
      },
      selectAll: false,
      selectedRow: null, // 選審核人
      selectedIndex: null, // 選審核人
      selectedLimit: null, // 選審核人
      selectedReviewer: [], // 選審核人
      selectedReviewerSubmitted: false, // 選審核人
      expandRowIndex: [],
      // output
      selectedProjects: [],
    };
  },
  mounted() {
    // 如果是小鈴鐺跳轉來的要自動搜尋
    const searchParams = new URLSearchParams(new URL(window.location.href).search);
    const idParam = searchParams.get('id');

    if(idParam && this.mode==='historyByCase') {
      this.searchVariables.projectCode = idParam
      this.search()
    }
  },
  methods: {
    // search bar
    resetVariables() {
      this.searchVariables = this.$options.data().searchVariables
    },
    async search() {
      if(this.mode === 'submit') {
        await this.submitSearch()
      } else if(this.mode === 'review') {
        await  this.reviewSearch()
      } else if(this.mode === 'historyByCase') {
        await this.historyByCaseSearch()
      } else if(this.mode === 'historyByStaff') {
        await this.historyByStaffSearch()
      }
      
      if(document.querySelector('.p-scrollpanel-content')) {
        document.querySelector('.p-scrollpanel-content').addEventListener('scroll', () => {
          if(document.querySelector('.scroll-header').scrollLeft != document.querySelector('.p-scrollpanel-content').scrollLeft) {
            document.querySelector('.scroll-header').scrollLeft = document.querySelector('.p-scrollpanel-content').scrollLeft
          }
        })
      }
    },
    async submitSearch() {
      this.fetching = 1
      await axios
        .get(this.apiURL + `/f/projects/${this.subsidiary}`, {params: {
          epjno: this.searchVariables.projectCode,
          closeYm: this.closeYm, 
          perPage: this.searchVariables.perPage,
          page: this.searchVariables.page,
          force: this.searchVariables.force ? 1 : 0,
        }})
        .then(res => {
          this.searched = 1
          this.fetching = 0
          this.dataList = res.data
          this.dataList.data = this.formatSearch(this.dataList.data)
          this.selectedProjects = []
        })
        .catch(err => {
          this.searched = 1
          this.fetching = 0
        })
    },
    formatSearch(data) {
      return  data.map(project => {
        if(!this.employeeList.some(el => el.number===project.pmEmpno)) { // 砍非在職人員
          project.pmEmpno = null
          project.pmCode = null
        }
        if(!this.employeeList.some(el => el.number===project.pcEmpno)) {  // 砍非在職人員
          project.pcEmpno = null
          project.pcCode = null
        }

        project.organizations = project.organizations.map(org => {
          let item = {
            code: org,
            amount: null,
            suggestAmount: null,
            preliminaryReviewers: []
          }

          if(!project.pcEmpno || !project.pmEmpno) { // 一個初核
            if(project.pcEmpno) {
              item.preliminaryReviewers.push(project.pcEmpno)
            }
            if(project.pmEmpno) {
              item.preliminaryReviewers.push(project.pmEmpno)
            }
          } else { // 兩個初核
            if(project.pcEmpno === project.pmEmpno) { // CPM與PM同一人
              item.preliminaryReviewers.push(project.pcEmpno)
            } else { // CPM與PM不同人
              if(project.organizations.length === 1) {  // 一個部門
              item.preliminaryReviewers.push(project.pcEmpno, project.pmEmpno) // CPM與PM都放進去那一個部門
              } else { // 兩個以上的部門
                if(project.pcEmpno.includes(org) && project.pmEmpno.includes(org)) { // 同時是CPM與PM的部門
                  item.preliminaryReviewers.push(project.pcEmpno, project.pmEmpno) // CPM與PM都放進去那一個部門
                } else { // 可能是CPM或PM其中一人的部門
                  if(project.pcEmpno.includes(org)) { // CPM的部門
                    item.preliminaryReviewers.push(project.pcEmpno)  // 放CPM為初核
                  } else if (project.pmEmpno.includes(org)) { // PM的部門 
                    item.preliminaryReviewers.push(project.pmEmpno)  // 放PM為初核
                  } else { // 都不屬於的部門
                      // 都不放
                  }
                }
              }
            }
          }
          return item
        })
        return project
      })
    },
    async reviewSearch() {
      this.fetching = 1
      await axios
        .get(this.apiURL + '/s/result', {params: {
          epjno: this.searchVariables.projectCode,
          closeYm: this.closeYm, 
          perPage: this.searchVariables.perPage,
          page: this.searchVariables.page,
          force: this.searchVariables.force ? 1 : 0,
        }})
        .then(res => {
          this.searched = 1
          this.fetching = 0
          this.dataList = res.data
        })
        .catch(err => {
          this.searched = 1
          this.fetching = 0
        })
    },
    async historyByCaseSearch() {
      this.fetching = 1
      await axios
        .get(this.apiURL + '/result/project',  {
          params: {
            closeYm: this.closeYm, 
            epjno: this.searchVariables.projectCode,
          }
        })
        .then(res => {
          this.searched = 1
          this.fetching = 0
          this.dataList = res.data
        })
        .catch(err => {
          this.searched = 1
          this.fetching = 0
          // DataStatus 會顯示獲取失敗的狀態，就不跳通知了
        })
    },
    async historyByStaffSearch() {
      this.fetching = 1
      await axios
        .get(this.apiURL + '/result/employee',  {
          params: {
            empno: this.searchVariables.staff.join(','),
            closeYm: this.closeYm, 
          }
        })
        .then(res => {
          this.searched = 1
          this.fetching = 0
          this.dataList = res.data
          this.dataList.data = this.dataList.data.flat()
        })
        .catch(err => {
          this.searched = 1
          this.fetching = 0
          // DataStatus 會顯示獲取失敗的狀態，就不跳通知了
        })
    },
    searchPage(params) {
      this.searchVariables.perPage = params.per
      this.searchVariables.page = params.page
      this.search()
    },
    openResultProject(id) {
      this.action = 1
      axios
        .get(this.apiURL + '/s/result/' + id)
        .then(res => {
          this.detailData = res.data.data[0]
        })
        .catch(err => {
          this.$toast.add({ 
            severity: 'error', 
            summary: 'Error Message',
            detail: '開啟失敗',
            life: 3000
          });
          this.detailData = {}
        })
    },
    openResultDetail(id) {
      this.action = 1
      this.detailData = {...this.dataList.data[id], ...this.dataList.data[id].info}
    },
    // data table
    customOptionLabel(option) {
        return `${option.orgName} ${option.name}`;
    },
    currentIndex(row, col) {
      return row.organizations.map(org => org.code).findIndex(el => el === col.code)
    },
    sumRow(row, type) {
      return row.organizations.reduce((acc,cur) => cur[type] + acc, 0)
    },
    formatValue(int) {
      return Number.parseFloat(int).toFixed(0).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    },
    setAmount(e, row, col) {
      row.organizations[this.currentIndex(row, col)].amount = e.value;
      row.organizations[this.currentIndex(row, col)].suggestAmount = e.value===null ? null : Math.round(e.value*0.7 / 100) * 100;
    },
    validateRecommendedAmount(e, row, col) {
      if(parseInt(e.value.replace(/,/g, ''), 10) > row.organizations[this.currentIndex(row, col)].amount) {
        this.recommendedAmountWarningView = true
      }
    },
    selectProject(e, row) {
      if(e.target.checked) {
        const {epjno, organizations} = row
        this.selectedProjects.push({projectId: epjno, organizations})
      } else {
        this.selectedProjects = this.selectedProjects.filter(el => el.projectId != row.epjno)
      }
    },
    selectAllProjects(e) {
      if(e.target?.checked) {
        this.selectedProjects = this.dataList.data.map(el => {
          el.selected = true;
          return {
            projectId: el.epjno,
            organizations: el.organizations,
          }
        })
      } else {
        if(this.selectAll) { // 如果從"全選"開，"取消選取"關閉時也要把"全選"取消打勾
          this.selectAll = false
        }
        this.dataList.data.forEach(element => {
          element.selected = false;
        })
        this.selectedProjects = []
      }
    },
    reviewerName(reviewerIdList) {
      let reviewers = ""
      let tic = 0
      reviewerIdList.forEach(element => {
        if(!tic) {
          reviewers += this.employeeList.find(emplInfo => emplInfo.number === element)?.name
          tic++
        } else {
          reviewers += '、' + this.employeeList.find(emplInfo => emplInfo.number === element)?.name
        }
      })
      return reviewers
    },
    selectReviewer(row, col) {
      this.selectedRow = row
      this.selectedIndex = this.currentIndex(row,col)
      this.selectedReviewer = row.organizations[this.selectedIndex].preliminaryReviewers
      this.selectedLimit = JSON.parse(JSON.stringify(this.selectedReviewer.length)) === 0 ? 1 : JSON.parse(JSON.stringify(this.selectedReviewer.length))
      this.selectReviewerView = true;
    },
    confirmSelectReviewer() {
      this.selectedReviewerSubmitted = true
      if(this.selectedReviewer.length === 0) {
        this.$toast.add({ 
          severity: 'warn', 
          summary: 'Warning!',
          detail: '請至少選擇一位審核人員',
          life: 3000
         });
        return
      }
      this.selectedRow.organizations[this.selectedIndex].preliminaryReviewers = this.selectedReviewer
      this.selectReviewerView = false
    },
    cancelSelectReviewer() {
      this.selectReviewerView = false;
    },
    expandRow(index) {
      if(this.expandRowIndex.includes(index)) {
        this.expandRowIndex = this.expandRowIndex.filter(el => el !== index)
      } else {
        this.expandRowIndex.push(index)
      }
    },
    reshape() { // 修改 primevue-multiselect 外型
      this.$nextTick(() => {
        const multiselectPanel = document.querySelector('.p-multiselect-panel') 
        multiselectPanel.classList.add("rounded", "overflow-hidden", "w-72", "translate-y-2", "custom-multiselect-panel")
    
        const multiselectHeader = document.querySelector('.p-multiselect-header')
        multiselectHeader.classList.add("custom-multiselect-header")

      })
    },
    verticalAlignTable() {
      if(document.querySelector('.scroll-header').scrollLeft != document.querySelector('.p-scrollpanel-content').scrollLeft) {
        document.querySelector('.p-scrollpanel-content').scrollLeft = document.querySelector('.scroll-header').scrollLeft
      }
    },
    // submit
    confirm(e) {
      this.$confirm.require({
        target: e.currentTarget,
        message: '是否確定送出核定?',
        acceptLabel: "確認",
        rejectLabel: "取消",
        accept: () => {
            this.submit()
            
        },
      });
    },
    submit() {
      this.fetching = 1
      axios.
        post(this.apiURL + `/f/projects/${this.subsidiary}`, {data: this.selectedProjects})
        .then(res => {
          this.$toast.add({ 
            severity: 'success', 
            summary: 'success Message',
            detail: '送出成功',
            life: 3000
          });
          this.selectedProjects = []
          this.$parent.currentTab = 1
        })
        .catch(err => {
          this.$toast.add({ 
            severity: 'error', 
            summary: '送出失敗',
            detail: err.response.data.message,
            life: 3000
          });
          this.fetching = 0
        })
    },
    scrollTo() {
      const targetElement = document.getElementById('row_8')
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: "smooth" }); // 使用平滑滚动效果
      } 
    },
    // export 
    exportBonus() {
      const url = this.apiURL + `/download/${this.mode==='historyByCase' ? 'projects' : 'employees'}`
      axios({
        url: url,
        method: "GET",
        responseType: 'blob',
        params: {
          epjno: this.searchVariables.projectCode,
          empno: this.searchVariables.staff.join(),
          closeYm: this.closeYm
        }
      })
      .then(response => {
        const link = document.createElement('a');
        const url = window.URL.createObjectURL(new Blob([response.data]));
        link.href = url;
        const timestampInSeconds = Math.floor(new Date().getTime() / 1000);
        link.setAttribute('download', `${this.mode==='historyByCase' ? '專案' : '人員'}績效_${timestampInSeconds}.xlsx`);
        link.click();
        window.URL.revokeObjectURL(url);
      })
      .catch(error => {
        this.$toast.add({ severity: 'error', summary: 'Error Message', detail: '下載錯誤', life: 3000 });
      });
    }
  },
  computed: {
    closeYm() {
      if(this.searchVariables.period) {
        const year = this.searchVariables.period.getFullYear() 
        const month = (this.searchVariables.period.getMonth() + 1).toString().padStart(2, '0')
        return year + '/' + month
      }
      return null
    },
  },
  watch: {
    selectedReviewer(newVal) { // 修改 multiSelect 框內容
      this.$nextTick(() => {
        const selectedArea = document.querySelector('.p-multiselect-label')
        selectedArea.innerText = !newVal.length ? '請選擇' : this.employeeList.filter(empl => this.selectedReviewer.some(el => el === empl.number)).map(el => el.name).join(', ')
      })
    },
    'searchVariables.staff': { // 修改選取人員內容
      deep: true,
      handler(newVal) {
        if(this.mode === 'historyByStaff') {
          this.$nextTick(() => {
            const selectedArea = document.querySelector('.p-multiselect-label')
            selectedArea.innerText = !newVal.length ? '請選擇人員' : this.employeeList.filter(empl => this.searchVariables.staff.some(el => el === empl.number)).map(el => el.name).join(', ')
          })
        }
      },
    },
    selectReviewerView(newVal) { // 以任何方式關閉視窗都將讓狀態回到原始
      if(newVal === false) {
        this.selectedRow = null
        this.selectedIndex = null
        this.selectedLimit = null
        this.selectedReviewer = []
        this.selectedReviewerSubmitted = false
      }
    },
    dataList: { // 勾勾選顯示一致性
      deep: true,
      handler() {
        if(this.dataList.data.length) {
          if(this.dataList.data.every(el => el.selected)) {
            this.selectAll = true
          } else {
            this.selectAll = false
          }
        } else {
          this.selectAll = false
        }
      }
    },
  }
};
</script>

<style scoped>
.custom-height {
  height: calc(100vh - 160px);
  min-height: 900px;
}
@media (min-width: 768px) {
  .custom-height {
    height: calc(100% - 80px);
    min-height: 720px;
  }
}
::v-deep(.p-scrollpanel.custom-bar .p-scrollpanel-content) {
    /* 跟Scroll Panel衝突，會卡住scroll動作 */
    /* scroll-behavior: smooth;  */
    padding: 0 !important;
    height: 100% !important;
    width: 100% !important;
}
::v-deep(.p-scrollpanel.custom-bar .p-scrollpanel-bar) {
    background-color: rgb(100 116 139);
    border-radius: 5px;
}
::v-deep(.p-inputnumber.custom-input .p-inputnumber-input) {
  padding: 8px 12px 8px 12px;
  line-height: 32px;
  text-align: center;
  width: 100%;
}
.custom-button {
  margin: 0 !important;
}
.custom-span-for-selected {
  transform: translateY(1px);
  border: 1.25px solid transparent;
  padding: 12px 20px 12px 20px;
}
</style>

<style>
.custom-confirm-popup {
  box-shadow:
    1px 0 1px -1px rgba(0, 0, 0, 0.1),  /* 右 */
    -1px 0 2px -1px rgba(0, 0, 0, 0.1), /* 左 */
    0 1px 3px 0 rgba(0, 0, 0, 0.1),  /* 下 */
    0 -1px 2px -1px rgba(0, 0, 0, 0.1) /* 上 */  ;
}
.custom-confirm-popup .p-confirm-popup-footer {
  padding: 0 24px 24px 24px;
  display: flex;
  justify-content: space-between;
}
.custom-confirm-popup .p-confirm-popup-reject {
  outline: 1px rgb(100, 116, 139) solid;
  color: rgb(100, 116, 139);
  width: 80px !important;
  display: inline-flex;
  flex-direction: column;
}
.custom-confirm-popup .p-confirm-popup-accept {
  width: 80px !important;
  display: inline-flex;
  flex-direction: column;
}
.custom-multiselect-panel {
  border: 1px solid rgb(156 163 175) !important; 
}
.custom-multiselect-header {
  border: none !important;
  background-color: white !important;
}
</style>