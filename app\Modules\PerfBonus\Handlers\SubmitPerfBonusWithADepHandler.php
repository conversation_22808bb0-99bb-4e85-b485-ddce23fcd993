<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Handlers;

use App\Modules\PerfBonus\Models\PerfDepReview;
use App\Modules\PerfBonus\Models\PerfErpProject;
use App\Modules\PerfBonus\Services\PerfBonusService;
use App\Traits\CalcNumberTrait;
use App\Traits\PerfLogTrait;
use Exception;

/**
 * 送審績效獎金的時候，部門派出的人員只有一種腳色
 * Class SubmitPerfBonusWithADepHasTwoRolesHandler.
 * @package App\Modules\acc\Handlers\PerfBonus
 */
class SubmitPerfBonusWithADepHandler
{
    use CalcNumberTrait, PerfLogTrait;

    public static function handle(
        string $code, // 部門代碼 -> C1, DA, ...
        int|float $amount, // 實際額度 ...
        int|float $suggestionAmount, // 建議額度 ...
        array $reviewers, // 審核人員
        int $projectModelId, // 專案id
        int $sponsorEmployeeId, // 發起人員工編號
        array $projectEmployees = [], // 專案參與人員
        string $commonType = 'P',
        string $closeym = '',
    ): void {
        static::perfLog('info', '建立發放績效獎金的審核資料(該專案只有一種角色存在)', compact('code', 'amount', 'suggestionAmount', 'reviewers', 'projectModelId', 'sponsorEmployeeId', 'projectEmployees'));
        /** @var PerfBonusService $perfBonusService */
        $perfBonusService = app(PerfBonusService::class);

        // 取得部門主管對照表
        $depManagerCollection = $perfBonusService->getDepManagerCache();
        $depMaster = $depManagerCollection->first(fn ($it) => $it['code'] === $code);

        if (empty($depMaster)) {
            static::perfLog('error', '錯誤：找不到部門主管對照表');
            return;
        }

        $projectEngineers = array_values(array_filter($projectEmployees, fn ($emp) => $emp['common_type'] === PerfDepReview::COMMON_TYPE_CPM));
        $projectStaffs = array_values(array_filter($projectEmployees, fn ($emp) => $emp['common_type'] === PerfDepReview::COMMON_TYPE_PM));

        // 部門主管員工編號及姓名
        ['empno' => $depMasterEmpno, 'empna' => $depMasterEmpna] = $depMaster;

        // 績效獎金的最後一關是部門主管
        // 總共初核人員有兩種狀況：
        // 1) 無初核人員，故直接送到部門主管 -> case 0
        // 2) 一位初核人員，因此同部門裡面的工程人員跟專案人員都是同一位去做審核 -> case 1

        // 先產生 Record 紀錄，再去透過 Record 取產生對應的 log 資訊。
        // Record 紀錄是關於(部門+人員的腳色)去判斷
        // 故: 同部門若是有工程及專案，則會有兩筆記錄。

        $tmpReviews = [];
        switch (count($reviewers)) {
            case 0: // 沒有評審人員，直接送到部門主管
                $tmpReviews[$commonType] = PerfDepReview::firstOrCreate([
                    'code' => $code,
                    'first_empno' => null, // 第一階段評核人員
                    'second_empno' => $depMasterEmpno, // 第二階段評核人員(通常為部門主管)
                    'prj_id' => $projectModelId,
                ], [
                    'amount' => $amount, // 實際金額
                    'suggestion_amount' => $suggestionAmount, // 推薦金額
                    'status' => $suggestionAmount == 0 ? PerfDepReview::STATUS_SKIP : PerfDepReview::STATUS_PENDING, // pending, finished, skip
                    'common_type' => $commonType,
                    'closeym' => $closeym,
                ]);
                break;
            case 1: // 一位評審人員，因此同部門裡面的工程人員跟專案人員都是同一位去做審核
                $tmpReviews[$commonType] = PerfDepReview::create([
                    'code' => $code,
                    'first_empno' => $reviewers[0], // 第一階段評核人員
                    'second_empno' => $depMasterEmpno, // 第二階段評核人員(通常為部門主管)
                    'prj_id' => $projectModelId, // 專案id
                    'amount' => $amount, // 實際金額
                    'suggestion_amount' => $suggestionAmount, // 推薦金額
                    'status' => $suggestionAmount == 0 ? PerfDepReview::STATUS_SKIP : PerfDepReview::STATUS_PENDING, // pending, finished, skip
                    'common_type' => $commonType,
                    'closeym' => $closeym,
                ]);
                break;
            default:
                static::perfLog('error', '錯誤：評審人員超過一位');
                throw new Exception('該案件評審人員應該只有一位或是不填寫');
        }

        foreach ($tmpReviews as $type => $reviewRecord) {
            $commonData = [
                'review_id' => $reviewRecord->id,
                'is_new' => false,
                'append_by' => null,
            ];

            $insertDataSet = match ($type) {
                PerfDepReview::COMMON_TYPE_CPM => array_map(function ($employee) use (&$reviewRecord, &$commonData) {
                    return array_merge($commonData, [
                        'sno' => $employee['sno'],
                        'empno' => $employee['empno'],
                        'empna' => $employee['empna'],
                        'member_type' => $employee['member_type'],
                        'show_title' => $employee['show_title'],
                        'question_extra' => $employee['question_extra'],
                    ]);
                }, $projectEngineers),
                PerfDepReview::COMMON_TYPE_PM => array_map(function ($employee) use (&$reviewRecord, &$commonData) {
                    return array_merge($commonData, [
                        'sno' => $employee['sno'],
                        'empno' => $employee['empno'],
                        'empna' => $employee['empna'],
                        'member_type' => $employee['member_type'],
                        'show_title' => $employee['show_title'],
                        'question_extra' => $employee['question_extra'],
                    ]);
                }, $projectStaffs),
            };

            foreach ($insertDataSet as $dataset) {
                $reviewRecord->logs()->firstOrCreate($dataset);
            }
        }

        PerfErpProject::where('id', $projectModelId)->update([
            'sponsor_employee_id' => $sponsorEmployeeId,
            'status' => PerfErpProject::STATUS_IN_PROGRESS,
        ]); // 將專案狀態改為審核中
    }
}
