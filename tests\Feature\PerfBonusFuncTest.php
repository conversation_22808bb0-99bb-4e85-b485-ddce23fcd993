<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\acc\models\Employee;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Ramsey\Uuid\Uuid;
use Tests\TestCase;

class PerfBonusFuncTest extends TestCase
{
    const TEST_EMPLOYEE_ID = 583;

    public function getToken()
    {
        $this->get('/login?id=' . static::TEST_EMPLOYEE_ID)
            ->assertStatus(302);
    }

    /**
     * 設定績效獎金提醒相關事項的測試更新
     */
    public function testConfirmNotificationUpOrDownWithContent()
    {
        $this->getToken();
        // switch on
        $this->patch('/api/acc/perf-bonus/setting/RHT/notification', [
            'data' => [
                'text' => '哈囉，您有 :{name} 尚未進行績效獎金評定，請盡速去評定', // 提示內文
                'frequencyDay' => 1, // 提示頻率
            ],
            'status' => 1,
        ])
        ->assertStatus(204);

        // switch off
        $this->patch('/api/acc/perf-bonus/setting/RHT/notification', [
            'status' => 0,
        ])->assertStatus(204);
    }

    /**
     * 取得績效獎金提醒相關事項相關資訊
     */    public function testGetNotificationSetting()
    {
        $this->getToken();
        $this->get('/api/acc/perf-bonus/setting/RHT/notification')
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'key',
                        'value',
                    ]
                ]
            ]);
    }
}
