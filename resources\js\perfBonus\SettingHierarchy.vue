<template>
  <div class="mx-auto p-6 custom-height" style="max-width: 1572px; ">
    <div v-show="hierarchy.length" class="shadow rounded-lg overflow-hidden bg-white flex flex-col">
      <div class="text-xl font-bold p-6 border-b">功能權限</div>
      <table class="w-full font-sans whitespace-nowrap">
        <thead>
          <tr class="h-20 bg-gray-50 text-left border-b">
            <th class="px-6 w-1">功能名稱</th>
            <th class="px-6">可使用人員</th>
            <th class="px-6 w-1"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="row in hierarchy" class="h-20 border-b w-full">
            <td class="px-6 font-bold">{{ row.name }}</td>
            <td class="px-6 w-[75%]">
              <div class="max-w-[300px] lg:max-w-[400px] xl:max-w-[700px] 2xl:max-w-[900px] truncate">{{ selectList(row.id).map(el => el.name).join('、') || "-" }}</div>
            </td>
            <td class="h-20 px-6 flex justify-end items-center">
              <div class="cursor-pointer rounded-full hover:bg-gray-200 w-8 aspect-square flex justify-center items-center" @click="editReviewer(row.id)">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M15.4141 4.70711L5.18379 14.9374C5.04419 15.077 4.94904 15.2548 4.91032 15.4484L4 20L8.55159 19.0897C8.74518 19.051 8.92298 18.9558 9.06258 18.8162L19.2929 8.58589C19.6834 8.19537 19.6834 7.5622 19.2929 7.17168L16.8283 4.70711C16.4378 4.31658 15.8046 4.31658 15.4141 4.70711Z" stroke="#1D2939" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M14 7L17 10" stroke="#1D2939" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M5 15L9 19" stroke="#1D2939" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <Toast position="top-center" />
  <Dialog v-model:visible="selectReviewerView" :draggable="false" modal :style="{width: '488px'}">
    <template #header>
      <span class="text-2xl font-bold">可使用人員</span>
    </template>
    <div class="text-sm mt-5 ml-1 mb-1" style="color: #98A2B3">人員</div>
    <MultiSelect v-model="selectedReviewer" @click="reshape" :options="employeeList" optionValue="id" filter :optionLabel="customOptionLabel" placeholder="請選擇人員" class="w-4/6 md:w-20rem mb-16 custom-multi-select"/>
    <template #footer>
      <Button label="取消" @click="cancelSelectReviewer" severity="secondary" outlined></Button>
      <Button label="確定" @click="confirmSelectReviewer" autofocus></Button>
    </template>
  </Dialog>
</template>
  
<script>
import Dialog from 'primevue/dialog';
export default {
  components: {
    Dialog
  },
  props: ['employeeList'],
  data() {
    return {
      currentTab: 0,
      apiURL: "/api/acc/perf-bonus",
      subsidiary: '',
      selectReviewerView: false,
      selectedReviewerType: null,
      selectedReviewer: [],
      inquiryList: [],
      historyList: [],
      settingList: [],
      // employeeList: [],
      hierarchy: [],
      
    };
  },
  mounted() {
    this.subsidiary = localStorage.getItem('company_code')
    this.fetchHierarchy()
  },
  methods: {
    fetchHierarchy() {
      axios
        .get(this.apiURL + `/setting/${this.subsidiary}`)
        .then(res => {
          this.hierarchy = res.data.data
          this.inquiryList = this.hierarchy[0].employees.map(el => ({id: el.id, name: el.name}))
          this.historyList = this.hierarchy[1].employees.map(el => ({id: el.id, name: el.name}))
          this.settingList = this.hierarchy[2].employees.map(el => ({id: el.id, name: el.name}))
        })
        .catch(err => {
          console.log(err)
          this.$toast.add({ 
            severity: 'warn', 
            summary: 'Warn Message',
            detail: '獲取失敗',
            life: 3000
          });
        })
    },
    selectList(type) {
      switch (type) {
        case 1:
          return this.inquiryList
        case 2:
          return this.historyList
        case 3:
          return this.settingList
        default:
          break;
      }
    },
    customOptionLabel(option) {
        return `${option.orgName} ${option.name}`;
    },
    editReviewer(type) {
      switch (type) {
        case 1:
          this.selectedReviewer = this.inquiryList.map(el => el.id)
          break;
        case 2:
          this.selectedReviewer = this.historyList.map(el => el.id)
          break;
        case 3:
          this.selectedReviewer = this.settingList.map(el => el.id)
          break;
        default:
          break;
      }
      this.selectedReviewerType = type
      this.selectReviewerView = true
    },
    cancelSelectReviewer() {
      this.selectReviewerView = false
      this.selectedReviewerType = null
      this.selectedReviewer = []
    },
    confirmSelectReviewer() {
      this.selectReviewerView = false
      axios
        .put(this.apiURL + `/setting/${this.subsidiary}/${this.selectedReviewerType}`, {
          employeeIds: this.selectedReviewer
        })
        .then(res => {
          this.$toast.add({ severity: 'success', summary: 'Success Message', detail: '編輯成功', life: 3000 });
          const formattedSelectedReviewer = this.selectedReviewer.map(id => ({
            id: id,
            name: this.employeeList.find(el => el.id === id).name
          }))
          switch (this.selectedReviewerType) {
            case 1:
              this.inquiryList = formattedSelectedReviewer
              break;
            case 2:
              this.historyList = formattedSelectedReviewer
              break;
            case 3:
              this.settingList = formattedSelectedReviewer
              break;
            default:
              break;
          }
          this.selectedReviewerType = null
          this.selectedReviewer = []
        })
        .catch(err => {
          this.$toast.add({ severity: 'error', summary: 'Error Message', detail: '編輯失敗', life: 3000 });
          this.selectReviewerView = false
          this.selectedReviewerType = null
          this.selectedReviewer = []
        })
    },
    reshape() { // 修改 primeue-multiselect 外型
      this.$nextTick(() => {
        const multiselectPanel = document.querySelector('.p-multiselect-panel') 
        multiselectPanel.classList.add("rounded", "overflow-hidden", "w-72", "translate-y-2", "custom-multiselect-panel")
        
        const multiselectCheckbox = document.querySelector('.p-checkbox')
        multiselectCheckbox.classList.add("custom-multiselect-checkbox")
        
        const multiselectClose = document.querySelector('.p-multiselect-close')
        multiselectClose.classList.add("custom-multiselect-close")
        
        const multiselectHeader = document.querySelector('.p-multiselect-header')
        multiselectHeader.classList.add("custom-multiselect-header")
      })
    },
  },
  watch: {
    selectedReviewer(newVal) { // 修改 multiSelect 框內容
      this.$nextTick(() => {
        if(document.querySelector('.p-multiselect-label')) {
          const selectedArea = document.querySelector('.p-multiselect-label')
          selectedArea.innerText = !newVal.length ? '請選擇人員' : this.selectedReviewer.map(el => this.employeeList.find(eml => eml.id === el).name).join(', ')
        }
      })
    },
  }
};
</script>

<style scoped>
.custom-height {
  min-height: calc(100vh - 160px);
}
@media (min-width: 768px) {
  .custom-height {
    height: calc(100vh - 84px);
  }
}
</style>
