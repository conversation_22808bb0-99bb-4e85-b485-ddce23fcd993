<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Modules\acc\Services\PerfBonusService;
use Tests\TestCase;

class PerfServiceTest extends TestCase
{

    public function testAppendPerfBonusToOrgsWithEmpty(): void
    {
        try {
            /** @var PerfBonusService $perfService */
            $perfService = app(PerfBonusService::class);

            // here will throw exception
            $perfService->appendPerfBonusToOrgs();
        } catch (\Throwable $th) {
            $this->assertTrue($th->getMessage() === '錯誤：傳入空的資料');
        }
    }
}
