<?php
use GuzzleHttp\Client;

/**
 * 使用 ap system 的目錄名稱來猜測目標 ap system
 *
 * @return array
 */
function guess_ap_system_get_tag(): array {
    $my_path = strtolower(getcwd());

    if (strpos($my_path, '/mnt') === false) {
        // 本地開發可以自己改 local 這個，這個是目標 tag
        return ['local', 'FDMC-ASAP-DEV'];
    }

    if (strpos($my_path, 'dev') === false) {
        // 正式環境不需要有這個功能
        return ['_', '_'];
    }

    if (strpos($my_path, 'as') !== false) {
        return ['asap', 'FDMC-ASAP-DEV'];
    } else if (strpos($my_path, 'hr') !== false) {
        return ['hr', 'FDMC-HR-DEV'];
    } else if (strpos($my_path, 'acc') !== false) {
        return ['acc', 'FDMC-ACC-DEV'];
    } else {
        return ['_', '_'];
    }
}

/**
 * 路由匹配
 *
 * @param string $path
 * @param string $request_method
 * @return array [content-type, raw-data]
 */
function routes_match(string $path = '', string $request_method = 'GET') {
    // route explode
    // --- ex1
    // ex1. /api/v1/messages?a=20&b=30
    // ex1 result: path = /api/v1/messages, query_str = a=20&b=30
    // --- ex2
    // ex2. /api/v1/messages
    // ex2 result: path = /api/v1/messages, query_str = ''
    $cli = new MailPit('https://smtp.fdmc.com.tw');
    #$exp_route = explode('?', $request_uri);
    #$path = $exp_route[0];
    #preg_match('/^(.*php)*(?<path>.+)/', $path, $matches);
    // $path = $request_uri;

    // ##### example 1. -> /api/inboxes/asap/messages
    // asap 是目標的 ap system
    // ##### example 2. -> /api/inboxes/hr/messages
    // hr 是目標的 ap system
    // ##### example 3. -> /api/inboxes/acc/messages
    // acc 是目標的 ap system

    $param = '';
    $inboxFlag = preg_match('/inboxes(?<target_ap>\/.*)*\/messages(?<param>\/.*)*/', $path, $matches);
    if (array_key_exists('param', $matches)) {
        $param = $matches['param'];
    }
    [$targetAp, $targetTag] = guess_ap_system_get_tag();

    $tmp = explode('.', trim($param, " /\n\r\t\v\x00"));
    $id = $tmp[0];
    $type = $tmp[1] ?? '';

    switch (true) {
        case $inboxFlag && $request_method === 'GET' && $param === '': // 列表
            return $cli->getList(['tag' => $targetTag, ...$_GET]);
        case $inboxFlag && $request_method === 'GET': // 詳細
            return $cli->getMail($id, $type);
        case $inboxFlag && $request_method === 'DELETE': // 刪除
            return $cli->deleteMail($param);
        case $inboxFlag && $request_method === 'PATCH': // 更新
            return $cli->patchMail($param, []);
        default:
            return ['text/html', "<h1>$targetAp</h1>"];
    }
}

interface MailServer {
    public function getList($query = []);
    public function getMail(string|int $id, string $type);
    public function deleteMail($id);
    public function patchMail($id, $data);
}

class MailPit implements MailServer {
    protected Client $httpCli;

    public function __construct(
        protected string $baseUri,
    ) {
        $this->httpCli = new Client([
            'base_uri' => $this->baseUri,
            'verify' => false,
        ]);
    }

    public function getList($query = []) {
        $tag = $query['tag'] ?? ''; // 系統去猜測的值，不需要去管他
        $search = $query['search'] ?? ''; // 搜尋條件

        $query = "tag:{$tag}";
        if (!empty($search)) {
            $query .= $search;
        }

        $messages = $this->getMailList($query)['messages'];
        $tmpData = [];

        foreach ($messages as $message) {
            $tmpData[] = [
                'id' => $message['ID'],
                'message_id' => $message['MessageID'], // Mailpit 特有
                'subject' => $message['Subject'],
                'sent_at' => $message['Created'],
                'from_email' => $message['From']['Address'],
                'from_name' => $message['From']['Name'],
                'to_email' => $message['To'][0]['Name'],
                'to_name' => $message['To'][0]['Address'],
                'email_size' => $message['Size'],
                'is_read' => $message['Read'],
                'created_at' => $message['Created'],
                'tags' => $message['Tags'],
                'raw_text' => $message['Snippet'],
                'html_body_size' => $message['Size'],
                'html_path' => "/inboxes/messages/{$message['ID']}.html",
            ];
        }
        return ['application/json', json_encode($tmpData)];
    }

    public function getMail(string|int $id, string $type) {
        try {
            $rawHtml = '<div style="margin: 0px 8px 0px 8px;">';
            $rawHtml .= '<h2>Html content</h2><div style="border: 1px solid black;">' . $this->getMailHtml($id) . '</div>';
            $rawHtml .= '<button onclick="showDetail()" id="btn-show">展開</button>';
            $rawHtml .= '<section class="hidden-class">';
            $rawHtml .= '<h2>Text content</h2><div style="margin-bottom: 5px;">' . $this->getMailTxt($id) . '</div><hr/>';
            $rawData = $this->getMailRaw($id);
            $rawHtml .= '<pre><h2>Raw data</h2>' . $rawData . '</pre><br/><br/>';
            $rawHtml .= '</section>';
            $rawHtml .= '</div>';

            $subject = '';
            if (preg_match('/Subject:(?<subject>[\s\S]*?)(?=\nMessage-ID:)/', $rawData, $matches)) {
                $subject =  rtrim($matches['subject']);
                $subject = iconv_mime_decode($subject, 0, "UTF-8");
            }

            return ['text/html', $rawHtml];
        } catch (Exception $e) {
            $err = [
                'uri' => $_SERVER['REQUEST_URI'],
                'method' => $_SERVER['REQUEST_METHOD'],
                'script' => $_SERVER['SCRIPT_NAME'],
		        'err' => $e->getMessage(),
            ];

            return ['application/json', json_encode([
                'error' => '未知錯誤，請聯絡管理員',
                'detail' => $err,
            ])];
        }

    }

    public function deleteMail($id) {
        $id = ltrim($id, '/');
        return ['text/html', "<h1>TODO DELETE MAIL - {$id} </h1>"];
    }

    public function patchMail($id, $data) {
        $id = ltrim($id, '/');
        return ['text/html', "<h1>TODO PATCH MAIL - {$id} </h1>"];
    }

    protected function getMailList($query): array {
        $content = $this->httpCli->get('/api/v1/search', ['query' => compact('query')])->getBody();
        return json_decode($content, true);
    }

    protected function getMailHtml($id): string {
        try {
            return $this->httpCli->get("/view/{$id}.html")->getBody()->getContents();
        } catch (Exception $e) {
            return '';
        }
    }

    protected function getMailTxt($id): string {
        try {
            return $this->httpCli->get("/view/{$id}.txt")->getBody()->getContents();
        } catch (Exception $e) {
            return '';
        }
    }

    protected function getMailRaw($id): string {
        return $this->httpCli->get("/api/v1/message/{$id}/raw")->getBody()->getContents();
    }
}

