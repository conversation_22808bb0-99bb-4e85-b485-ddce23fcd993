<template>
  <div class="flex flex-col">
    <Banner :names="['零用金申請']" />
    <div class="m-6">
      <cash v-if="currentTab == 0" />
    </div>
  </div>
</template>
<script>
import Banner from "../common/Banner.vue";
import cash from "../pettyCash/cash.vue";

export default {
  components: {
    Banner,
    cash,
  },
  data: function () {
    return {
      currentTab: 0,
    };
  },
  methods: {
    FormatValue(value) {
      const formatter = new Intl.NumberFormat("en-US", {
        currency: "USD",
      });
      if (value == null) {
        return (value = "-");
      } else {
        return (value = formatter.format(value));
      }
    },
    computedDetailTotal(detail, keyName) {
      if (keyName == "c9" || keyName == "c12" || keyName == "c14") {
        if (detail.length == 0) return 0;
        let keyIndex = Object.keys(detail[0]).findIndex(
          (key) => key == keyName
        );
        let detailValues = detail.map((item) => +Object.values(item)[keyIndex]);
        return this.FormatValue(
          detailValues.reduce((total, value) => {
            return total + value;
          })
        );
      } else {
        return "";
      }
    },
  },
};
</script>
