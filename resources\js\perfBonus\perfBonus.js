import '../bootstrap';
import "primevue/resources/themes/lara-light-blue/theme.css";
import "primevue/resources/primevue.min.css";
import 'primeicons/primeicons.css';

import {createApp} from 'vue'
import PrimeVue from 'primevue/config';
import { library } from "@fortawesome/fontawesome-svg-core";
import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";
import { faChevronLeft } from "@fortawesome/free-solid-svg-icons";
import { faChevronRight } from "@fortawesome/free-solid-svg-icons";
import { faMagnifyingGlass } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

let currentPath = window.location.pathname.split('/').pop()
import inquiry from './Inquiry.vue'
import review from './Review.vue'
import history from './History.vue'
import setting from './Setting.vue'
const content = (key) => {
    switch (key) {
        case 'inquiry':
            return inquiry;
        case 'review':
            return review;
        case 'history':
            return history;
        case 'setting':
            return setting;
    }
}

import Button from 'primevue/button';
import Calendar from 'primevue/calendar';
import Checkbox from 'primevue/checkbox';
import Column from 'primevue/column';
import DataTable from 'primevue/datatable';
import ColumnGroup from 'primevue/columngroup';
import Row from 'primevue/row';
import Dropdown from 'primevue/dropdown';
import MultiSelect from 'primevue/multiselect';
import ToastService from 'primevue/toastservice';
import Toast from 'primevue/toast';
import InputNumber from 'primevue/inputnumber';
import InputText from 'primevue/inputtext';
import Timeline from 'primevue/timeline';
import ConfirmPopup from 'primevue/confirmpopup';
import ConfirmationService from 'primevue/confirmationservice';
import ProgressSpinner from 'primevue/progressspinner';
import Card from 'primevue/card';


const container = createApp(content(currentPath));

library.add(
    faArrowLeft,
    faChevronLeft,
    faChevronRight,
    faMagnifyingGlass
);
container.use(PrimeVue);
container.use(ToastService);
container.use(ConfirmationService);
container.component('Button', Button);
container.component('Card', Card);
container.component('Calendar', Calendar);
container.component('Checkbox', Checkbox);
container.component('Column', Column);
container.component('DataTable', DataTable);
container.component('ColumnGroup', ColumnGroup);
container.component('Row', Row);
container.component('Dropdown', Dropdown);
container.component('MultiSelect',MultiSelect);
container.component('Toast',Toast);
container.component('InputNumber', InputNumber);
container.component('InputText', InputText);
container.component('Timeline', Timeline);
container.component('ConfirmPopup', ConfirmPopup);
container.component('ProgressSpinner', ProgressSpinner);
container.component("font-awesome-icon", FontAwesomeIcon);
container.mount("#content");


