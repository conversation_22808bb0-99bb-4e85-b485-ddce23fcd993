<?php

use App\Modules\acc\models\Employee;

if (!function_exists('perf_bonus_route_generate')) {
    /**
     * 績效獎金的路由產生器
     * @param int $employeeId 員工ID
     *
     * @return array ['type' => '', 'name' => '', 'url' => '', 'icon' => '', 'active' => false, 'drop' => false, 'drop_lists' => []]
     */
    function perf_bonus_route_generate(int $employeeId) {
        $perfRoutes = [
            'type'=> 'dollar',
            'name' => '績效獎金',
            'url' => '#',
            'icon' => 'hand-holding-dollar',
            'active' => false,
            'drop' => false,
        ];

        // 預設大家都有的下拉選單
        $dropLists = [
            [
                "name" => "獎金核定",
                "url" => "/acc/perfbonus/review",
                "active" => false,
            ]
        ];

        $employeeData = Employee::with(['perfPermissions'])->findOrFail($employeeId);
        // 專案撈取的權限
        if ($employeeData->perfPermissions->first(fn ($it) => $it->slug === 'group::first')) {
            array_unshift($dropLists, [
                "name" => "專案撈取",
                "url" => "/acc/perfbonus/inquiry",
                "active" => false,
            ]);
        }

        // 結果查詢的權限
        if ($employeeData->perfPermissions->first(fn ($it)  => $it->slug === 'group::history')) {
            $dropLists[] = [
                "name" => "結果查詢",
                "url" => "/acc/perfbonus/history",
                "active" => false,
            ];
        }

        // 相關設定的權限
        if ($employeeData->perfPermissions->first(fn ($it)  => $it->slug === 'group::settings')) {
            $dropLists[] =[
                "name" => "相關設定",
                "url" => "/acc/perfbonus/setting",
                "active" => false,
            ];
        }

        $perfRoutes['drop_lists'] = $dropLists;
        return $perfRoutes;
    }
}


if (!function_exists('project_payment_route_generate')) {
    function project_payment_route_generate()
    {
        return [
            "type"=> "pay",
            "name" => "專案付款",
            "url" => "/acc/project-payment",
            "route" => "projectPayment",
            "icon" => "credit-card",
            "active" => false,
        ];
    }
}

if (!function_exists('project_report_route_generate')) {
    function project_report_route_generate() {
        return [
            "type"=> "report",
            "name" => "報表查詢",
            "url" => "#",
            "icon" => "file-lines",
            "active" => false,
            "drop" => false,
            "drop_lists" => [
                [
                    "name" => "專案付款",
                    "url" => "/acc/project-pay-search",
                    "route" => "projectPaySearch",
                    "active" => false,
                ],
                [
                    "name" => "零用金申請",
                    "url" => "/acc/cash",
                    "route" => "pettyCash",
                    "active" => false,
                ],
            ],
        ];
    }
}
