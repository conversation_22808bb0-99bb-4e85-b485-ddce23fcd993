<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ExportSendFaxLog extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:export-send-fax-log {--startYm=} {--endYm=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '匯出傳真發送記錄到 Excel';

    private $columns = [
        'id' => 'ID',
        'pkey' => '專案金鑰',
        'startYm' => '開始月份',
        'endYm' => '結束月份',
        'pbatchno' => '批次',
        'epjacc' => '會計代號',
        'epjno' => '專案編號',
        'epjna' => '專案簡稱',
        'venno' => '廠商代號',
        'venna' => '廠商簡稱',
        'copname' => '廠商名稱',
        'sendStatus' => '發送狀態',
        'faxNo' => '傳真號碼',
        'subject' => '傳送單號',
        'creator_name' => '建立者',
        'created_at' => '建立時間',
        'updated_at' => '更新時間'
    ];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startYm = $this->option('startYm');
        $endYm = $this->option('endYm');

        $this->info('開始匯出傳真發送記錄...');
        $this->info('startYm: ' . $startYm);
        $this->info('endYm: ' . $endYm);

        try {
            // 執行 SQL 查詢

            $data = $this->executeReportQuery($startYm, $endYm);
            // $data = $this->executePayQuery($startYm, $endYm);

            // 建立 Excel
            $this->createExcel($data);

            $this->info('匯出完成！');
        } catch (\Exception $e) {
            $this->error('匯出失敗：' . $e->getMessage());
        }
    }
    private function executePayQuery($startYm, $endYm)
    {
        $sql = "
            select 
                sl.id, 
                sl.pkey, 
                sl.\"startYm\", 
                sl.\"endYm\", 
                sl.pbatchno, 
                sl.epjacc, 
                sl.epjno, 
                p.epjna,
                sl.venno, 
                pd.venna,
                pd.copname,
                sl.\"sendStatus\",
                sl.payload->>'faxNo' as faxNo, 
                sl.payload->>'subject' as subject, 
                e.payload->>'name' as creator_name,
                sl.created_at, 
                sl.updated_at
            from send_logs sl 
            left join employees e on e.id = sl.created_by
            left join (
                select 
                    pkey,
                    pbatchno,
                    symno,
                    payload->>'venno' as venno,
                    max(payload->>'venna') as venna,
                    max(payload->>'copname') as copname
                from pj_pay_details
                group by pkey, pbatchno, symno, payload->>'venno'
            ) pd on pd.venno = sl.venno 
                and pd.pkey = sl.pkey 
                and pd.pbatchno = sl.pbatchno 
                and pd.symno = sl.\"startYm\"
            left join (
                select 
                    pkey,
                    max(payload->>'epjna') as epjna
                from pj_pays
                group by pkey
            ) p on p.pkey = sl.pkey
            where sl.\"sendStatus\" <> 'send-email' 
                and sl.created_at between '$startYm-01 00:00:00' and '$endYm-31 23:59:59'
            order by sl.created_at asc
        ";

        return DB::select($sql);
    }

    private function executeReportQuery($startYm, $endYm)
    {
        $sql = "
            select 
                sl.id, 
                sl.pkey, 
                sl.\"startYm\", 
                sl.\"endYm\", 
                sl.pbatchno, 
                sl.epjacc, 
                sl.epjno, 
                pr.epjna,
                sl.venno, 
                pr.venna,
                pr.copname,
                sl.\"sendStatus\",
                sl.payload->>'faxNo' as faxNo, 
                sl.payload->>'subject' as subject, 
                e.payload->>'name' as creator_name,
                sl.created_at, 
                sl.updated_at
            from send_logs sl 
            left join employees e on e.id = sl.created_by
            left join (
                select 
                    pkey,
                    payload->>'pbatchno' as pbatchno,
                    payload->>'symno' as symno,
                    list->>'venno' as venno,
                    max(payload->>'epjna') as epjna,
                    max(list->>'venna') as venna,
                    max(list->>'copname') as copname
                from pj_reports
                where payload->>'symno' between '$startYm' and '$endYm'
                group by pkey, payload->>'pbatchno', payload->>'symno', list->>'venno'
            ) pr on pr.venno = sl.venno 
                and pr.pkey = sl.pkey 
                and pr.pbatchno = sl.pbatchno 
                and pr.symno = sl.\"startYm\"
            where sl.\"sendStatus\" <> 'send-email' 
                and sl.created_at between '$startYm-01 00:00:00' and '$endYm-31 23:59:59'
            order by sl.created_at asc
        ";

        return DB::select($sql);
    }

    private function createExcel($data)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // 設定欄位標題
        $column = 1;
        foreach ($this->columns as $key => $title) {
            $sheet->setCellValueByColumnAndRow($column, 1, $title);
            $column++;
        }

        // 寫入資料
        $row = 2;
        foreach ($data as $item) {
            $column = 1;
            foreach ($this->columns as $key => $title) {
                $value = $item->$key ?? '';
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column++;
            }
            $row++;
        }

        // 設定欄寬
        foreach (range('A', $sheet->getHighestColumn()) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // 儲存檔案
        $writer = new Xlsx($spreadsheet);
        $filename = '傳真發送記錄_' . date('YmdHis') . '.xlsx';
        $writer->save(storage_path('app/public/' . $filename));

        $this->info('檔案已儲存至：' . storage_path('app/public/' . $filename));
    }
}
