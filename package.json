{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"autoprefixer": "^10.4.14", "axios": "^1.1.2", "laravel-vite-plugin": "^0.7.5", "postcss": "^8.4.23", "tailwindcss": "^3.3.2", "vite": "^4.0.0"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@vitejs/plugin-vue": "^4.2.3", "lodash": "^4.17.21", "primeicons": "^6.0.1", "primevue": "^3.29.1", "vue": "^3.2.36", "vue-loader": "^17.0.1"}}