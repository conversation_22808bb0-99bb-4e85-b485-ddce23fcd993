<?php

namespace App\Modules\acc\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Traits\FormatDate;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Modules\acc\models\FuncAuth;
use App\Modules\SSO\Controllers\AuthController as ControllersAuthController;
use App\Traits\CompanyTrait;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class AuthController extends CommonController
{
    use FormatDate, CompanyTrait;
    protected $user;
    protected $CompanyId;
    protected $timezone;
    public function __construct()
    {
        $this->user = Session::get('employee_id');
        $this->CompanyId = Session::get('CompanyId');
        $this->timezone = Session::get('timezone');
    }

    public function  fetchFuncAuth()
    {

        $authList = FuncAuth::with('users')
                    ->where('metadata->isShow', true)
                    ->orderby('payload->sort')
                    ->get();
        // dd($authList);
        $result = $authList->map(function ($query) {
            $users = $query->users->slice(0, 5);
            $user = [];
            foreach ($users as $key => $value) {
                // $user[] = [
                //     'name' => $value->payload['name']
                // ];
                array_push($user, $value->payload['name']);
            }
            return [
                'id' => $query->id,
                'name'=>$query->payload->get('name'),
                'userList'=>$query->payload->get('user_list'),
                'users' => $user,
            ];
        });

        return $result;
    }

    public function  updateFuncAuth(Request $request)
    {
        if (empty($request->id) || empty($request->users))
            return 0;

        if (!is_array($request->users))
            return 0;
        $functionAuth =  FuncAuth::where('id', $request->id)->update(
            [
                'payload->user_list' => $request->users
            ]
        );

        return  $functionAuth;
    }

    public function updateSetSessionCompanyId(Request $request, $company)
    {
        if (empty($company)) {
            return response()->noContent();
        }
        $companyId = Session::get('CompanyId');
        if (!empty($company)) {
            $companyId = $this->getCompanyId($company);
        }

        Session::put('CompanyId', $companyId);
        $targetUrl = app()->get(ControllersAuthController::class)->noSpecialUriGetFirstPath();
        return redirect($targetUrl);
    }
}
