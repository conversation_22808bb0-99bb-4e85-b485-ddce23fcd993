<template>
  <Banner :names="['績效獎金核定', '核定紀錄']" />
  <OnHand v-if="!currentTab" mode="review" :employeeList="employeeList" :subsidiary="subsidiary">
    <template #table="tableProps">
      <div class="grow">
        <table class="w-full h-full text-left font-sans flex flex-col">
          <thead ref='scrollHeader' class="overflow-hidden overflow-x-scroll scroll-header" @scroll="tableProps.verticalAlignTable">
            <tr class="table w-full bg-gray-50 h-20 border-b border-gray-200" style="min-width: 1200px;">
              <th v-for="header in tableHeader.slice(0, 7)" class="px-6">{{ header.displayName }}</th>
            </tr>
          </thead>
          <ScrollPanel class="custom-bar h-1 grow">
            <tbody>
              <tr v-for="row,rowIndex in tableProps.dataList.data" style="min-width: 1200px;" class="table table-fixed w-full h-20 border border-gray-200 hover:border-blue-400 cursor-pointer" :class="{'bg-gray-50': rowIndex%2===1 }" @click="tableProps.openReviewProject(row.id)">
                <td v-for="col in tableHeader.slice(0, 7)" class="px-6">{{ col.type === 'money' ? (tableProps.formatValue(row[col.section]) || '') : (row[col.section] || '') }} </td>
              </tr>
            </tbody>
          </ScrollPanel>
        </table>
      </div>
    </template>
  </OnHand>
  <Search v-else mode="review" :employeeList="employeeList" :subsidiary="subsidiary">
    <template #dataHeader>
      <span class="text-lg font-bold">核定紀錄</span>
    </template>
    <template #table="tableProps">
      <table class="grow w-full h-max flex flex-col">
        <thead  ref="scrollHeader" class="overflow-x-scroll overflow-hidden scroll-header text-left" @scroll="tableProps.verticalAlignTable">
          <tr class="h-20 table w-full table-fixed border-y bg-gray-50" style="min-width: 1200px;">
            <th v-for="header in tableHeader" class="pl-6">
              {{ header.displayName }}
            </th>
          </tr>
        </thead>
        <ScrollPanel class="custom-bar h-1 grow">
          <tbody>
            <tr v-for="row,rowIndex in tableProps.dataList.data" class="text-center table h-20 w-full table-fixed border border-gray-100 hover:border-blue-400 cursor-pointer" :class="{'bg-gray-50': rowIndex%2===1 && !row.selected, 'bg-blue-50': row.selected}" @click="tableProps.openResultProject(row.id)" style="min-width: 1200px;">
              <td v-for="col in tableHeader" class="text-left pl-6"> {{ col.type === 'money' ? tableProps.formatValue(row[col.section]) : row[col.section] }}</td>
            </tr>
          </tbody>
        </ScrollPanel>
      </table>
    </template>
  </Search>
</template>

<script>
import Banner from "@/common/Banner.vue";
import OnHand from "@/perfBonus/common/OnHand.vue";
import Search from "@/perfBonus/common/Search.vue";
import ScrollPanel from 'primevue/scrollpanel';
export default {
  components: {
    Banner,
    OnHand,
    Search,
    ScrollPanel
  },  
    data() {
      return {
        currentTab: 0,
        employeeList: [],
        subsidiary: '',
        apiURL: "/api/acc/perf-bonus",
        tableHeader: [
          {displayName: '專案代號', section: 'epjno', type: "string"},
          {displayName: '會計代號', section: 'accId', type: "string"},
          {displayName: '專案簡稱', section: 'projectName', type: "string"},
          {displayName: '合約總價', section: 'totalPay', type: "money"},
          {displayName: '績效年月', section: 'closeYm', type: "string"},
          {displayName: '績效額度', section: 'amount', type: "money"},
          {displayName: '建議額度', section: 'suggestionAmount', type: "money"},
          {displayName: '核定金額', section: 'totalPay', type: "money"},
        ],
      };
    },
    mounted() {
      this.subsidiary = localStorage.getItem('company_code')
      this.fetchEmployeeList()
    },
    methods: {
      fetchEmployeeList() {
        axios
        .get(this.apiURL + `/f/employees/${this.subsidiary}`)
        .then(res => {
          this.employeeList = res.data.data
        })
        .catch(err => {
          console.log(err)
        })
      },
      async search() {
        this.fetching = 1
        await axios
          .get(this.apiURL + '/s/result',  {
            params: {}
          })
          .then(res => {
            this.searched = 1
            this.fetching = 0
            this.dataList = res.data
          })
          .catch(err => {
            this.searched = 1
            this.fetching = 0
          })

          if(document.querySelector('.p-scrollpanel-content')) {
          document.querySelector('.p-scrollpanel-content').addEventListener('scroll', () => {
            if(this.$refs.scrollHeader.scrollLeft != document.querySelector('.p-scrollpanel-content').scrollLeft) {
              this.$refs.scrollHeader.scrollLeft = document.querySelector('.p-scrollpanel-content').scrollLeft
            }
          })
        }  

      },
    },
};
</script>
<style scoped>
.scroll-header::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
</style>