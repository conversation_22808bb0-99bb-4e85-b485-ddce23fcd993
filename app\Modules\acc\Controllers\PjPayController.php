<?php

namespace App\Modules\acc\Controllers;

use App\Http\Controllers\Controller;
use App\Jobs\BellErpGetJob;
use App\Modules\acc\models\PjPay;
use App\Modules\acc\models\PjPayDetail;
use App\Modules\acc\models\PjReport;
use App\Modules\acc\models\SignLog;
use App\Modules\acc\Resources\PayDetailResourceCollection;
use App\Modules\acc\Resources\PayResourceCollection;
use App\Modules\SSO\Controllers\SSOController;
use App\Traits\FormatDate;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class PjPayController extends Controller
{
    use FormatDate;
    protected $user;
    protected $timezone;
    public function __construct()
    {
        $this->user = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
    }

    public function getPayList(Request $request)
    {
        if ($request->missing(['startYm', 'endYm', 'pbatch'])) {
            return response('缺少必要資訊', 400);
        }

        $startYm = $this->formatDate($request->input('startYm'), 'ym');
        $endYm = $this->formatDate($request->input('endYm'), 'ym');

        // dd($startYm, $endYm);
        $pbatchno = $request->input('pbatch');
        // $pbatchno = Arr::pluck($request->input('pbatchno'), 'code');
        $epjacc = $request->input('epjacc');
        $forceFetch = filter_var(
            $request->get('forceFetch'),
            FILTER_VALIDATE_BOOLEAN
        );
        $page = (int) $request->input('page') ?? 1;
        $per = $request->input('per') ?? 50;


        // dd( $startYm, $endYm, $pbatchno, $epjacc, $forceFetch);
        // $details = PjPayDetail::
        //         whereBetween('symno', [$startYm, $endYm])
        //         ->whereIn('pbatchno', $pbatchno)
        //         ->get();
        //         return $details;

        $query = function ($q) use ($startYm, $endYm, $pbatchno, $epjacc) {
            $q->whereBetween('symno', [$startYm, $endYm])
                ->whereIn('pbatchno', $pbatchno)
                ->when($epjacc, function ($query) use ($epjacc) {
                    return $query->where('epjacc', 'like', "%$epjacc%");
                });
        };

        $pj = PjPay::whereHas('detail', $query)
            ->with(['pd', 'pc', 'pm']);

        // 不強制撈取 且 批次單選 要確認是否已送審過
        if (!$forceFetch && count($pbatchno) == 1) {
            $pj->whereDoesntHave('signlog', function ($q) use ($startYm, $endYm, $pbatchno, $epjacc) {
                $q->where(function ($query) use ($startYm, $endYm) {
                    $query->where('startYm', '<=', $endYm)
                        ->where('endYm', '>=', $startYm);
                })
                    // ->whereBetween('symno', [$startYm, $endYm])
                    ->whereIn('pbatchno', $pbatchno)
                    ->when($epjacc, function ($query) use ($epjacc) {
                        return $query->where('epjacc', 'like', "%$epjacc%");
                    });
            });
        }
        // detail的本期請款加總
        $pay_detail = DB::table('pj_pay_details')
            ->whereBetween('symno', [$startYm, $endYm])
            ->whereIn('pbatchno', $pbatchno)
            ->groupBy('epjacc', 'epjno')
            ->select(
                'epjacc',
                'epjno',
                DB::raw('sum((payload->>\'venpay\')::integer) as sumvenpay')
            );
        if ($epjacc)
            $pay_detail->where('epjacc', 'like', "%$epjacc%");
        ;

        // 用來計算venper 欄位而已
        $subQuery = DB::raw('(select  case when sum(case when t.rn > 1 then 0 else t.budnew end)=0
                    then 0
                    else round((SUM(t.vencamt)/sum(case when t.rn > 1 then 0 else t.budnew end))::Decimal*100,2)
                    end
                    as venper
                    ,t.epjno
                    from (
                        select
                        ROW_NUMBER() OVER ( PARTITION BY budcno,pjbno,budkey ORDER BY pjbno,budcno,budkey,venno ) as rn
                        ,ve.epjno
                        ,case when budcno=\'無\' And budnew=0 then
                            (
                                 case when odate <> \'\'  and sdate <> \'\' then
                                    case when TO_CHAR(NOW():: DATE, \'yyyymmdd\') >= odate then vencamt+vendcp+( case when tvencamt=0 then vendp1 else Round((vencamt+vendcp)*vendp1/tvencamt,2) end )
                                    else
                                        (vencamt+vendcp+( case when tvencamt=0 then vendp1 else Round((vencamt+vendcp)*vendp1/tvencamt,2) end))
                                        *
                                        ( case when (extract(day from current_timestamp - sdate::timestamp)+1) > 0 then (extract(day from current_timestamp - sdate::timestamp)+1) else 0 end   )
                                        /
                                        (extract(day from odate::timestamp - sdate::timestamp)+1)
                                    end
                                else
                                    case when tvencamt = 0 then venpay + dpay
                                    else Round((vencamt + vendcp)*(venpay + dpay)/ tvencamt, 2)
                                    end
                                end
                            )
                        else
                            budnew
                        end  as budnew
                        ,vencamt
                        from venbuds ve
                    ) t
                    group by t.epjno) t
        ');

        $pj->leftJoin($subQuery, 't.epjno', '=', 'pj_pays.epjno')
            ->leftJoinsub($pay_detail, 'pd', function ($join) {
                $join->on('pd.epjacc', '=', 'pj_pays.epjacc')
                    ->on('pd.epjno', '=', 'pj_pays.epjno');
            })
            ->orderby('pj_pays.epjacc');

        // $result = $pj->get();
        $result = $pj->paginate($per); //, ['*'], 'page', $page
        return new PayResourceCollection($result);
    }

    public function getPayDetailList(Request $request)
    {
        if (!$request->has(['startYm', 'endYm', 'pbatch', 'pkey']))
            return response('缺少必要資訊', 400);

        $startYm = $this->formatDate($request->input('startYm'), 'ym');
        $endYm = $this->formatDate($request->input('endYm'), 'ym');
        $pbatch = $request->input('pbatch');
        // $pbatchno = Arr::pluck($request->input('pbatchno'), 'code');
        $pkey = $request->input('pkey');

        $details = PjPayDetail::where('pkey', $pkey)
            ->whereBetween('symno', [$startYm, $endYm])
            ->whereIn('pbatchno', $pbatch)
            ->orderby('payload->venno')
            ->get();

        return new PayDetailResourceCollection($details);
    }
    public function fetchEpj()
    {
        return PjPay::query()
            ->select('epjno', 'epjacc', 'payload->epjna AS epjname')
            ->get()
            ->toArray();
    }
    public function submitPay(Request $request)
    {
        if (!$request->has(['startYm', 'endYm', 'pbatch', 'projects']))
            return response('缺少必要資訊', 400);

        $startYm = $this->formatDate($request->input('startYm'), 'ym');
        $endYm = $this->formatDate($request->input('endYm'), 'ym');
        $pbatch = $request->input('pbatch');
        $projects = $request->input('projects');


        $details = PjPayDetail::wherein('pkey', Arr::pluck($projects, 'pkey'))
            ->whereBetween('symno', [$startYm, $endYm])
            ->where('pbatchno', $pbatch)
            ->selectRaw("pkey ,epjacc ,epjno ,(payload || jsonb_build_object('unkey', unkey)) as payload")
            ->get()
            ->groupby('pkey');

        try {
            // 打api ，資料到asap 去做
            $urlToken = (new SSOController())->getToken();
            // $url = config('app.sso_asap_url') . '/acc?token=' . $urlToken;
            $url = config('app.fdmc_sys.asap') . '/api/sso/acc_submit?token=' . $urlToken;

            // 內網沒掛證書，用https 打時會出現ssl問題打不過
            //$resopnseUrl = $request->schemeAndHttpHost() . config('app.resopnse_to_report_url');
            $resopnseUrl = 'http://' . $request->host() . config('app.resopnse_to_report_url');

            $pj = [
                'startYm' => $startYm,
                'endYm' => $endYm,
                'pbatch' => $pbatch,
                'projects' => $projects,
                'details' => $details,
                'isMultiple' => true,
                'resopnseUrl' => $resopnseUrl,
            ];

            //遇到 cURL error 60: SSL certificate problem: certificate has expired
            //請更新 C:\laragon\etc\ssl\cacert.pem
            $response = Http::post($url, $pj);


            if ($response->ok()) {
                $signLog = [];
                foreach ($projects as $key => $value) {
                    $signLog[] = $this->signLog($value, $startYm, $endYm, $pbatch);
                }

                SignLog::insert($signLog);
                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            return 0;
        }
    }

    public function submitDetail(Request $request)
    {
        if (!$request->has(['startYm', 'endYm', 'pbatch', 'pjInfo', 'details']))
            return response('缺少必要資訊', 400);


        $startYm = $this->formatDate($request->input('startYm'), 'ym');
        $endYm = $this->formatDate($request->input('endYm'), 'ym');
        $pbatch = $request->input('pbatch');
        $pjInfo = $request->input('pjInfo');
        $details = $request->input('details');

        $urlToken = (new SSOController())->getToken();
        try {
            // 打api ，資料到asap 去做

            $url = config('app.fdmc_sys.asap') . '/api/sso/acc_submit?token=' . $urlToken;
            $resopnseUrl = config('app.app_domain') . config('app.resopnse_to_report_url');
            $pj = [
                'startYm' => $startYm,
                'endYm' => $endYm,
                'pbatch' => $pbatch,
                'pjInfo' => $pjInfo,
                'details' => collect($details)->flatten(1),
                'isMultiple' => false,
                'resopnseUrl' => $resopnseUrl,
            ];
            $response = Http::post($url, $pj);

            if ($response->ok()) {

                $signLog = $this->signLog($pjInfo, $startYm, $endYm, $pbatch);
                SignLog::insert($signLog);
                return 1;
            } else {
                Log::error($response);

                return 0;
            }
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);

            return 0;
        }
    }

    public function signLog($data, ...$info)
    {
        return [
            // 'pkey' => $data['pkey'],
            'startYm' => $info[0],
            'endYm' => $info[1],
            'pbatchno' => $info[2],
            'epjacc' => $data['epjacc'],
            'epjno' => $data['epjno'],
            'payload' => json_encode([]),
            'created_by' => $this->user,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
    public function onGoing(Request $request)
    {
        $epjacc = $request->input('epjacc');

        $payReport = PjReport::where('created_by', $this->user)
            ->where('payload->status', 1)
            ->when($epjacc, function ($query) use ($epjacc) {
                return $query->where('payload->epjacc', 'like', '%' . $epjacc . '%');
            })
            ->get();

        $employees = CommonController::fetchEmployees($includeTrashed = true);


        // $codeTable = Code::where('code_kind', 'AA')->get();
        $result = $payReport
            ->groupby('id')
            ->map(function ($item) use ($employees) {
                $signRoles = $item[0]->payload->get('sign_roles');
                $roles = collect($employees)->whereIn('id', collect($signRoles)->pluck('role_id'))->values();
                foreach ((array) $signRoles as $roleIndex => $role) {
                    $signRoles[$roleIndex]['name'] = $roles->firstWhere('id', $role['role_id']) ? $roles->firstWhere('id', $role['role_id'])['name'] : '';
                    if (isset($role['isRepresent']) && $role['isRepresent']) {
                        $signRoles[$roleIndex]['name'] .= '(代)';
                    }
                }

                return [
                    'as_id' => $item[0]->id,
                    'as_no' => $item[0]->no,
                    'epjacc' => $item[0]->payload->get('epjacc'),
                    'epjno' => $item[0]->payload->get('epjno'),
                    'epjna' => $item[0]->payload->get('epjna'),
                    'startYm' => $item[0]->payload->get('startYm'),
                    'endYm' => $item[0]->payload->get('endYm'),
                    'status' => $item[0]->payload->get('status'),
                    'created_at' => $this->formatDate($item[0]->created_at, 'date_time'),
                    'sign_roles' => $signRoles,
                    'details' => $item->pluck('list')
                ];
            })
            ->values();

        return $result;
    }

    public function cancelPay(Request $request, $ac_id)
    {
        try {
            if (empty($ac_id))
                return response('缺少必要資訊', 400);
            // 打api ，資料到asap 去做
            $urlToken = (new SSOController())->getToken();
            // $url = config('app.sso_asap_url') . '/acc?token=' . $urlToken;
            $url = config('app.fdmc_sys.asap') . '/api/sso/acc_cancel_pay?token=' . $urlToken;

            $pj = [
                'id' => $ac_id,
            ];
            $response = Http::post($url, $pj);

            // record log
            if ($response->ok()) {
                return 1;
            } else {
                return 0;
            }
        } catch (Exception $e) {
            // DB::rollBack();
            Log::error($e);
            return 0;
        }
    }

    //從erp擷取單一專案   依據專案代碼
    public function fetchERPProject(Request $request)
    {
        try {
            if (!$request->has(['symno', 'batch']) || !$request->filled('erpEpjno'))
                return response('缺少必要資訊', 400);

            $symno = $request->input('symno');
            $batch = $request->input('batch');
            $epjno = $request->input('erpEpjno');

            $url = config('app.fdmc_sys.ms') . '/api/acc/fetch/erp';

            // $url = "http://127.0.0.1:333/api/acc/fetch/erp";
            $pj = [
                'symno' => $symno,
                'batch' => $batch,
                'erpEpjno' => $epjno,
            ];
            $response = Http::post($url, $pj);
            if ($response->ok()) {
                return response(1, 204);
            } else {
                return response(0, 500);
            }
        } catch (\Throwable $th) {
            \Log::error($th);
            return response([], 500);
        }
    }
}
