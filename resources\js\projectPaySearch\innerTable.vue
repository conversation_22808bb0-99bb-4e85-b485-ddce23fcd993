<template>
<div>
  <DataTable
    :value="rowData.data.list"
    dataKey="venno"
    :metaKeySelection="false"
    :scrollable="true"
    tableStyle="min-width: 42rem"
    class="expansionTable w-fit"
  >
    <Column
        frozen
        headerStyle="width: 1rem"
    >
        <template #header>
            <input
                type="checkbox"
                class="cursor-pointer"
                v-model="rowData.data.allSelected"
                @change="selectAllRowDetails"
            >
        </template>
        <template #body="slotProps">
            <input
                type="checkbox"
                class="cursor-pointer"
                v-model="slotProps.data.selected"
                @change="selectRowDetail"
            >
        </template>
    </Column>
    <Column v-for="column in showColumns.filter(column => column !== 'submitType')" :field="column" :header="columnsMap[column]">
        <template #body="slotProps">
            <div :class="{
                'text-right': typeof slotProps.data[column] === 'number',
                'text-left': typeof slotProps.data[column] !== 'number'
            }">
                {{
                typeof slotProps.data[column] === 'number' ? formatValue(slotProps.data[column]) : slotProps.data[column]
                }}
            </div>
        </template>
    </Column>
    <Column v-if="showColumns.includes('submitType')" field="submitType" header="發送狀態">
        <template #body="slotProps">
            <div class="flex flex-col">
                <span>{{
                    slotProps.data.submitTime
                }}</span>
                <span
                    :class="
                        changeState(
                            slotProps.data.submitType
                        )
                    "
                >
                    {{ slotProps.data.submitType }}
                </span>
            </div>
        </template>
    </Column>
  </DataTable>
</div>
</template>

<script>
export default {
    data() {
        return {
            columnsMap: {
                epjacc: "會計代號",
                epjna: "專案簡稱",
                payrmk: "請款摘要",
                vencamt: "發包總價",
                tvenpay: "發包已付",
                npay: "本期請款",
                ppay: "本期預付",
                hpay: "本期保留",
                dpay: "本期折讓",
                venpay: "本期實付",
                tax: "稅金",
                submitType: "發送狀態",
            },
        }
    },
    props: {
        projects: {
            type: Object,
            required: true,
        },
        rowData: {
            type: Object,
            required: true,
        },
        showColumns: {
            type: Array,
            default: () => [
                "epjacc",
                "epjna",
                "payrmk",
                "vencamt",
                "tvenpay",
                "npay",
                "ppay",
                "hpay",
                "dpay",
                "venpay",
                "tax",
                "submitType",
            ],
        },
    },
    watch: {
        // detail 連動 row allSelected
        'rowData.data.list': {
            handler: function () {
                if(this.rowData.data.list.some(detail => !detail.selected)) {
                    this.rowData.data.allSelected = false
                } else {
                    this.rowData.data.allSelected = true
                }
            },
            deep: true
        }
    },
    methods: {
        formatValue(value) {
            const formatter = new Intl.NumberFormat("en-US", {
                currency: "USD",
            });
            if (value == null) {
                return (value = "-");
            } else {
                return (value = formatter.format(value));
            }
        },
        changeState(type) {
            switch (type) {
                case "未發送":
                    return "text-gray-500 border-gray-400";
                case "已送出傳真":
                    return "text-yellow-500 border-gray-400";
                case "傳真發送失敗":
                    return "text-red-500 border-gray-400";
                default:
                    return "text-green-500 border-green-300";
                // case "已寄信":
                // case "已傳真":
                //     return "text-green-400 border-green-300";
                // default:
                //     return "text-gray-400 border-gray-400";
            }
        },
        selectRowDetail(event) {
            if(event.target.checked) {
                this.projects.data[this.rowData.index].selected = true
            } else {
                if(this.rowData.data.list.every(detail => !detail.selected)) {
                    this.projects.data[this.rowData.index].selected = false
                }
            }
        },
        selectAllRowDetails(event) {
            if(event.target.checked) {
                this.projects.data[this.rowData.index].selected = true
                this.rowData.data.list.forEach(detail => {
                    detail.selected = true
                })
            } else {
                this.projects.data[this.rowData.index].selected = false
                this.rowData.data.list.forEach(detail => {
                    detail.selected = false
                })
            }

        },
    }
}

</script>

<style scoped>

</style>
