<template>
  <Banner :names="['專案撈取', '核定進度']" />
  <Search v-if="!currentTab" mode="submit" :forcedFetch="true" :subsidiary="subsidiary" :employeeList="employeeList">
    <template #dataHeader="dataHeaderProps">
      <span class="text-lg font-bold">績效額度設定</span>
      <ConfirmPopup style="width: 244px" class="custom-confirm-popup">
        <template #message="slotProps">
          <div class="p-6 pb-8">
            <p class="pl-2">{{ slotProps.message.message }}</p>
          </div>
        </template>
      </ConfirmPopup>
      <Button :disabled="!dataHeaderProps.selectedProjects.length" label="送出核定" @click="dataHeaderProps.confirm($event)"></Button>
    </template>
    <template #table="tableProps">
      <table class="grow w-full h-max flex flex-col">
        <thead ref="scrollHeader" class="overflow-x-scroll overflow-hidden scroll-header font-extrabold" @scroll="tableProps.verticalAlignTable">
          <tr style="min-width: 1572px;" class="h-20 table w-full table-fixed border-y bg-gray-50">
            <th>
              <label for="selectAll" class="block h-full w-full flex justify-center cursor-pointer">
                <input v-model="tableProps.selectAll" type="checkbox" class="scale-150 cursor-pointer" id="selectAll" @change="tableProps.selectAllProjects($event)">
              </label> 
            </th>
            <th v-for="header in tableHeader">
              {{ header.displayName }}
            </th>
          </tr>
        </thead>
        <ScrollPanel class="custom-bar h-1 grow">
          <tbody>
            <tr v-for="row,rowIndex in tableProps.dataList.data" :id="`row_${rowIndex}`" style="min-width: 1572px;" class="text-center table w-full table-fixed border border-gray-100 hover:border-blue-400 " :class="{'bg-gray-50': rowIndex%2===1 && !row.selected, 'bg-blue-50': row.selected}" >
              <td>
                <label :for="`select${rowIndex}`" class="block h-40 w-full cursor-pointer flex items-center justify-center">
                  <input type="checkbox" class="scale-150 cursor-pointer" :id="`select${rowIndex}`" @change="tableProps.selectProject($event, row);" v-model="row.selected">
                </label>
              </td>
              <template v-for="col in tableHeader">
                <!-- 專案訊息行 -->
                <td v-if="'section' in col" :class="{'text-left': col.displayName === '專案簡稱'}"> {{ col.displayName === '合約總價' ? tableProps.formatValue(row[col.section]) : row[col.section] }}</td>
                <!-- 小計 -->
                <td v-else-if="col.displayName === '小計'" class="py-6 px-1 align-top">
                  <div class="mb-3 overflow-scroll whitespace-nowrap scroll-header" style="line-height: 50px;">{{ tableProps.formatValue(tableProps.sumRow(row, 'amount')) }}</div> 
                  <div class="mb-3 overflow-scroll whitespace-nowrap scroll-header" style="line-height: 50px;">{{ tableProps.formatValue(tableProps.sumRow(row, 'suggestAmount')) }}</div> 
                </td>
                <!-- 輸入行 -->
                <td v-else class="py-6 px-1 align-top">
                  <template v-if="!row.organizations.map(el=>el.code).includes(col.code)">
                    <InputNumber :disabled="true" class="custom-input mb-3" :inputStyle="{'background-color': '#F9FAFB'}" placeholder="績效額度"/>
                    <InputNumber :disabled="true" class="custom-input mb-3" :inputStyle="{'background-color': '#F9FAFB'}" placeholder="建議額度"/>
                  </template>
                  <template v-else>
                    <InputNumber class="custom-input mb-3" locale="zh-TW" placeholder="績效額度" :modelValue="row.organizations[tableProps.currentIndex(row, col)].amount" @input="tableProps.setAmount($event, row, col)"/>
                    <InputNumber class="custom-input mb-3" locale="zh-TW" placeholder="建議額度" :modelValue="row.organizations[tableProps.currentIndex(row, col)].suggestAmount" @input="row.organizations[tableProps.currentIndex(row, col)].suggestAmount = $event.value" @blur="tableProps.validateRecommendedAmount($event, row, col)"/>
                    <button class="w-full text-sm text-left" style="text-decoration: underline;" @click="tableProps.selectReviewer(row, col)"> {{ `初核 : ${tableProps.reviewerName(row.organizations[tableProps.currentIndex(row, col)].preliminaryReviewers)}` }} </button>
                  </template>
                </td>
              </template>
            </tr>
          </tbody>
        </ScrollPanel>
      </table>
    </template>
  </Search>
  <OnHand v-else mode="submit" :subsidiary="subsidiary" :employeeList="employeeList">
    <template #headerButton="headerButtonProps">
      <ConfirmPopup style="width: 244px" class="custom-confirm-popup">
        <template #message="slotProps">
          <div class="p-6 pb-8">
            <p class="pl-2">{{ slotProps.message.message }}</p>
          </div>
        </template>
      </ConfirmPopup>
      <Button  label="取消核定" class="custom-button" :disabled="headerButtonProps.selectedProjects.length === 0" @click="headerButtonProps.confirm($event)"></Button>
    </template>
    <template #table="tableProps">
      <div class="grow flex flex-col">
          <!-- list head -->
          <div ref="scrollHeader" class="overflow-x-scroll overflow-hidden scroll-header" @scroll="tableProps.verticalAlignTable">
            <div class="flex font-sans bg-gray-50 sticky top-0 z-10" style="min-width: 1572px;">
              <label for="selectAll" class="block aspect-square cursor-pointer flex justify-center items-center" style="min-width: 80px;">
                <input v-model="tableProps.selectAll" type="checkbox" class="scale-150 cursor-pointer"  id="selectAll" @change="tableProps.selectAllProjects($event)">
              </label>
              <div v-for="col in wipHeader" class="text-center font-bold flex justify-center items-center" style="min-width: 179px;">{{ col.name }}</div>
            </div>
          </div>
          <!-- list body -->
          <ScrollPanel class="custom-bar h-1 grow">
            <div v-for="row,rowIndex in tableProps.dataList.data" :class="{'bg-gray-50': rowIndex%2 && !row.selected, 'bg-blue-50': row.selected}" class="font-sans border border-gray-200 hover:border-blue-400" :id="`row_${rowIndex}`" style="min-width: 1572px;">
              <div class="flex">
                <!-- col:checkbox -->
                <label :for="`select_${rowIndex}`" class="block aspect-square cursor-pointer flex items-center justify-center" style="min-width: 80px;">
                  <input v-model="row.selected" type="checkbox" class="scale-150 cursor-pointer" :id="`select_${rowIndex}`" @change="tableProps.selectProject($event, row)">
                </label>
                <!-- col:info -->
                <template v-for="col in wipHeader">
                  <div v-if="col.index === 'status'" class="flex items-center justify-center" style="min-width: 179px;">
                    <span class="rounded-full bg-blue-50 border border-blue-400 text-blue-400 w-fit text-sm"  style="padding: 6px 16px;">
                      核定中
                    </span>
                  </div>
                  <div v-else class="flex items-center justify-center" style="min-width: 179px;">{{ col.type==="money" ? tableProps.formatValue(row[col.index]) : row[col.index] }}</div>
                </template>
                <!-- col:expand -->
                <div class="flex justify-center items-center">
                  <div class="cursor-pointer w-10 aspect-square rounded-full hover:bg-gray-200 flex justify-center items-center" @click="tableProps.expandRow(rowIndex)">
                    <font-awesome-icon :icon="['fas', 'chevron-down']" />
                  </div>
                </div>
              </div>
              <div class="w-full expandable-table pl-[80px] pr-[60px]" :class="{'expanding': tableProps.expandRowIndex.includes(rowIndex)}">
                <table class="min-h-0 mt-1">
                  <thead>
                    <tr class="h-20 border-b border-gray-200 bg-gray-50" :class="{'bg-gray-50': !rowIndex%2 && !row.selected, 'bg-white': rowIndex%2 && !row.selected}">
                      <th v-for="col in wipExpandHeader" class="">{{ col.name }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="innerRow,innerRowIndex in row.reviews" class="h-20 border-b border-gray-200" :class="{'bg-gray-50': !(rowIndex%2*1) && innerRowIndex%2 && !row.selected, 'bg-white': rowIndex%2 && innerRowIndex%2 && !row.selected}">
                      <td v-for="col in wipExpandHeader" class="text-center">
                        <span v-if="col.type === 'object'">{{ innerRow[col.index] ? innerRow[col.index].name : '' }}</span>
                        <span v-else-if="col.type === 'money'">{{ tableProps.formatValue(innerRow[col.index]) }}</span>
                        <span  v-else-if="col.index === 'showStatus'" class="rounded-full bg-blue-50 border border-blue-400 text-blue-400 w-fit text-sm" :class="{'bg-green-50 border-green-500 text-green-500 ': !innerRow[col.index]}" style="padding: 6px 16px;">
                          {{ innerRow[col.index] ? (innerRow[col.index]===1?'初核中':'覆核中') : '已完成' }}
                        </span>
                        <span v-else>{{ innerRow[col.index] }}</span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </ScrollPanel>
        </div>
    </template>
  </OnHand>
  <Toast position="top-center" />
</template>
  
<script>
import Banner from "@/common/Banner.vue";
import OnHand from "@/perfBonus/common/OnHand.vue";
import Search from "@/perfBonus/common/Search.vue";
import ScrollPanel from 'primevue/scrollpanel';
export default {
  components: {
    Banner,
    Search,
    OnHand,
    ScrollPanel
  },
  data() {
    return {
      currentTab: 0,
      employeeList: [],
      subsidiary: '',
      apiURL: "/api/acc/perf-bonus",
      tableHeader:  [
        {displayName: '專案代號', section: 'epjno'},
        {displayName: '會計代號', section: 'accId'},
        {displayName: '專案簡稱', section: 'projectName'},
        {displayName: '合約總價', section: 'totalPay'},
        {displayName:"商空部", code:"B", sort:0},
        {displayName:"工二部", code:"CB", sort:10},
        {displayName:"工三部", code:"CC", sort:20},
        {displayName:"工五部", code:"E", sort:30},
        {displayName:"設一部", code:"DA", sort:40},
        {displayName:"設二部", code:"DB", sort:50},
        {displayName:"設三部", code:"DC", sort:60},
        {displayName:"展業部", code:"C", sort:70},
        {displayName:"實設部", code:"TD", sort:80},
        {displayName:"小計"}
      ],
      wipHeader: [
        {
          name: "專案代號",
          index: "epjno",
          type: "string"
        },
        {
          name: "會計代號",
          index: "accId",
          type: "string"
        },
        {
          name: "專案簡稱",
          index: "projectName",
          type: "string"
        },
        {
          name: "合約總價",
          index: "totalPay",
          type: "money"
        },
        {
          name: "績效年月",
          index: "closeYm",
          type: "string"
        },
        {
          name: "績效額度",
          index: "amount",
          type: "money"
        },
        {
          name: "建議額度",
          index: "suggestionAmount",
          type: "money"
        },
        {
          name: "核定進度",
          index: "status",
          type: "string"
        }
      ],
      wipExpandHeader: [
        {
          name: "部門",
          index: "codeName",
          type: "string"
        },
        {
          name: "初核人員",
          index: "firstReviewer",
          type: "object"
        },
        {
          name: "覆核人員",
          index: "secondReviewer",
          type: "object"
        },
        {
          name: "績效額度",
          index: "amount",
          type: "money"
        },
        {
          name: "建議額度",
          index: "suggestionAmount",
          type: "money"
        },
        {
          name: "核定進度",
          index: "showStatus",
          type: "string"
        }
      ],
    };
  },
  mounted() {
    this.subsidiary = localStorage.getItem('company_code')
    this.fetchEmployeeList()
  },
  methods: {
    fetchEmployeeList() {
      axios
      .get(this.apiURL + `/f/employees/${this.subsidiary}`)
      .then(res => {
        this.employeeList = res.data.data
      })
      .catch(err => {
        console.log(err)
      })
    },
  },
};
</script>

<style scoped>
.scroll-header::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
.expandable-table {
  display: grid;
  grid-template-rows: 0fr;
  transition: 0.4s;
  overflow: hidden;
}
.expanding {
  grid-template-rows: 1fr;
  padding-bottom: 24px;
}
</style>