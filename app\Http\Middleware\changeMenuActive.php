<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class changeMenuActive
{
    /**
     * 判斷URL決定side menu狀態
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $url = $request->path();
        if (Session::has('menus')) {
            $menus = Session::get('menus');
            $menus = $menus->map(function (array $menu) use ($url) {
                if (Str::contains($menu['url'], $url)) {
                    $menu['active'] = true;
                } else {
                    $menu['active'] = false;
                    if (isset($menu['drop_lists'])) {
                        $dropMenus = $menu['drop_lists'];
                        $menu['drop'] = false;
                        $tag = 0;
                        $dropMenus = Arr::map($dropMenus, function (array $dropMenu) use ($url, &$tag) {
                            if (Str::contains($dropMenu['url'], $url)) {
                                $dropMenu['active'] = true;
                                $tag = 1;
                                return $dropMenu;
                            } else {
                                $dropMenu['active'] = false;
                                return $dropMenu;
                            }
                        });
                        if ($tag)
                            $menu['drop'] = true;
                        $menu['drop_lists'] = $dropMenus;
                    }
                }
                return $menu;
            });
            Session::put('menus', $menus);
        }

        return $next($request);
    }
}
