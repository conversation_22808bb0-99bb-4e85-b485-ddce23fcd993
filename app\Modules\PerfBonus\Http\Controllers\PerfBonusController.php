<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\PerfBonus\Services\EmployeeService;
use App\Modules\PerfBonus\Services\PerfBonusService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Throwable;

class PerfBonusController extends Controller
{
    protected array $supportedCompany = ['RHT', 'RHY', 'FuncSync'];

    public function __construct(
        protected PerfBonusService $perfBonusService,
        protected EmployeeService $employeeService,
    ) {
    }

    /**
     * 取得所有員工資訊.
     */
    public function employees(string $company)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        $data = $this->employeeService->getEmployees($company);
        return response()->json(compact('data'));
    }

    /**
     * 取得部門代碼對應表。
     */
    public function code(string $company)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        $result = $this->perfBonusService->getCode($company);
        return response()->json($result);
    }

    /**
     * 取得專案列表。
     * 可傳入參數:
     * - epjno: 專案代碼
     * - closeYm: 結算年月
     * - force: 強制刷新
     * - page: 當前第幾頁
     * - perPage: 每頁幾筆.
     */
    public function index(string $company, Request $request)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        $tmpData = $this->perfBonusService->getProjects(
            company: $company,
            filterCondition: $request->all(),
            force: boolval($request->get('force', 0)),
        );

        $epjnoArr = array_map(fn ($it) => $it->epjno, $tmpData['data']);
        $tmpProjectMapping = $this->perfBonusService->getProjectOrgs($epjnoArr);

        // 回補專案的組織資訊
        foreach ($tmpData['data'] as &$itData) {
            $itData->organizations = $tmpProjectMapping[$itData->epjno] ?? [];
        }

        return response()->json([
            'data' => $tmpData['data'],
            'pagination' => $this->formatPagination($tmpData),
        ]);
    }

    /**
     * 送出欲核發績效獎金相關資訊，使 PM、CPM、PD 去做審核該專案的人員績效如何.
     */
    public function submitDetail(Request $request, string $company)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        try {
            $this->perfBonusService->appendPerfBonusToOrgs(
                postData: $request->get('data', []),
                sponsorEmployeeId: (int) Session::get('employee_id')
            );
        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], 400);
        }
        return response()->noContent();
    }

    /**
     * 取得正在跑績效獎金的專案列表.
     */
    public function getProcessing(string $company, Request $request)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }
        $data = $this->perfBonusService->getProcessing($company, $request->all());

        return response()->json([
            'data' => $data['data'],
            'pagination' => $this->formatPagination($data),
        ]);
    }

    /**
     * 取消績效獎金發放.
     */
    public function cancelProcess(string $company, string $epjnoMultiStr)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        $epjnoArr = explode(',', $epjnoMultiStr);
        $data = $this->perfBonusService->cancelPerfBonus($company, $epjnoArr);
        return response()->json(compact('data'));
    }

    /**
     * 取得績效獎金通知相關配置設定.
     */
    public function getNotification(string $company, Request $request)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        $data = $this->perfBonusService->getNotification($company);
        return response()->json(compact('data'));
    }

    /**
     * 設定績效獎金通知相關配置設定.
     */
    public function notification(string $company, Request $request)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        try {
            $request->validate([
                'status' => 'required|boolean',
                'data' => 'required_if:status,1|array',
                'data.text' => 'required_if:status,1|string',
                'data.frequencyDay' => 'required_if:status,1|integer',
            ]);

            $status = $request->get('status');
            $statusRes = $status == 1 ? true : false;

            $text = $request->get('data')['text'] ?? '';
            $frequencyDay = $request->get('data')['frequencyDay'] ?? 1;

            $this->perfBonusService->notification(
                company: $company,
                status: $statusRes,
                text: $text,
                frequencyDay: $frequencyDay,
            );

            return response()->noContent();
        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], 400);
        }
    }

    /**
     * 取得績效獎金相關配置設定.
     */
    public function getSetting(string $company, Request $request)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        $data = $this->perfBonusService->getSetting($company);
        return response()->json(compact('data'));
    }

    /**
     * 更新績效獎金相關配置設定.
     */
    public function putSetting(string $company, string $id, Request $request)
    {
        if (! in_array($company, $this->supportedCompany)) {
            return response('unsupported', 400);
        }

        $data = $this->perfBonusService->putSetting(
            id: $id,
            company: $company,
            nowUserCompanyId: (int) $request->session()->get('CompanyId'),
            employeeIdArr: $request->get('employeeIds', [])
        );
        return response()->json(compact('data'));
    }

    /**
     * 取得以"專案"為單位的結果.
     * @param Request $request
     *                         - epjno 專案id
     *                         - closeYm 結算年月
     */
    public function getResultByProjects(Request $request)
    {
        $data = $this->perfBonusService->getProjectsResult(
            filterCondition: $request->all(),
            isExport: false,
        );

        return response()->json([
            'data' => $data['data'],
            'pagination' => $this->formatPagination($data),
        ]);
    }

    /**
     * 匯出以"專案"為單位的結果.
     * @param Request $request
     *                         - epjno 專案id
     *                         - closeYm 結算年月
     */
    public function exportByProjects(Request $request)
    {
        $writer = $this->perfBonusService->exportByProjects(filterCondition: $request->all());

        $response = new StreamedResponse(
            fn () => $writer->save('php://output')
        );

        $response->headers->set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        $response->headers->set('Content-Type', 'application/vnd.ms-excel');
        $response->headers->set('Content-Disposition', 'attachment;filename="export-by-project(' . date('Y-m-d_H_i_s', strtotime('now')) . ').xlsx"');
        $response->headers->set('Cache-Control', 'max-age=0');
        return $response;
    }

    /**
     * 取得以"專案"為單位的結果.
     * @param Request $request
     *                         - epjno 專案id
     *                         - closeYm 結算年月
     */
    public function getResultByEmployees(Request $request)
    {
        $data = $this->perfBonusService->getEmployeesResult(
            filterCondition: $request->all(),
        );

        return response()->json([
            'data' => $data,
        ]);
    }

    /**
     * 匯出以"員工"為單位的結果.
     */
    public function exportByEmployees(Request $request)
    {
        $writer = $this->perfBonusService->exportByEmployees(
            filterCondition: $request->all()
        );

        $response = new StreamedResponse(
            fn () => $writer->save('php://output')
        );

        $response->headers->set('Content-Type', 'application/vnd.ms-excel');
        $response->headers->set('Content-Disposition', 'attachment;filename="export-by-employee(' . date('Y-m-d_H_i_s', strtotime('now')) . ').xlsx"');
        $response->headers->set('Cache-Control', 'max-age=0');
        return $response;
    }

    /**
     * 取得登入人員所需要審核的獎金額度頁面.
     */
    public function getShouldReviewProject(Request $request)
    {
        $data = $this->perfBonusService->getShouldReviewProjectByEmployeeId(
            employeeId: (int) Session::get('employee_id'),
            filterCondition: $request->all(),
        );

        return response()->json([
            'data' => $data['data'],
            'pagination' => $this->formatPagination($data),
        ]);
    }

    /**
     * 取得登入人員所需要審核的獎金額度頁面 - 內頁: 會顯示該專案的所有人員
     */
    public function getShouldReviewDetail(Request $request, $reviewId)
    {
        $data = $this->perfBonusService->getShouldReviewProjectByEmployeeId(
            employeeId: (int) Session::get('employee_id'),
            with: ['logs'],
            filterCondition: [
                'id' => $reviewId,
            ],
        );
        return response()->json(['data' => $data['data']]);
    }

    public function patchReview(Request $request, $reviewId)
    {
        try {
            $this->perfBonusService->updateApprove(
                reviewId: (int) $reviewId,
                modifyEmployeeId: (int) Session::get('employee_id'),
                data: $request->get('data'),
                confirm: boolval($request->get('confirm')),
            );
        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
            ], 400);
        }
        return response()->noContent();
    }

    public function getAlreadyReview(Request $request)
    {
        $data = $this->perfBonusService->getAlreadyReviewProjectByEmployeeId(
            employeeId: (int) Session::get('employee_id'),
            filterCondition: $request->all(),
        );

        return response()->json([
            'data' => $data['data'],
            'pagination' => $this->formatPagination($data),
        ]);
    }

    public function getAlreadyReviewDetail(Request $request, $reviewId)
    {
        $data = $this->perfBonusService->getAlreadyReviewProjectByEmployeeId(
            employeeId: (int) Session::get('employee_id'),
            with: ['logs'],
            filterCondition: [
                'id' => $reviewId,
            ],
        );

        return response()->json(['data' => $data['data']]);
    }

    /**
     * 格式化分頁器輸出.
     */
    protected function formatPagination(array $data = []): array
    {
        return [
            'current_page' => $data['page'], // 當前第幾頁
            'last_page' => $data['totalPage'], // perpage
            'total' => $data['total'], // 總共筆數
        ];
    }
}
