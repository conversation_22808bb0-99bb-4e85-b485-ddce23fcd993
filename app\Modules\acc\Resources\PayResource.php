<?php

namespace App\Modules\acc\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PayResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return  [
            'id' => $this->id,
            'pkey' => $this->pkey,
            'epjacc' => $this->epjacc,
            'epjno' => $this->epjno,
            'epjna' => $this->payload->get('epjna'),
            'pdno' => $this->pd?->payload->get('name'),
            'pmno' => $this->pm?->payload->get('name'),
            'pcno' => $this->pc?->payload->get('name'),
            'pdid' => $this->pd?->id,
            'pmid' => $this->pm?->id,
            'pcid' => $this->pc?->id,
            'sdate' => $this->payload->get('sdate'),
            'odate' => $this->payload->get('odate'),
            'atypena' => $this->payload->get('atypena'),

            'pleper' => $this->payload->get('pleper') ?? 0,
            'venpayper' => $this->payload->get('venpayper') ?? 0,
            'difamt' => $this->payload->get('difamt') ?? 0,
            'qupamtc' => $this->payload->get('qupamtc') ?? 0,
            'qupamtad' => $this->payload->get('qupamtad') ?? 0,
            'qupamt' => $this->payload->get('qupamt') ?? 0,
            'tqurece' => $this->payload->get('tqurece') ?? 0,
            'tqurecn' => $this->payload->get('tqurecn') ?? 0,
            'budamt' => $this->payload->get('budamt') ?? 0,
            'vencamt' => $this->payload->get('vencamt') ?? 0,
            'venper' => $this->payload->get('venper') ?? 0,
            'tvpay' => $this->payload->get('tvpay') ?? 0,
            'nacpay' => $this->payload->get('nacpay') ?? 0,
            'venpay' => $this->sumvenpay ?? 0,
        ];
    }
}
