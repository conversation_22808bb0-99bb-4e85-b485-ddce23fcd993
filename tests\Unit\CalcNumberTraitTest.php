<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Modules\acc\Services\PerfBonusService;
use Tests\TestCase;

class CalcNumberTraitTest extends TestCase
{

    public function testCalcNumberTrait(): void
    {
        $newObj = new class {
            use \App\Traits\CalcNumberTrait;
        };

        $this->assertTrue($newObj->roundWithPrecision(1234, 2) === 1200);
        $this->assertTrue($newObj->roundWithPrecision(1288, 1) === 1290);
        $this->assertTrue($newObj->roundWithPrecision(334, 2) === 300);
        $this->assertTrue($newObj->roundWithPrecision(55234, 3) === 55000);
        $this->assertTrue($newObj->roundWithPrecision(1444, 1) === 1440);
        $this->assertTrue($newObj->roundWithPrecision(444444, 4) === 440000);
    }
}
