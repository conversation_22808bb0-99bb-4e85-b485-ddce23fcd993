<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

class OrgUnitMember extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = ['id', 'company_id', 'payload', 'metadata'];
    protected $casts = ['payload' => 'collection', 'metadata' => 'collection'];

    public function employee()
    {
        return $this->hasOne(Employee::class, 'id', 'employee_id');
    }
    public function orgUnit()
    {
        return $this->hasOne(OrgUnit::class, 'id', 'org_unit_id');
    }
}
