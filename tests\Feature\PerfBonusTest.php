<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\acc\models\Employee;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Ramsey\Uuid\Uuid;
use Tests\TestCase;

class PerfBonusTest extends TestCase
{
    const TEST_EMPLOYEE_ID = 583;

    public function getToken()
    {
        $this->get('/login?id=' . static::TEST_EMPLOYEE_ID)
            ->assertStatus(302);
    }

    public function testGetPerformanceBonusCode(): void
    {
        $this->getToken();
        $this->get('/api/acc/perf-bonus/f/code/RHT')->assertStatus(200);
        $this->get('/api/acc/perf-bonus/f/code/RHY')->assertStatus(200);
        $this->get('/api/acc/perf-bonus/f/code/RHF')->assertStatus(400);
        $this->get('/api/acc/perf-bonus/f/code/RHQ')->assertStatus(400);
    }

    /**
     * get projects for performance bonus (最上級使用 - first)
     * support query: `force`, `prjId`, `evaluationDuring`, `page`, `perPage`.
     * @return void
     */
    public function testGetProjectsForPerformanceBonus(): void
    {
        // without params
        $this->perfBonusQuery();

        $this->perfBonusQuery(['force' => 1]);
        $this->perfBonusQuery(['force' => 0, 'page' => 1]);
        $this->perfBonusQuery(['force' => 0, 'page' => 2, 'perPage' => 10]);
        $this->perfBonusQuery(['force' => 1, 'closeYm' => '202304']);
        $this->perfBonusQuery(['epjno' => '*********']);
    }

    /**
     * 取得公司的員工列表.
     */
    public function testGetCompanyEmployees(): void
    {
        $this->getToken();

        $queryUrl = '/api/acc/perf-bonus/f/employees/AAA';
        $this
            ->get($queryUrl)
            ->assertStatus(400);

        $queryUrl = '/api/acc/perf-bonus/f/employees/RHT';
        $this
            ->get($queryUrl)
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'companyId',
                        'name',
                        'number',
                        'rank',
                    ],
                ],
            ]);
    }

    /**
     * 財務長發送專案的績效獎金申請.
     */
    public function testManagerSubmitAPerformanceBonus(): void
    {
        $this->getToken();
        $queryUrl = '/api/acc/perf-bonus/f/projects/RHT';

        $this
            ->post($queryUrl,
                [
                    'data' => [
                        [
                            'projectId' => '*********',
                            'organizations' => [
                                [
                                    'code' => 'C1',
                                    'amount' => '1000000',
                                    'suggestAmount' => '1000000',
                                    'preliminaryReviewers' => ['XA12345678', 'XA12345679'],
                                ],
                                [
                                    'code' => 'D2',
                                    'amount' => '1000000',
                                    'suggestAmount' => '1000000',
                                    'preliminaryReviewers' => ['XA12345678'],
                                ], [
                                    'code' => 'RB',
                                    'amount' => '1000000',
                                    'suggestAmount' => '1000000',
                                    'preliminaryReviewers' => ['XA12345678'],
                                ],
                            ],
                        ],
                    ],
                ])
        // ->dump()
        ->assertStatus(400);
    }

    /**
     * 取得審核中的績效獎金.
     */
    public function testManagerReviewProcessingPerformanceBonus(): void
    {
        $this->getToken();

        $queryUrl = '/api/acc/perf-bonus/f/processing/AAA';

        $this
            ->get($queryUrl)
            ->assertStatus(400);

        $queryUrl = '/api/acc/perf-bonus/f/processing/RHT';
        $this
            ->get($queryUrl)
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'epjno',
                        'accId',
                        'projectName',
                        'totalPay', // 合約總價
                        'closeYm', // 績效年月
                        'amount', // 績效額度
                        'suggestionAmount', // 建議額度
                        'status', // 核定進度 inProgress, completed
                    ],
                ],
            ]);
    }

    // testManagerReviewProcessingPerformanceBonus
    public function testManagerReviewCancelPerformanceBonus(): void
    {
        $this->getToken();

        $queryUrl = '/api/acc/perf-bonus/f/processing/AAA/202303083';

        $this
            ->delete($queryUrl)
            ->assertStatus(400);

        $queryUrl = '/api/acc/perf-bonus/f/processing/RHT/202303083';

        $this
            ->delete($queryUrl)
            ->assertStatus(200);
    }

    public function testManagerReviewProcessingPerformanceBonusDetail(): void
    {
        $this->getToken();
        $queryUrl = '/api/acc/perf-bonus/f/processing/RHT/202303083';

        $this
            ->delete($queryUrl)
            ->assertStatus(200);
    }

    private function perfBonusQuery(array $params = []): void
    {
        $this->getToken();
        $queryUrl = '/api/acc/perf-bonus/f/projects/RHT';
        if (! empty($params)) {
            $queryUrl .= '?' . http_build_query($params);
        }
        $response = $this
            
            ->get($queryUrl);

        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'epjno', // 專案代號，原則上為唯一值。
                        'accId', // 會計代號，有可能為 0
                        'projectName', // 專案名稱
                        'pcEmpno', // 專案管制員
                        'pmEmpno', // 專案經理
                        'totalPay', // 總金額
                        'organizations', // 部門代號
                        'status', // 專案狀態: notApplied, inProgress, completed
                        'closeYm',
                    ],
                ],
                'pagination' => [
                    'page',
                    'totalPage',
                    'total',
                ],
            ]);
    }
}
