<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PerfEmailNotifyLog.
 *
 * @property int $id
 * @property string $email
 * @property string $send_date
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 */
class PerfEmailNotifyLog extends Model
{
    use HasFactory;

    protected $table = 'perf_email_notify_logs';

    protected $fillable = [
        'email',
        'send_date',
    ];
}
