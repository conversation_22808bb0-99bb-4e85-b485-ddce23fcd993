<?php

namespace App\Modules\acc\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class PayDetailResourceCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
           'data'=> PayDetailResource::collection($this->collection)->collection->groupBy('pbatchno')
        ];
    }
}
