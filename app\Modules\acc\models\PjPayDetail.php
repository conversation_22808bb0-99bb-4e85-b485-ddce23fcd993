<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


// id	    bint	id		
// pkey	    varchar	會計代號_專案代號		
// symno	varchar	年月		
// pbatchno	varchar	批次		
// epjacc	varchar	會計代號		
// epjno	varchar	專案代號		
// payload	json			
// 	venno	varchar	廠商代號	
// 	venna	varchar	廠商簡稱	
// 	payrmk	varchar	付款摘要	
// 	vencamt	int	    發包總價	
// 	tvenpay	int	    發包已付	
// 	balance	int	    發包結餘	
// 	npay	int	    本期請款	
// 	ppay	int	    本期預付	
// 	hpay	int	    本期保留	
// 	dpay	int	    本期折讓	
// 	venpay	int	    本期實付	
// 	tax	    int	    稅金	default=0
class PjPayDetail extends Model
{
    use HasFactory;

    // protected $fillable = ['id','key', 'epjacc', 'epjno', 'payload'];
    protected $casts = ['payload' => 'collection'];
}
