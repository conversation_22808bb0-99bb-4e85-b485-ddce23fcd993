<?php

namespace App\Modules\PerfBonus\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $epjno 專案代號
 * @property string $epjna 專案簡稱
 * @property string $accasno 會計單號
 * @property string $epjname 專案名稱 -Funcsync 自定義欄位。
 * @property string $sdate 開始時間
 * @property string $odate 完工日
 * @property string $cusno 客戶代號
 * @property string $cusname 客戶公司名稱
 * @property string $cusman 客戶對接人姓名
 * @property string $cmanwtna 客戶職位
 * @property string $cmantel 客戶電話
 * @property string $cmanmail 客戶信箱
 * @property string $wkcla 案子類型 0-9
 * @property string $wkclana 案子類型相應名稱 {"1": "設計+施工", "2": "純設計", "3": "純施工", "4": "實施設計+施工", "5": "實施設計", "6": "專案管理", "7": "傢俱生產", "8": "現場組裝", "9": "傢俱+組裝", "": ""}
 * @property string $schdepno 對應的部門 ex: D2
 * @property string $pjmno 專案經理的員工編號 ex: DB9308678 -> ProJect Manager
 * @property string $pjmna 專案經理的名稱 ex: 林宗岳
 * @property string $atype 專案狀態 0-9,M,C 說明在下方
 * @property string $atypena 專案狀態名稱 {"": "", "0": "開發中", "1": "提案投標", "2": "準備中", "3": "設計中", "4": "施工中", "5":"收尾", "6":"完工", "7":"停案", "8":"刪除", "9":"會結案", "M": "製令結案", "C": "婉謝"}
 * @property string $totpay 總共支付
 * @property string $epjbrno 品牌名稱
 * @property string $closeym 結案的績效年月
 *
 */
class TempWapBepj extends Model
{
    use HasFactory;

    protected $primaryKey = 'epjno';

    public $incrementing = false;

    protected $table = 'temp_wap_bepjs';

    protected $guarded = [];

}
