<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FuncAuth extends Model
{
    use HasFactory, SoftDeletes;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $fillable = [
        'id', 'name', 'payload','metadata'
    ];

    protected $casts = [
        'payload' => 'collection',
        'metadata' => 'collection'
    ];

    public function users()
    {
        return $this->belongsToJson(Employee::class, 'payload->user_list');
    }
}
