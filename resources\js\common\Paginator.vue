<template>
    <div class="max-h-20 w-full px-6 py-4 bg-gray-50">
        <div class="flex flex-wrap md:justify-end w-full gap-6 md:gap-10">
            <p class="my-auto">每頁資訊</p>
            <Dropdown class="w-36" v-model="selectedPer" :options="per" optionLabel="name" optionValue="value"/>

            <p class="my-auto font-sans font-normal">共 {{ data.total }} 筆</p>

            <div class="flex gap-6">
                <button @click="fetchPage(1, selectedPer)" :disabled="data.current_page == 1">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="#D0D5DD" xmlns="http://www.w3.org/2000/svg" :class="[data.current_page <=1 ? '' : 'fill-gray-500']">
                        <path d="M16.9555 6.70538C17.3448 7.09466 17.3452 7.72569 16.9563 8.11538L13.0802 12L16.9563 15.8846C17.3452 16.2743 17.3448 16.9053 16.9555 17.2946C16.566 17.6842 15.9343 17.6842 15.5448 17.2946L10.9573 12.7071C10.5667 12.3166 10.5667 11.6834 10.9573 11.2929L15.5448 6.70538C15.9343 6.31581 16.566 6.31581 16.9555 6.70538Z"/>
                        <rect x="6.25" y="6" width="2" height="12" rx="1"/>
                    </svg>
                </button>
                <button @click="fetchPage(data.current_page - 1, selectedPer)" :disabled="data.current_page == 1">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="#D0D5DD" xmlns="http://www.w3.org/2000/svg" :class="[data.current_page <=1 ? '' : 'fill-gray-500']">
                        <g id="icon/arrow/left" clip-path="url(#clip0_3337_5539)">
                        <path id="Vector" d="M14.7055 6.70538C15.0948 7.09466 15.0952 7.72569 14.7063 8.11538L10.8302 12L14.7063 15.8846C15.0952 16.2743 15.0948 16.9053 14.7055 17.2946C14.316 17.6842 13.6843 17.6842 13.2948 17.2946L8.70726 12.7071C8.31674 12.3166 8.31674 11.6834 8.70726 11.2929L13.2948 6.70538C13.6843 6.31581 14.316 6.31581 14.7055 6.70538Z"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_3337_5539">
                        <rect width="24" height="24" fill="white" transform="translate(24) rotate(90)"/>
                        </clipPath>
                        </defs>
                    </svg>
                </button>
                <p class="mx-5 my-auto font-sans font-normal">{{  String(data.current_page).padStart(2, "0") }}</p>
                <button @click="fetchPage(data.current_page + 1, selectedPer)" :disabled="data.current_page === data.last_page">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="#D0D5DD" xmlns="http://www.w3.org/2000/svg" :class="data.current_page === data.last_page ? '' : 'fill-gray-500'">
                        <g id="icon/arrow/rght" clip-path="url(#clip0_3337_5543)">
                        <path id="Vector" d="M9.29446 17.2946C8.90519 16.9053 8.90484 16.2743 9.29369 15.8846L13.1698 12L9.29369 8.11538C8.90484 7.72569 8.90519 7.09466 9.29446 6.70538C9.68403 6.31581 10.3157 6.31581 10.7052 6.70538L15.2927 11.2929C15.6833 11.6834 15.6833 12.3166 15.2927 12.7071L10.7052 17.2946C10.3157 17.6842 9.68403 17.6842 9.29446 17.2946Z"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_3337_5543">
                        <rect width="24" height="24" fill="white" transform="translate(0 24) rotate(-90)"/>
                        </clipPath>
                        </defs>
                    </svg>
                </button>
                <button @click="fetchPage(data.last_page, selectedPer)" :disabled="data.current_page === data.last_page">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="#D0D5DD" xmlns="http://www.w3.org/2000/svg" style="transform: scaleX(-1);" :class="[data.current_page === data.last_page ? '' : 'fill-gray-500']">
                        <path d="M16.9555 6.70538C17.3448 7.09466 17.3452 7.72569 16.9563 8.11538L13.0802 12L16.9563 15.8846C17.3452 16.2743 17.3448 16.9053 16.9555 17.2946C16.566 17.6842 15.9343 17.6842 15.5448 17.2946L10.9573 12.7071C10.5667 12.3166 10.5667 11.6834 10.9573 11.2929L15.5448 6.70538C15.9343 6.31581 16.566 6.31581 16.9555 6.70538Z"/>
                        <rect x="6.25" y="6" width="2" height="12" rx="1"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

</template>
<script>
import Dropdown from "primevue/dropdown";
export default {
    components:{
        Dropdown
    },
    props:{
        // data 請給 total, current_page, last_page
        data: {
            required: true
        },
        per: {
            default: [
                {name: '200　筆', value: 200},
                {name: '150　筆', value: 150},
                {name: '100　筆', value: 100},
                {name: '50 　 筆', value: 50},
            ]
        },
        sPer: { // stand for selected numbers of item per page
            default: 50
        }
    },
    watch: {
        selectedPer: {
            handler: function() {
                this.fetchPage(1, this.selectedPer)
            }
        }
    },
    data() {
        return {
            selectedPer: this.sPer,
        }
    },
    methods: {
        fetchPage(page, per) {
            this.$emit("page", {
                page : page,
                per: per
            })
        }
    },
}
</script>
