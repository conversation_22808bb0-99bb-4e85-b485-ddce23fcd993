<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Modules\acc\models\Employee;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Ramsey\Uuid\Uuid;
use Tests\TestCase;

class EmployeeNotificationTest extends TestCase
{
    const TEST_EMPLOYEE_ID = 583;

    /** 
     * 透過 session 或是 payload 交換 token
     */
    public function getToken()
    {
        $this->get('/login?id=' . static::TEST_EMPLOYEE_ID)
            ->assertStatus(302);
    }

    public function testGetNotifications()
    {
        $this->getToken();

        $employee = Employee::query()->findOrFail(static::TEST_EMPLOYEE_ID);
        $employee->notify(new \App\Notifications\PerfLogNotification());
        $employee->notify(new \App\Notifications\PerfLogNotification());
        $employee->notify(new \App\Notifications\PerfLogNotification());
        $employee->notify(new \App\Notifications\PerfLogNotification());
        $employee->notify(new \App\Notifications\PerfLogNotification());
        
        $this->get('/api/acc/notify')
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'data',
                    ],
                ],
            ]);
    }

    /**
     * 測試員工讀取通知(單筆)
     * @depends testGetNotifications
     */
    public function testEmployeeMakeAsReadANotification()
    {
        $this->getToken();

        $employee = Employee::query()->findOrFail(static::TEST_EMPLOYEE_ID);
        $notification = $employee->unreadNotifications()->first();

        $this->patch('/api/acc/notify/read/' . $notification->id)
            ->assertStatus(204);
        
        $result = DB::table('notifications')->where('id', $notification->id)->first();
        $this->assertNotNull($result->read_at);
    }

    /**
     * 測試員工讀取通知(全部)
     * @depends testEmployeeMakeAsReadANotification
     */
    public function testEmployeeMakeAsReadAllNotifications()
    {
        $this->getToken();

        $this->patch('/api/acc/notify/read/all')
            ->assertStatus(204);

        $result = DB::table('notifications')
            ->whereNull('read_at')
            ->where('notifiable_id', static::TEST_EMPLOYEE_ID)
            ->get();

        $this->assertTrue($result->isEmpty());
    }

    /**
     * 測試員工刪除通知(單筆)
     * @depends testEmployeeMakeAsReadAllNotifications
     */
    public function testEmployeeDeleteANotification()
    {
        $this->getToken();
        
        $employee = Employee::query()->findOrFail(static::TEST_EMPLOYEE_ID);
        $notification = $employee->notifications()->first();

        $this->delete('/api/acc/notify/' . $notification->id)
            ->assertStatus(204);
        
        $this->assertDatabaseMissing('notifications', ['id' => $notification->id]);
    }

    /**
     * 測試員工刪除通知(全部)
     * @depends testEmployeeDeleteANotification
     */
    public function testEmployeeDeleteAllNotifications()
    {
        $this->getToken();
        
        $this->delete('/api/acc/notify/all')
            ->assertStatus(204);

        $this->assertDatabaseMissing('notifications', ['notifiable_id' => static::TEST_EMPLOYEE_ID]);
    }
}
