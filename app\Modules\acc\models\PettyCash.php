<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
class PettyCash extends Model
{
    use HasFactory;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $table = 'petty_cash';
    protected $fillable = ['id', 'no','employee_id','layout_id','form_index','columns','sign_roles','updated_by','created_at', 'updated_at'];
    protected $casts = ['columns' => 'collection', 'sign_roles' => 'collection'];

    public function employee()
    {
        return $this->hasOne(Employee::class, 'id', 'employee_id');
    }
    public function org()
    {
        return $this->hasOneThrough(OrgUnit::class, OrgUnitMember::class, 'employee_id', 'id', 'employee_id', 'org_unit_id');
    }
    public function company()
    {
        return $this->hasOneThrough(Company::class, Employee::class, 'id', 'id', 'employee_id', 'company_id');
    }
}
