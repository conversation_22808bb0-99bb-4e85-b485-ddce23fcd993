<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;

class PerfBonusPart2Test extends TestCase
{
    public function testGroupLeaderGetApprovedList()
    {
        // allow search project id
        $this->get('/')->assertStatus(404);
    }

    public function testGroupLeaderGetApprovedDetail()
    {
        $this->get('/')->assertStatus(404);
    }

    public function testGroupLeaderSaveTempApprovedData()
    {
        $this->get('/')->assertStatus(404);
    }

    public function testGroupLeaderMakeSureApprovedData()
    {
        $this->get('/')->assertStatus(404);
    }

}
