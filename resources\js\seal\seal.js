import '../bootstrap';
import "primevue/resources/themes/lara-light-blue/theme.css";
import "primevue/resources/primevue.min.css";
import 'primeicons/primeicons.css';

import {createApp} from 'vue'
import content from '../seal/Seal.vue'
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import "@/common/fontawsome";
import PrimeVue from 'primevue/config';
import Button from 'primevue/button';
// import Dropdown from 'primevue/dropdown';
// import Column from 'primevue/column';
// import InputText from 'primevue/inputtext';
// import Checkbox from 'primevue/checkbox';
// import DataTable from 'primevue/datatable';
// import ColumnGroup from 'primevue/columngroup';
// import Row from 'primevue/row';
import ToastService from 'primevue/toastservice';
// import Toast from 'primevue/toast';
// import InputNumber from 'primevue/inputnumber';
// import Timeline from 'primevue/timeline';
// import ConfirmPopup from 'primevue/confirmpopup';
import ConfirmationService from 'primevue/confirmationservice';
// import Card from 'primevue/card';

const container = createApp(content);

container.use(PrimeVue);
container.use(ToastService);
container.use(ConfirmationService);
container.component('Button', Button);
// container.component('Checkbox', Checkbox);
// container.component('Card', Card);
// container.component('Column', Column);
// container.component('ColumnGroup', ColumnGroup);
// container.component('Row', Row);
// container.component('Dropdown', Dropdown);
// container.component('Toast',Toast);
// container.component('InputNumber', InputNumber);
// container.component('InputText', InputText);
// container.component('Timeline', Timeline);
// container.component('ConfirmPopup', ConfirmPopup);
container.component("font-awesome-icon", FontAwesomeIcon);
container.mount("#content");

