<template>
  <div>
    <div class="p-6 flex flex-wrap w-full bg-white rounded-xl shadow">
      <div class="flex flex-col">
        <label class="mb-4 text-xl"> 查詢結果</label>
        <MultiSelect
          v-model="selectColumns"
          :options="columns"
          optionLabel="name"
          placeholder="選擇欄位"
          class="md:w-20rem"
        />
      </div>
      <div class="w-full"><hr class="my-8" /></div>
      <div class="w-full flex flex-col">
        <label class="mb-4 text-xl"> 查詢條件</label>
        <div class="flex flex-row">
          <label class="my-auto mr-2"> 申請日期區間:</label>
          <Calendar
            v-model="dateRange"
            selectionMode="range"
            placeholder="請選擇年月"
          />
          <label class="my-auto mx-2"> 需求單號:</label>
          <InputText type="text" v-model="no" placeholder="請輸入需求單號" />
          <label class="my-auto mx-2">申請人:</label>
          <Dropdown
            filter
            type="text"
            v-model="employee"
            optionLabel="name"
            optionValue="id"
            :options="employees"
            placeholder="請選擇"
          />
        </div>
      </div>
      <div>
        <div
          v-for="(selector, index) of selectors"
          class="pt-6 flex flex-row"
          :key="index"
        >
          <div>
            <Dropdown
              v-model="selectors[index]"
              :options="
                columns.filter(
                  (column) =>
                    !['c99', 'c98', 'c97', 'c96', 'c95'].includes(column.id)
                )
              "
              optionLabel="name"
              placeholder="選擇欄位"
            />
          </div>
          <span v-if="selector.type == 'money'||selector.type == 'total'" class="ml-3 mt-3">大於</span>
          <div class="ml-2">
            <Dropdown
              v-if="selector.type == 'dropdown' || selector.type == 'cascade'"
              v-model="selector.value"
              optionValue="name"
              :options="selector.options"
              optionLabel="name"
              placeholder="選擇欄位"
            />
            <Calendar
              v-else-if="selector.type == 'date'"
              v-model="selector.value"
              selectionMode="range"
              placeholder="請選擇日期"
            />
            <InputText
              type="text"
              v-else
              v-model="selector.value"
              placeholder="請輸入"
            />
          </div>
          <font-awesome-icon
            class="pl-2 pt-4"
            :icon="['fas', 'trash']"
            @click="deleteSelector(selector.id)"
          />
        </div>
      </div>
      <div class="w-full flex justify-end mt-2">
        <div>
          <Button
            type="button"
            label="新增條件"
            severity="secondary" text raised
            @click="addSelector()"
          />
        </div>
        <div class="ml-6">
          <Button
            type="button"
            label="查詢"
            icon="pi pi-search"
            :loading="loading"
            @click="fetch()"
          />
        </div>
      </div>
    </div>

    <div
      class="p-6 my-6 bg-white rounded-xl shadow whitespace-nowrap overflow-x-auto flex"
      :class="result.length == 0 ? 'justify-center items-center' : 'flex-col'"
      :style="result.length == 0 ? 'height : calc(100vh - 276px)' : ''"
    >
      <div class="text-center" v-if="result.length == 0">
        <ProgressSpinner v-if="loading" />
        <img
          v-else-if="result.length == 0"
          src="../../images/page_state/search.png"
          alt=""
        />
        <img v-else src="../../images/page_state/search_empty.png" alt="" />
        <p v-if="!loading && result.length == 0">輸入您要查詢的資料</p>
        <p v-else-if="!loading && result?.length == 0">
          無任何查詢結果，請再次查詢
        </p>
      </div>
      <div class="w-full" v-else>
        <DataTable
          v-model:selection="selectedValue"
          :value="result.data"
          selectionMode="multiple"
          :metaKeySelection="false"
          :dataKey="index"
          scrollable
          scrollHeight="600px"
          stripedRows
        >
          <template #header>
            <div
              class="flex flex-wrap align-items-center justify-between gap-2"
            >
              <p class="text-xl text-900 font-bold">零用金申請</p>
              <div>
                <Button
                  :disabled="selectedValue.length == 0"
                  @click="exportLists($event)"
                  :loading="loading"
                  label="匯出"
                />
                &ensp;
              </div>
            </div>
          </template>
          <Column
            frozen
            selectionMode="multiple"
            headerStyle="width: 3rem"
            footer="小計"
          >
          </Column>
          <Column
            v-for="col of selectColumns"
            :header="col.name"
            :field="col.id"
            :key="col.id"
            :footer="this.$parent.computedDetailTotal(selectedValue, col.id)"
            :footerClass="checkColumnClass(col.id)"
          >
            <template
              v-if="['c9', 'c11', 'c12', 'c13', 'c14'].includes(col.id)"
              #body="slotProps"
            >
              <span class="float-right">{{
                this.$parent.FormatValue(slotProps.data[col.id])
              }}</span>
            </template>
            <template v-else-if="col.type == 'date'" #body="slotProps">
              <span class="float-right">{{
                new Date(slotProps.data[col.id])
                  .toLocaleString("zh-TW")
                  .split(" ")[0]
              }}</span>
            </template>
          </Column>
        </DataTable>
        <Paginator
          :data="result"
          :per="[
            {name: '100　筆', value: 100},
            {name: '60 　筆', value: 60},
            {name: '30 　筆', value: 30},
            {name: '10 　筆', value: 10},
          ]"
          :sPer="10"
          @page="(val) => fetch(val.page, val.per)"
        />
      </div>
    </div>
  </div>
    <Sidebar
    v-model:visible="visibleBottom"
    position="bottom"
    :baseZIndex="1000"
    :dismissable="false"
    :modal="false"
    :showCloseIcon="false"
    class="p-sidebar-xs"
  >
    <div class="flex justify-end w-full">
      <p class="text-sm my-auto">已選取{{ selectedValue.length }}個</p>
      <p
        @click="
          selectedValue = [];
          visibleBottom = false;
        "
        class="mx-12 text-sm text-blue-600 underline my-auto cursor-pointer"
      >
        取消選取
      </p>
    </div>
  </Sidebar>
</template>
<script>
import Paginator from "../common/Paginator.vue";

export default {
  components: {
    Paginator,
  },
  data: function () {
    return {
      loading: false,
      dateRange: [],
      visibleBottom:false,
      page: 1,
      per: 10,
      columns: [],
      employees: [],
      selectColumns: [],
      selectedValue: [],
      whereSelector: [],
      selectors: [],
      no: null,
      employee: null,
      result: [],
      apiURL: "/api/acc/report/cash",
    };
  },
  mounted() {
    this.fetchColumns();
    this.fetchEmployees();
  },
  watch:{
    selectedValue(newArray,oldArray){
        if (newArray.length > 0)
            this.visibleBottom = true;
        else
            this.visibleBottom = false;
    }
  },
  methods: {
    fetchEmployees() {
      axios
        .get("/api/acc/common/employees")
        .then((response) => {
          let data = response.data ?? [];
          this.employees = data;
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    checkColumnClass(keyName) {
      if (["c9", "c12", "c14"].includes(keyName)) {
        return "float-right";
      } else {
        return "";
      }
    },
    fetchColumns() {
      this.loading = true;
      axios
        .get("/api/acc/report/cash/columns")
        .then((response) => {
          let data = response.data ?? [];
          this.columns = data;
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fetch(page, per) {
      this.loading = true;
      if (page) this.page = page;
      if (per) this.per = per;
      axios
        .get(this.apiURL, {
          params: {
            start: this.dateRange[0],
            end: this.dateRange[1],
            no: this.no,
            employee: this.employee,
            selectors: this.selectors.filter(x=>x.value!=''),
            page: this.page,
            per: this.per,
          },
        })
        .then((response) => {
          this.result = response.data ?? [];
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    addSelector() {
      let opt = JSON.parse(JSON.stringify(this.columns[0]));
      delete opt.name;
      opt.value = "";
      this.selectors.push(opt);
    },
    deleteSelector(id) {
      this.selectors = this.selectors.filter((selector) => selector.id != id);
    },
    exportLists() {
      axios({
        url: this.apiURL + "/excel",
        method: "POST",
        // headers: {
        //   "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        // },
        data: {
          values: this.selectedValue,
          columns: this.selectColumns,
        },
        responseType: "blob",
      })
        .then((response) => {
          if (response.headers["content-type"].includes("application/zip")) {
            this.download(response);
          } else if (
            response.headers["content-type"].includes(
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
          ) {
            var blob = new Blob([response.data], {
              type: "application/vnd.ms-excel;charset=utf-8",
            });

            var filename = decodeURIComponent(
              response.headers["content-disposition"].split(
                "filename*=utf-8''"
              )[1]
            );
            var url = window.URL.createObjectURL(blob);
            var aLink = document.createElement("a");
            aLink.style.display = "none";
            aLink.href = url;
            aLink.setAttribute("download", filename);
            document.body.appendChild(aLink);
            aLink.click();
            setTimeout(function () {
              window.URL.revokeObjectURL(url);
            }, 100);
          } else {
            window.print();
          }
        })
        .catch((error) => {
          console.log(error);
        });
    },
    download(response) {
      var blob = new Blob([response.data], {
        type: "application/zip",
      });
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        window.navigator.msSaveOrOpenBlob(blob);
        return;
      }
      var filename = decodeURIComponent(
        response.headers["content-disposition"].split("filename=")[1]
      );
      const data = window.URL.createObjectURL(blob);
      var link = document.createElement("a");
      link.href = data;
      link.download = filename; // response.name + '.pdf'
      link.click();
      // link.remove();
      setTimeout(function () {
        window.URL.revokeObjectURL(data);
      }, 100);
    },
  },
};
</script>
