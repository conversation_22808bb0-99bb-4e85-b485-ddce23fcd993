<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sign_logs', function (Blueprint $table) {
            $table->id();
            $table->string('pkey')->storedAs("epjacc ||'_'|| epjno")->index();
            $table->string('startYm');
            $table->string('endYm');
            $table->string('pbatchno');
            $table->string('pbatchna')->nullable();
            $table->string('epjacc');
            $table->string('epjno');
            $table->jsonb('payload')->nullable();
            $table->integer('created_by');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sign_logs');
    }
};
