<template>
  <div>
    <div class="bg-white p-6 px-4 text-black">
      <span class="text-xl"> 功能權限 </span>
    </div>
    <div class="bg-gray-100">
      <div class="w-full px-4 py-2">
        <div class="flex justify-between w-full py-2">
          <div class="w-1/5">
            <span class="text-lg"> 權限功能 </span>
          </div>
          <div class="w-2/3">
            <span class="text-lg"> 可使用之人員 </span>
          </div>
          <div class="w-1/3"></div>
        </div>
      </div>
    </div>
    <hr />
    <div class="bg-white">
      <div v-for="(dataList, index) in dataLists" :key="dataList.id">
        <div
          class="relative w-full p-5 border border-white hover:border-blue-600"
        >
          <div class="flex justify-between w-full">
            <div class="w-1/5">
              <span class="text-black">
                {{ dataList.name }}
              </span>
            </div>
            <div class="w-2/3">
              <div class="flex text-black">
                <p>{{ showName(dataList.users) }}</p>
                <p v-if="dataList.userList.length > 5">．．．</p>
              </div>
            </div>
            <div class="w-1/3 flex justify-end">
              <span class="text-black cursor-pointer">
                <font-awesome-icon
                  icon="fa-solid fa-pen"
                  @click="openModel(index)"
                />
              </span>
            </div>
          </div>
        </div>
        <hr />
      </div>
    </div>
    <Dialog
      header="可編輯之人員"
      v-model:visible="displayModal"
      :dismissableMask="true"
      :style="{
        width: '40vw',
      }"
      :contentStyle="{
        height: '20vw',
      }"
      :modal="true"
      :draggable="false"
    >
      <div class="my-auto font-bold text-gray-400">
        <!-- :options="getRoles"  v-model="reviewToNodeId" @input="invalids.orgs = false"-->

        <div class="pb-8">
          <span>可查看之人員</span>
          <br />

          <MultiSelect
            class="w-full"
            placeholder="選擇人員"
            :options="getEmployees"
            optionLabel="name"
            optionValue="id"
            data-key="id"
            :showClear="true"
            :filter="true"
            v-model="users"
          >
            <template #option="slotProps">
              <div>
                <span>{{ slotProps.option.org_name }}</span>
                <span>{{ slotProps.option.name }}</span>
              </div>
            </template>
          </MultiSelect>
        </div>
      </div>
      <template #footer>
        <Button
          @click="displayModal = false"
          label="取消"
          class="p-button-outlined p-button-secondary w-28 mr-5"
        />
        <Button @click="save()" label="儲存" class="w-28" />
      </template>
    </Dialog>
  </div>
  <Toast position="top-center" />
</template>


<script>
export default {
  components: {},
  mounted() {
    this.fetchAuthList();
    this.getUser();
    // this.fetchOrgUnits();
  },
  props: {
    //
  },
  data() {
    return {
      displayModal: false,
      onAdd: false,
      dataPemissionListUndo: [],
      dataLists: [],
      getEmployees: [],
      users: [],
      apiURL: "/api/acc/auth/",
    };
  },
  computed: {
    //
  },
  watch: {},
  methods: {
    fetchAuthList() {
      axios
        .get(this.apiURL + "func-auth")
        .then((response) => {
          this.dataLists = response.data;
        })
        .catch((error) => {
          console.error(error);
        });
    },
    getUser() {
      axios
        .get("/api/acc/common/employees")
        .then((response) => {
          this.getEmployees = null;
          this.getEmployees = response.data;
        })
        .catch((error) => {
          console.error(error);
        });
    },
    openModel(index) {
      this.displayModal = true;
      this.index = index;
      this.users = this.dataLists[index].userList;
    },

    save() {
      axios
        .post(this.apiURL + "func-auth/update", {
          id: this.dataLists[this.index].id,
          users: this.users,
        })
        .then((response) => {
          if (response.data) {
            this.$toast.add({
              severity: "success",
              summary: "儲存成功",
              detail: this.messages,
              life: 3000,
            });
            this.displayModal = false;
            this.fetchAuthList();
          } else {
            this.$toast.add({
              severity: "error",
              summary: "儲存失敗",
              detail: this.messages,
              life: 3000,
            });
          }
        })
        .catch((error) => {
          console.error(error);
        });
    },
    showName(users) {
      return users.join("、");
    },
  },
};
</script>
<style lang="css" >
.multiss .p-multiselect-panel .p-multiselect-items .p-multiselect-item {
  justify-content: space-between;
}

.vs__dropdown-toggle {
  border-color: rgba(96, 165, 250);
  border-width: 2px;
}
</style>
