{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "aspose-cloud/aspose-words-cloud": "^23.5", "dompdf/dompdf": "^2.0", "gotenberg/gotenberg-php": "^2.12", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.8", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "league/flysystem-sftp-v3": "^3.0", "phpoffice/phpspreadsheet": "^1.29", "phpoffice/phpword": "^1.1", "rap2hpoutre/fast-excel": "^5.3", "staudenmeir/eloquent-json-relations": "^1.1", "ysgao/php-zip": "*******"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["module_helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}, "files": ["dev-helper.php"]}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "dealerdirect/phpcodesniffer-composer-installer": true}}, "minimum-stability": "stable", "prefer-stable": true}