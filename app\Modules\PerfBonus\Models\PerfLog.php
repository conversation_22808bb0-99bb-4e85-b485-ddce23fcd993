<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * PerfLog.
 *
 * @property int $id - 記錄的唯一識別碼
 * @property int $review_id - 評論的唯一識別碼
 * @property string $sno - 專案角色代號
 * @property string $empno - 員工編號
 * @property string $empna - 員工姓名
 * @property bool $is_new - 是否為新的紀錄
 * @property string $append_by - 附加者
 * @property string $first_reviewer_payload - 第一位審核者的資料
 * @property string $finally_reviewer_payload - 最後一位審核者的資料
 * @property string $member_type - 成員類型
 * @property string $question_extra - 額外問題
 * @property string $show_title - 顯示標題
 * @property \Illuminate\Support\Carbon|null $created_at - 記錄建立時間
 * @property \Illuminate\Support\Carbon|null $updated_at - 記錄更新時間
 * @property \Illuminate\Support\Carbon|null $deleted_at - 記錄刪除時間
 */
class PerfLog extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'perf_logs';

    protected $fillable = [
        'review_id',
        'sno',
        'empno',
        'empna',
        'is_new',
        'append_by',
        'first_reviewer_payload',
        'finally_reviewer_payload',
        'member_type',
        'question_extra',
        'show_title',
    ];
}
