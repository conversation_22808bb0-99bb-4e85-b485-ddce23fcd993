<?php

namespace App\Modules\acc\Controllers;

use App\Modules\acc\Enums\SendStatusenum;
use App\Modules\acc\Services\ReportService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Modules\acc\models\Code;
use App\Modules\acc\models\PjReport;
use App\Traits\FormatDate;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use \PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ReportController extends Controller
{
    use FormatDate;
    protected $user;
    private $reportService;
    public function __construct()
    {
        $this->user = Session::get('employee_id');
        $this->timezone = Session::get('timezone');
        $this->reportService = new ReportService;
        // $this->user = 583;
    }

    /**
     * 專案付款
     * @param \Illuminate\Http\Request $request
     */
    public function getPayReport(Request $request)
    {
        if (!$request->has(['startYm', 'endYm', 'pbatch'])) {
            return response('缺少必要資訊', 400);
        }

        $result = $this->payList($request, false);

        $result->getCollection()->transform(function ($item) {

            $odate = Arr::first($item->columns, function ($value) {
                return $value['acc_name'] == 'odate';
            });
            $sdate = Arr::first($item->columns, function ($value) {
                return $value['acc_name'] == 'sdate';
            });

            $sign_roles = $item->payload->get('sign_roles');
            $e = Arr::first($sign_roles, function ($value) {
                return $value['self_name'] == '工程主管';
            });
            $acc = Arr::first($sign_roles, function ($value) {
                return $value['self_name'] == '財務主管';
            });
            $pm = Arr::first($sign_roles, function ($value) {
                return $value['self_name'] == 'CPM';
            });
            $pd = Arr::first($sign_roles, function ($value) {
                return $value['self_name'] == 'PD';
            });

            $data = $item->list->merge($item->payload)
                ->merge([
                    'odate' => $odate['value'],
                    'sdate' => $sdate['value'],

                    // 需求單各關卡簽核日期
                    'edate' => $e ? $this->formatDate($e['raw_time'], 'date') : '',
                    'accdate' => $acc ? $this->formatDate($acc['raw_time'], 'date') : '',
                    'pmdate' => $pm ? $this->formatDate($pm['raw_time'], 'date') : '',
                    'pddate' => $pd ? $this->formatDate($pd['raw_time'], 'date') : '',
                ]);
            unset($data['status']);
            return $data;
        });

        return $result;
    }

    public function payList($request, $isExport)
    {
        $startYm = $this->formatDate($request->input('startYm'), 'ym');
        $endYm = $this->formatDate($request->input('endYm'), 'ym');
        $pbatchno = $request->input('pbatch');
        $epjacc = $request->input('epjacc');
        $fetchDiff = filter_var(
            $request->input('fetchDiff'),
            FILTER_VALIDATE_BOOLEAN
        );
        ;

        $page = (int) $request->input('page') ?? 1;
        $per = $request->input('per') ?? 50;

        $result = PjReport::where(function ($query) use ($startYm, $endYm) {
            $query->where('payload->startYm', '<=', $endYm)
                ->where('payload->endYm', '>=', $startYm);
        })
            ->where('payload->status', 2)
            ->whereIn('payload->pbatchno', $pbatchno)
            ->when($epjacc, function ($query) use ($epjacc) {
                return $query->where('payload->epjacc', 'like', "%$epjacc%");
            })
            ->when($fetchDiff, function ($query) {
                return $query->whereraw('list->>\'venpay\' <> list->>\'pdamount\'');
            })
            ->when($isExport, function ($q) {
                return $q->get();
            }, function ($q) use ($per, $page) {
                return $q->paginate($per);
            });

        return $result;
    }

    public function PayExport(Request $request)
    {
        // *付款只會每月結算 ，不會跨月
        if (!$request->has(['payList', 'companyTitle']) || count($request->input('payList')) < 1) {
            return response('缺少必要資訊', 400);
        }

        // 暫時資料夾
        $tmpDir = now()->format('Ymdhis') . '_' . $this->user;
        $this->reportService->setTmpDir($tmpDir);
        Storage::disk('export')->makeDirectory($tmpDir);

        $payList = $request->input('payList');
        $companyTitle = $request->input('companyTitle');

        // 根據不同專案、月份、批次、廠商分pdf
        $result = collect($payList)->groupBy(function ($item, int $key) {
            return $item['epjacc'] . '-' . $item['epjno'] . '-' . $item['startYm'] . '-' . $item['pbatchno'];
        });


        // 小記=總計
        $baseName = 'project_pay.docx';

        /** 因樹娟要直接當作正式的來用 所以先註解掉測試的 */
        if (config('app')['debug']) {
            $baseName = 'project_pay_test.docx';
        }

        if (!$this->reportService->checkFile($baseName))
            return response('缺少必要資訊', 400);
        $filePath = $this->reportService->getFilePath($baseName);


        $result->each(function ($item) use ($companyTitle, $baseName, $filePath) {
            // dd($item[0]);
            // 將流程關卡人員與日期塞入
            $sign_roles = collect($item[0]['sign_roles'])
                ->map(function ($item) {
                    return [
                        $item['self_name'] . '名稱' => $item['role_id'],
                        $item['self_name'] . '日期' => $item['raw_time'],
                    ];
                })->flatMap(function (array $values) {
                    return $values;
                });

            $lists = $item->map(function ($list) {
                return [
                    'venno' => $list['venno'],
                    'venna' => $list['venna'],
                    'payrmk' => $list['payrmk'],
                    'vencamt' => number_format($list['vencamt']),
                    'tvenpay' => number_format($list['tvenpay']),
                    'balance' => number_format($list['balance']),
                    'npay' => number_format($list['npay']),
                    'ppay' => number_format($list['ppay']),
                    'hpay' => number_format($list['hpay']),
                    'dpay' => number_format($list['dpay']),
                    'venpay' => number_format($list['venpay']),
                ];
            });


            $col = [
                'title' => config('reporttitle')[$companyTitle]['title'],
                'epjacc' => $item[0]['epjacc'],
                'epjno' => $item[0]['epjno'],
                'epjna' => $item[0]['epjna'],
                'startYm' => $item[0]['startYm'],
                'pbatchno' => $item[0]['pbatchno'],
                '製表人名稱' => $this->user,
                'today' => $this->formatDate(today(), 'date'),

                '工程主管名稱' => $sign_roles['工程主管名稱'] ?? '',
                '工程主管日期' => $this->formatDate($sign_roles['工程主管日期'] ?? '', 'date'),
                '財務主管名稱' => $sign_roles['財務主管名稱'] ?? '',
                '財務主管日期' => $this->formatDate($sign_roles['財務主管日期'] ?? '', 'date'),
                'CPM名稱' => $sign_roles['CPM名稱'] ?? '',
                'CPM日期' => $this->formatDate($sign_roles['CPM日期'] ?? '', 'date'),
                'PD名稱' => $sign_roles['PD名稱'] ?? '',
                'PD日期' => $this->formatDate($sign_roles['PD日期'] ?? '', 'date'),

                'total_vencamt' => number_format($item->sum('vencamt')),
                'total_balance' => number_format($item->sum('balance')),
                'total_npay' => number_format($item->sum('npay')),
                'total_venpay' => number_format($item->sum('venpay')),
            ];
            // $this->export('pay', $baseName, $filePath, $lists, $col);
            $this->reportService->export(type: 'pay', sendType: '', baseName: $baseName, filePath: $filePath, data: $lists, col: $col);
        });

        return $this->reportService->convertPDFAndResponseZip();
    }

    /**
     * 廠商付款憑單
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function getVenReport(Request $request)
    {
        if (!$request->has(['startYm', 'endYm', 'pbatch'])) {
            return response('缺少必要資訊', 400);
        }

        $startYm = $this->formatDate($request->input('startYm'), 'ym');
        $endYm = $this->formatDate($request->input('endYm'), 'ym');
        $pbatchno = $request->input('pbatch');
        $venno = $request->input('venno');
        // submitType = 0,1,2;
        $status = $request->input('submitType');
        $page = (int) $request->input('page') ?? 1;
        $per = $request->input('per') ?? 50;


        // DB::enableQueryLog();
        // 批次要複選
        // 畫面呈現不同筆資料
        // pdf 整合成一份

        $select = "
                list->>'venno' as venno
                ,jsonb_agg( list || payload - 'sign_roles' - 'status') as list
                ,emails.email
                ,emails.faxno
                ";
        $logFun = function ($join) use ($startYm, $endYm, $pbatchno, $venno, $status) {
            $join->where(function ($query) use ($startYm, $endYm) {
                $query->where('startYm', '<=', $endYm)
                    ->where('endYm', '>=', $startYm);
            })
                ->whereIn('pbatchno', $pbatchno)
                ->when($venno, function ($query) use ($venno) {
                    return $query->where('venno', $venno);
                })
                ->when($status == 2, function ($query) {
                    return $query->where('sendStatus', '<>', '');
                })
                ->select('venno', 'sendStatus', 'payload->hash_id as hash_id', 'created_at');
        };

        $result = PjReport::with(
            ['vensendlog' => $logFun]
        )
            ->when($status == 1, function ($query) {
                return $query->wheredoesntHave('sendlog');
            })
            ->when($status == 2, function ($query) use ($logFun) {
                return $query->whereHas('sendlog', $logFun);
            })
            ->where('payload->status', 2)
            ->where(function ($query) use ($startYm, $endYm) {
                $query->where('payload->startYm', '<=', $endYm)
                    ->where('payload->endYm', '>=', $startYm);
            })
            ->whereIn('payload->pbatchno', $pbatchno)
            ->when($venno, function ($query) use ($venno) {
                return $query->where('list->venno', 'like', "%$venno%");
            })
            ->select(DB::raw($select))
            ->leftjoin('emails', 'emails.venno', '=', 'pj_reports.list->venno')
            ->groupByRaw('list->>\'venno\',emails.email,emails.faxno')
            ->orderBy('list->venno')
            // ->get();
            ->paginate($per);

        $result->getCollection()
            ->transform(function ($item) use ($status) {

                $lists = $item['list']
                    ->sortBy([
                        // sql groupby 沒有payload 不能orderby 所以放到php做
                        ['epjacc', 'asc'],
                        ['unkey', 'asc'],
                        ['vencamt', 'desc'],
                    ])
                    ->values();


                $sendLog = $item['vensendlog'];

                $total = $lists
                    ->transform(function ($list) use ($sendLog) {
                        // 稅金值為”本期實付”乘以 5%
                        $list['tax'] = round($list['pdamount'] * 0.05, 2);

                        $log = $sendLog->filter(function ($value, $key) use ($list) {
                            return $value->hash_id == $list['hash_id'];
                        })
                            ->sortByDesc('created_at')
                            ->first();

                        $list['submitType'] = SendStatusenum::tryfrom($log?->sendStatus)?->getLabel() ?? '未發送';
                        $list['submitTime'] = $this->formatDate($log?->created_at, 'date');

                        return $list;
                    })
                    ->reduce(function (?int $carry, $list) {
                        return $carry + ($list['pdamount'] + $list['tax']);
                    });

                return [
                    'venno' => $lists[0]['venno'],
                    'total' => $total,
                    'list' => $lists,
                    'email' => $item['email'] ?? '',
                    'fax' => $item['faxno'] ?? '',
                ];
            });

        return $result;
    }
    // 匯出
    public function venExport(Request $request)
    {
        // *付款只會每月結算 ，不會跨月
        if (!$request->has('payList') || count($request->input('payList')) < 1 || !$request->filled('companyTitle')) {
            return response('缺少必要資訊', 400);
        }
        // 暫時資料夾
        $tmpDir = now()->format('Ymdhis') . '_' . $this->user;
        $this->reportService->setTmpDir($tmpDir);
        Storage::disk('export')->makeDirectory($tmpDir);

        $payList = $request->input('payList');
        $companyTitle = $request->input('companyTitle');
        $companyInfo = config('reporttitle')[$companyTitle];
        // 根據不同專案、月份、批次、廠商分pdf
        $result = collect($payList)->groupBy(function ($item, int $key) {
            return $item['venno'] . '-' . $item['startYm'] . '-' . $item['pbatchno'];
        });

        // 付款 :會計代號-付款年月-批次
        // 小記=總計
        $baseName = 'ven_pay.docx';
        // $baseName ='ven_pay_has_home.docx';

        /** 因樹娟要直接當作正式的來用 所以先註解掉測試的 */
        if (config('app')['debug']) {
            $baseName = 'ven_pay_test.docx';
        }

        if (!$this->reportService->checkFile($baseName))
            return response('缺少必要資訊', 400);
        $filePath = $this->reportService->getFilePath($baseName);

        $result->each(function ($item) use ($companyInfo, $baseName, $filePath) {

            $lists = $item
                ->sortBy([
                    ['epjacc', 'asc'],
                    ['unkey', 'asc'],
                    ['vencamt', 'desc'],
                ])
                ->map(function ($list) {
                    return [
                        'epjacc' => $list['epjacc'],
                        'epjna' => $list['epjna'],
                        'payrmk' => $list['payrmk'],
                        'vencamt' => number_format($list['vencamt']),
                        'tvenpay' => number_format($list['tvenpay']),
                        'npay' => number_format($list['npay'] + $list['ppay']),
                        'hpay' => number_format($list['hpay']),
                        'dpay' => number_format($list['dpay']),
                        'venpay' => number_format($list['venpay']),
                        'tax' => number_format($list['tax']),
                    ];
                });

            $col = [
                'title' => $companyInfo['title'],
                'venno' => $item[0]['venno'],
                'copname' => $item[0]['copname'],
                'startYm' => $item[0]['startYm'],
                'pbatchno' => $item[0]['pbatchno'],
                '製表人名稱' => $this->user,
                'today' => $this->formatDate(today(), 'date'),

                // 'total' => number_format($item->sum('vencamt')),
                'total_vencamt' => number_format($item->sum('vencamt')),
                'total_tvenpay' => number_format($item->sum('tvenpay')),
                'total_npay' => number_format($item->sum('npay') + $item->sum('ppay')),
                'total_hpay' => number_format($item->sum('hpay')),
                'total_dpay' => number_format($item->sum('dpay')),
                'total_venpay' => number_format($item->sum('venpay')),
                'total_tax' => number_format($item->sum('tax')),
                'total' => number_format($item->sum('venpay') + $item->sum('tax')),
            ];
            // $this->export('ven', $baseName, $filePath, $lists, $col);
            $this->reportService->export(type: 'ven', sendType: '', baseName: $baseName, filePath: $filePath, data: $lists, col: $col);

        });

        return $this->reportService->convertPDFAndResponseZip();
    }
    // 傳真 ，寄信
    public function venSend(Request $request)
    {
        try {
            // 發現廠商沒有傳真或是mail 就跳過
            // *付款只會每月結算 ，不會跨月
            if (
                !$request->has(['payList', 'sendInfo', 'companyTitle', 'sendType'])
                || count($request->input('payList')) < 1
                || count($request->input('sendInfo')) < 1
            ) {
                return response('缺少必要資訊', 400);
            }

            // 暫時資料夾
            $tmpDir = now()->format('Ymdhis') . '_' . $this->user;
            $this->reportService->setTmpDir($tmpDir);
            Storage::disk('export')->makeDirectory($tmpDir);

            $payList = $request->input('payList');
            $sendInfo = $request->input('sendInfo');
            $companyTitle = $request->input('companyTitle');
            $companyInfo = config('reporttitle')[$companyTitle];
            $sendType = $request->input('sendType');
            // 根據不同專案、月份、批次、廠商分pdf
            $result = collect($payList)->groupBy(function ($item, int $key) {
                return $item['venno'] . '-' . $item['startYm'] . '-' . $item['pbatchno'];
            });

            // 付款 :會計代號-付款年月-批次
            // 小記=總計
            $baseName = $sendType == 'fax' ?
                'ven_pay_has_home.docx' :
                'ven_pay.docx'
            ;
            // $baseName ='ven_pay_has_home.docx';

            /** 因樹娟要直接當作正式的來用 所以先註解掉測試的 */
            if (config('app')['debug']) {
                $baseName = $sendType == 'fax' ?
                    'ven_pay_has_home_test.docx' :
                    'ven_pay_test.docx';
            }

            if (!$this->reportService->checkFile($baseName))
                return response('缺少必要資訊', 400);
            $filePath = $this->reportService->getFilePath($baseName);
            //
            $logs = [];
            $subjectTime = 'acc' . date("YmdHis", time());
            $result->each(function ($item) use ($companyInfo, $baseName, $filePath, &$logs, $sendType, $sendInfo, $subjectTime) {

                $lists = $item
                    ->filter(function ($list) use ($sendInfo, $sendType) {
                        // 找出
                        $venno = Arr::first($sendInfo, function ($item) use ($list) {
                            return $item['venno'] == $list['venno'];
                        });
                        if (empty($venno))
                            return false;

                        // 剔除沒有傳真或email的資料
                        return !$venno[$sendType] == '' && !empty($venno[$sendType]);
                    })
                    ->sortBy([
                        ['epjacc', 'asc'],
                        ['unkey', 'asc'],
                        ['vencamt', 'desc'],
                    ])
                    ->map(function ($list) use (&$logs, $sendInfo, $sendType, $subjectTime) {

                        $venno = $list['venno'];
                        $faxNo = Arr::first($sendInfo, function ($value) use ($venno) {
                            return $value['venno'] == $venno;
                        })['fax'];

                        array_push($logs, [
                            // 'pkey' => $list['epjacc'].'_'.$list['epjno'],
                            'startYm' => $list['startYm'],
                            'endYm' => $list['endYm'],
                            'pbatchno' => $list['pbatchno'],
                            'pbatchna' => null,
                            'epjacc' => $list['epjacc'],
                            'epjno' => $list['epjno'],
                            'venno' => $venno,
                            'sendStatus' => $sendType == 'fax' ? SendStatusenum::SEND_FAX->value : SendStatusenum::SEND_EMAIL->value,
                            'payload' => json_encode([
                                'hash_id' => $list['hash_id'],
                                'subject' => $subjectTime . $venno,
                                'faxNo' => $sendType == 'fax' ? $this->reportService->reNumber($faxNo) : '',
                            ]),
                            'created_by' => $this->user,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);

                        return [
                            'epjacc' => $list['epjacc'],
                            'epjna' => $list['epjna'],
                            'payrmk' => $list['payrmk'],
                            'vencamt' => number_format($list['vencamt']),
                            'tvenpay' => number_format($list['tvenpay']),
                            'npay' => number_format($list['npay'] + $list['ppay']),
                            'hpay' => number_format($list['hpay']),
                            'dpay' => number_format($list['dpay']),
                            'venpay' => number_format($list['venpay']),
                            'tax' => number_format($list['tax']),
                        ];
                    });

                if ($lists->isEmpty())
                    return;

                $col = [
                    'title' => $companyInfo['title'],
                    'taxnumber' => $companyInfo['taxnumber'],
                    'venno' => $item[0]['venno'],
                    'copname' => $item[0]['copname'],
                    'startYm' => $item[0]['startYm'],
                    'pbatchno' => $item[0]['pbatchno'],
                    '製表人名稱' => $this->user,
                    'today' => $this->formatDate(today(), 'date'),
                    // 'total' => number_format($item->sum('vencamt') + $item->sum('tax')),
                    'total_vencamt' => number_format($item->sum('vencamt')),
                    'total_tvenpay' => number_format($item->sum('tvenpay')),
                    'total_npay' => number_format($item->sum('npay') + $item->sum('ppay')),
                    'total_hpay' => number_format($item->sum('hpay')),
                    'total_dpay' => number_format($item->sum('dpay')),
                    'total_venpay' => number_format($item->sum('venpay')),
                    'total_tax' => number_format($item->sum('tax')),
                    'total' => number_format($item->sum('venpay') + $item->sum('tax')),
                ];
                $this->reportService->export(type: 'ven', sendType: $sendType, baseName: $baseName, filePath: $filePath, data: $lists, col: $col);
            });

            // 暫存資料夾位置
            $dir = $this->reportService->setDirAndConvertPDF();

            if ($sendType == 'email')
                $this->reportService->venEmail($dir, $sendInfo, $companyTitle, $logs);
            else
                $this->reportService->venFax($dir, $sendInfo, $subjectTime, $logs);

            return 1;
        } catch (\Throwable $th) {
            \Log::error($th);
            return response()->json('error', 400);
        }
    }

/**
     * 廠商用印通知
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator|\Illuminate\Contracts\Routing\ResponseFactory|\Illuminate\Http\Response
     */
    public function getVenSealReport(Request $request)
    {
        if (!$request->has(['startYm', 'endYm', 'pbatch'])) {
            return response('缺少必要資訊', 400);
        }

        $startYm = $this->formatDate($request->input('startYm'), 'ym');
        $endYm = $this->formatDate($request->input('endYm'), 'ym');
        $pbatchno = $request->input('pbatch');
        $venno = $request->input('venno');
        // submitType = 0,1,2;
        $status = $request->input('submitType');
        $page = (int) $request->input('page') ?? 1;
        $per = $request->input('per') ?? 50;


        // DB::enableQueryLog();
        // 批次要複選
        // 畫面呈現不同筆資料
        // pdf 整合成一份

        $select = "
                list->>'venno' as venno
                ,jsonb_agg( list || payload - 'sign_roles' - 'status') as list
                ,emails.email
                ,emails.faxno
                ";
        $logFun = function ($join) use ($startYm, $endYm, $pbatchno, $venno, $status) {
            $join->where(function ($query) use ($startYm, $endYm) {
                $query->where('startYm', '<=', $endYm)
                    ->where('endYm', '>=', $startYm);
            })
                ->whereIn('pbatchno', $pbatchno)
                ->when($venno, function ($query) use ($venno) {
                    return $query->where('venno', $venno);
                })
                ->when($status == 2, function ($query) {
                    return $query->where('sendStatus', '<>', '');
                })
                ->select('venno', 'sendStatus', 'payload->hash_id as hash_id', 'created_at');
        };

        $result = PjReport::with(
            ['vensendlog' => $logFun]
        )
            ->when($status == 1, function ($query) {
                return $query->wheredoesntHave('sendlog');
            })
            ->when($status == 2, function ($query) use ($logFun) {
                return $query->whereHas('sendlog', $logFun);
            })
            ->where('payload->status', 2)
            ->where(function ($query) use ($startYm, $endYm) {
                $query->where('payload->startYm', '<=', $endYm)
                    ->where('payload->endYm', '>=', $startYm);
            })
            ->whereIn('payload->pbatchno', $pbatchno)
            ->when($venno, function ($query) use ($venno) {
                return $query->where('list->venno', 'like', "%$venno%");
            })
            ->select(DB::raw($select))
            ->leftjoin('emails', 'emails.venno', '=', 'pj_reports.list->venno')
            ->groupByRaw('list->>\'venno\',emails.email,emails.faxno')
            ->orderBy('list->venno')
            // ->get();
            ->paginate($per);

        $result->getCollection()
            ->transform(function ($item) use ($status) {

                $lists = $item['list']
                    ->sortBy([
                        // sql groupby 沒有payload 不能orderby 所以放到php做
                        ['epjacc', 'asc'],
                        ['unkey', 'asc'],
                        ['vencamt', 'desc'],
                    ])
                    ->values();


                $sendLog = $item['vensendlog'];

                $total = $lists
                    ->transform(function ($list) use ($sendLog) {
                        // 稅金值為”本期實付”乘以 5%
                        $list['tax'] = round($list['pdamount'] * 0.05, 2);

                        $log = $sendLog->filter(function ($value, $key) use ($list) {
                            return $value->hash_id == $list['hash_id'];
                        })
                            ->sortByDesc('created_at')
                            ->first();

                        $list['submitType'] = SendStatusenum::tryfrom($log?->sendStatus)?->getLabel() ?? '未發送';
                        $list['submitTime'] = $this->formatDate($log?->created_at, 'date');

                        return $list;
                    })
                    ->reduce(function (?int $carry, $list) {
                        return $carry + ($list['pdamount'] + $list['tax']);
                    });

                return [
                    'venno' => $lists[0]['venno'],
                    'total' => $total,
                    'list' => $lists,
                    'email' => $item['email'] ?? '',
                    'fax' => $item['faxno'] ?? '',
                ];
            });

        return $result;
    }


    //匯出成excel x軸放column y軸放row
    public static function outputExcel(Request $request)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $docName = $request->docName;
        $columnp_names = $request->names;
        $namesCount = count($columnp_names);
        $mata = $request->mata;

        $columnp_pioritys = [];
        for ($x = ord('A'); $x <= ord('Z'); $x++) {
            array_push($columnp_pioritys, chr($x));
        }
        $columnp_pioritys = array_splice($columnp_pioritys, 0, $namesCount);

        for ($i = 0; $i < $namesCount; $i++) {
            $sheet->getCell($columnp_pioritys[$i] . '1')->getStyle()->getFont()->setBold(true);
            $sheet->setCellValue($columnp_pioritys[$i] . '1', $columnp_names[$i]);
        }
        ;

        //先Y軸 從A2開始
        $baseRow = 2;
        foreach ($mata as $key => $items) {
            foreach ($items as $index => $itemCol) {
                $sheetKey = $key + $baseRow;
                $sheet->setCellValue($columnp_pioritys[$index] . $sheetKey, $itemCol);
            }
        }
        $getCodeTable = Code::where('code_kind', 'AE')->where('code_id', 'export')->first();
        $exportName = $getCodeTable ? $getCodeTable->nm_zh_tw : '';
        //html回傳excel需寫標頭,訂定格式為UTF-8
        $encoded_fname = rawurlencode(date('Y-m-d') . $docName . time() . $exportName . '.xlsx');
        header('Content-Type:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $encoded_fname . '"; filename*=utf-8\'\'' . $encoded_fname);
        header('Pragma:no-cache');
        header('Expires:0');

        $writer = new Xlsx($spreadsheet);
        //  $writer = IOFactory::createWriter($spreadsheet, "Xls");
        $writer->save('php://output');
        exit;
    }
}
