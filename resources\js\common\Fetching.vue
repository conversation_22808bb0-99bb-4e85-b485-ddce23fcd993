<template>
  <div class="absolute z-10 pt-30 flex flex-col gap-5 justify-center items-center h-full w-full">
    <img class="rotating" src="../../images/page_state/loading_circle.svg" alt="search"/>
    <span class="font-bold">資料載入中...</span>
  </div>
</template>
  
<script>
export default {
  components: {
  },
  data() {
    return {
    };
  },
  methods: {
  },
};
</script>

<style scoped>
.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>