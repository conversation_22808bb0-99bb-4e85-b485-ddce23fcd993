<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SignLog extends Model
{
    use HasFactory;
    use SoftDeletes;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;

    protected $fillable = ['startYm', 'endYm', 'pbatchno', 'epjacc', 'epjno', 'payload', 'created_by'];
    protected $casts = ['payload' => 'collection'];
}
