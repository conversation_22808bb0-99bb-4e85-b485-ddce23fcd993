<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PjpayReport extends Model
{
    use HasFactory;
    use \Staudenmeir\EloquentJsonRelations\HasJsonRelationships;
    protected $casts = [
        'columns' => 'collection',
        'list' => 'collection',
        'payload' => 'collection'
    ];

    public function sendlog()
    {
        return $this->hasMany(SendLog::class, 'payload->hash_id', 'payload->hash_id');
    }

    // 廠商列表使用，因為需要groupby 會跟pkey或hash_id 衝突
    // 所以換成venno
    public function vensendlog()
    {
        return $this->hasMany(SendLog::class, 'venno', 'venno');
    }

    public function email()
    {
        return $this->hasone(Email::class, 'venno',  'list->venno');
    }
}
