<template>
  <div class="flex justify-center items-center h-full">
    <!-- 沒搜過 -->
    <img class="h-3/5" style="max-height: 500px;" v-if="!searched && data === undefined" src="../../images/page_state/search.svg" alt="search" /> 
    <!-- 自動搜尋過，但沒有 -->
    <img  class="h-3/5" style="max-height: 500px;" v-else-if="!searched && data.length === 0" src="../../images/page_state/empty.svg" alt="search" />
    <!-- 使用者搜尋過，但沒有 -->
    <img  class="h-3/5" style="max-height: 500px;" v-else-if="searched && data.length === 0" src="../../images/page_state/search_empty.svg" alt="search" />
    <!-- 搜失敗 -->
    <p v-else-if="searched && data === undefined" class="text-center">獲取失敗</p>
  </div>
</template>

<script>
export default {
  props: ["data", "searched"],
  data() {
    return {
    }
  },
  methods: {
  },
};
</script>
