<?php

namespace App\Modules\acc\models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Code extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $fillable = [
        'id',
        'code_kind',
        'code_parent',
        'code_id',
        'sort_order',
        'nm_zh_tw',
        'nm_zh_cn',
        'nm_en_us'
    ];
}
