<template>
  <div class="flex flex-col">
    <Banner :names="['專案撈取', '審核進度']" />
    <div class="m-6">
      <fetchProject v-if="currentTab == 0" />
      <reviewHistory v-if="currentTab == 1" />
    </div>
  </div>
</template>
<script>
import Banner from "../common/Banner.vue";
import fetchProject from "../projectPayment/fetchProject.vue";
import reviewHistory from "../projectPayment/reviewHistory.vue";

export default {
  components: {
    Banner,
    fetchProject,
    reviewHistory,
  },
  data: function () {
    return {
      currentTab: 0,
      newDate:new Date(),
    };
  },
  methods: {
    //畫面共用邏輯
    changeState(state) {
      switch (state) {
        case 1:
          return "bg-blue-50 text-blue-400 border-blue-400";
        case 3:
          return "bg-red-50 text-red-400 border-red-400";
      }
    },
    statusReturnName(state) {
      switch (state) {
        case 0:
          return "未審核";
        case 1:
          return "審核中";
        case 2:
          return "同意";
        case 3:
          return "駁回";
        case 4:
          return "再審";
        case 5:
          return "會簽";
        case 6:
          return "退回";
        case 7:
          return "退回至申請人";
      }
    },
    stateColor(state) {
      if (state == 2) {
        return "text-green-400";
      } else {
        return "text-gray-400";
      }
    },
    stateTimeLineMarker(state) {
      if (state !== 0) {
        return "bg-blue-400 border-blue-400";
      } else {
        return "border-gray-200 ";
      }
    },
    stateTimeLineConnector(state) {
      if (state !== 0 && state !== 1) {
        return "bg-blue-400";
      } else {
        return "bg-gray-200";
      }
    },
    FormatValue(value) {
      const formatter = new Intl.NumberFormat("en-US", {
        currency: "USD",
      });
      if (value == null) {
        return (value = "-");
      } else {
        return (value = formatter.format(value));
      }
    },
    computedDetailTotal(detail, keyName) {
      if (detail.length == 0) return 0;
      let keyIndex = Object.keys(detail[0]).findIndex((key) => key == keyName);
      let detailValues = detail.map((item) => Object.values(item)[keyIndex]);
      return this.FormatValue(
        detailValues.reduce((total, value) => {
          return total + value;
        })
      );
    },

    toastSucess(msg,life=3000) {
      this.$toast.add({
        severity: "success",
        summary: msg,
        //   detail: this.messages,
        life: life,
      });
    },
    toastError(msg) {
      this.$toast.add({
        severity: "error",
        summary: msg,
        // detail: this.messages,
        life: 3000,
      });
    },
  },
};
</script>

<style>
/* .p-datatable .p-column-header-content {
        display: inline-block !important;
    } */
div.p-datatable .p-column-header-content {
  display: inline-block;
}
</style>
