<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PerfEmployeePermission 模型代表了員工的權限。
 *
 * @property int $id 權限 ID
 * @property int $employee_id 員工 ID
 * @property int $permission_id 權限 ID
 * @property \Illuminate\Support\Carbon $created_at 建立時間
 * @property \Illuminate\Support\Carbon $updated_at 更新時間
 */
class PerfEmployeePermission extends Model
{
    use HasFactory;

    protected $table = 'perf_employee_permission';

    protected $fillable = [
        'employee_id',
        'permission_id',
    ];
}
