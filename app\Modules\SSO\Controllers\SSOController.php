<?php

namespace App\Modules\SSO\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Modules\SSO\Models\SsoToken;
use Illuminate\Support\Facades\Session;
use Illuminate\Encryption\Encrypter;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class SSOController extends Controller
{
    protected $company_id;
    protected $user_id;

    // public function __construct()
    // {
    //     $this->user_id = Session::get('employee_id');
    // }

    private function setToken()
    {

        if (!Session::has('employee_id'))
            return redirect('\\');
        // $employee_id=Session::get('employee_id');

        $employee_id = Session::get('employee_id');

        $token = hash('sha1', now());
        //刪掉過期的
        SsoToken::where('created_at', '<', Carbon::now()->addMinute(-10))->delete();
        //新增
        SsoToken::create([
            'token' => $token,
            'employee_id' => $employee_id
        ]);
        return  $token;
    }

    public function getToken()
    {
        $token = $this->setToken();

        $newEncrypter = new Encrypter(md5(Config('sso_key')), 'AES-256-CBC');
        $urlToken = $newEncrypter->encrypt($token);

        return $urlToken;
    }
    //
    protected function redirectAsap()
    {
        $urlToken = $this->setToken();
        $url = config('app.sso_asap_url') . '/login?token=' . $urlToken;
        return redirect()->away($url);
    }
}
