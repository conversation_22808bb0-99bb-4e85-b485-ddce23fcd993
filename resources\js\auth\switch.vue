<template>
  <div class="flex flex-col">
    <Banner :names="['功能權限']" />
    <div class="m-6">
      <auth v-if="currentTab == 0" />
    </div>
  </div>
</template>
<script>
import Banner from "../common/Banner.vue";
import auth from "../auth/auth.vue";

export default {
  components: {
    Banner,
    auth,
  },
  data: function () {
    return {
      currentTab: 0,
    };
  },
  methods: {

  },
};
</script>
