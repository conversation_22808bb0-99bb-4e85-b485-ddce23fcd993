<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use App\Modules\acc\models\Company;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PerfErpProject 專案.
 *
 * @property int $id id
 * @property string $project_name 專案名稱
 * @property int $company_id 公司id
 * @property string $epjno 專案代號
 * @property string $accasno 會計科目
 * @property string $pc_empno PC負責人(員工編號)
 * @property string $pc_code PC部門代號
 * @property string $pm_empno PM負責人(員工編號)
 * @property string $pm_code PM部門代號
 * @property string $s_date 工程開始日
 * @property string $e_date 工程結束日
 * @property string $closeym 結算年月
 * @property string $schdepno 部門代號
 * @property string $status 送審狀態，總共有三種: notApplied, inProgress, completed
 * @property float $total_pay 合約總價
 */
class PerfErpProject extends Model
{
    use HasFactory;

    public const STATUS_NOT_APPLIED = 'notApplied';

    public const STATUS_IN_PROGRESS = 'inProgress';

    public const STATUS_COMPLETED = 'completed';

    protected $table = 'perf_erp_projects';

    protected $fillable = [
        'project_name',
        'company_id',
        'epjno',
        'accasno',
        'pc_empno',
        'pc_code',
        'pm_empno',
        'pm_code',
        's_date',
        'e_date',
        'closeym',
        'schdepno',
        'status',
        'total_pay',
        'sponsor_employee_id',
    ];

    public function employees()
    {
        return $this->hasMany(PerfErpProjectEmployee::class, 'epjno', 'epjno');
    }

    public function reviews()
    {
        return $this->hasMany(PerfDepReview::class, 'prj_id', 'id');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }
}
