<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Http\Middleware;

use App\Modules\acc\models\Employee;
use App\Modules\PerfBonus\Models\PerfPermission as ModelsPerfPermission;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class PerfPermission
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $slugs = ModelsPerfPermission::get(['slug']);

        // 群組控管
        $groupSlugs = $slugs
            ->filter(fn ($it) => str_starts_with($it->slug, 'group::'))
            ->map(fn ($it) => str_replace('group::', '', $it->slug))
            ->values();

        // 個別控管
        $singleSlugs = $slugs
            ->filter(fn ($it) => ! str_starts_with($it->slug, 'group::'))
            ->pluck('slug')
            ->toArray();

        $routeName = $request->route()->getName();

        $em = Employee::with(['perfPermissions'])->findOrFail(Session::get('employee_id'));
        /** @var \Illuminate\Database\Eloquent\Collection $hasPermissions */
        $hasPermissions = $em->perfPermissions;

        if (in_array($routeName, $singleSlugs)) {
            $singlePermission = $hasPermissions->first(fn ($it) => $it->slug === $routeName);

            if (empty($singlePermission)) {
                return response()->json(['message' => 'permission denied'], 403);
            }
        }

        /** @var \Illuminate\Support\Collection $groupSlugs */
        $r = $groupSlugs->first(function ($it) use ($routeName) {
            return str_starts_with($routeName, $it);
        });

        if ($r) {
            $c = $hasPermissions->first(fn ($it) => $it->slug === 'group::' . $r);
            if (empty($c)) {
                return response()->json(['message' => 'permission denied'], 403);
            }
        }

        return $next($request);
    }
}
