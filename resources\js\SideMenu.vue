<template>
  <div class="relative overflow-hidden whitespace-nowrap">
    <label
      for="sideMenu-active"
      class=" cursor-pointer md:hidden"
    > 
      <font-awesome-icon
        @click="showMenu"
        class="fixed top-6 left-6"
        style="color: white;"
        :icon="['fa', 'bars']"
        size="xl"
      />
    </label>
    <h1 class="block text-white text-xl md:text-2xl text-center py-6 md:py-12 h-20 md:h-auto">財務系統</h1>
    <nav class="whitespace-nowrap">
      <ul>
        <li v-for="(menu, index) in menus" :key="index">
          <a
            class="block px-12 py-6 text-white hover:opacity-100 apply-list"
            :href="menu.url"
            :class="menu.drop||menu.active ? 'opacity-100' : 'opacity-50'"
            @click="'drop' in menu ? (menu.drop = !menu.drop) : ''"
            ><font-awesome-icon class="mr-4 w-5" :icon="['fa', menu.icon]" />
            {{ menu.name }}
            <font-awesome-icon
              v-if="'drop' in menu"
              class="float-right mt-1 transition duration-300"
              :class="{'rotate-180': menu.drop}"
              :icon="['fa', 'angle-up']"
          /></a>
          <div class="grid overflow-hidden transition-all duration-300" :class="{'gird-row-0': !menu.drop, 'gird-row-1': menu.drop}">
            <div style="min-height: 0;">
              <a
                class="block pl-[88px] pr-12 py-6 text-white hover:opacity-100 apply-list"
                :class="list.active?'opacity-100 bg-white/20':'opacity-50'"
                v-for="(list, listIndex) in menu.drop_lists"
                :key="listIndex"
                :href="list.url"
                >{{ list.name }}
              </a>
            </div>
          </div>
        </li>
      </ul>
    </nav>
  </div>
  <label
    for="sideMenu-active"
    style="right: 0; top: calc((100% - 30px) / 2)"
    class="absolute cursor-pointer hidden md:block"
  >
    <font-awesome-icon
      @click="showMenu"
      class="fixed scale-125"
      style="color: #1677FF;"
      :icon="['fa', 'bars']"
      size="xl"
    />
  </label>
</template>

<script>
export default {
    inject: ['menu'],
  data: function () {
    return {
      display_menu: 1,
      menus:this.menu
    };
  },
  methods: {
    showMenu() {
      if(this.display_menu == 1) {
        this.display_menu = 0
      } else {
        this.display_menu = 1
      }
    }
  }
};
</script>

<style scoped>
.gird-row-0 {
  grid-template-rows: 0fr;
}
.gird-row-1 {
  grid-template-rows: 1fr;
}
</style>
