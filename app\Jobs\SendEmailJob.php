<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;
use App\Modules\acc\models\SendLog;

class SendEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // protected $info;
    // protected $dir;
    // protected $companyTitle;
    protected $isDebug;

    /**
     * Summary of __construct
     * @param string $dir 檔案位置
     * @param mixed $info
     * @param mixed $companyTitle
     * @param mixed $logs
     */
    public function __construct(
        public string $dir,
        public $info,
        public $companyTitle,
        public $logs
    ) {
        // $this->info = $info;
        // $this->dir = $dir;
        // $this->companyTitle = $companyTitle;
        $this->isDebug = config('app')['debug'];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $info = $this->info;
        $files = collect(File::files($this->dir));


        $data['title'] = config('reporttitle')[$this->companyTitle]['title'];
        $data['taxnumber'] = config('reporttitle')[$this->companyTitle]['taxnumber'];
        foreach ($info as $key => $value) {
            if(empty($value['email'])) continue;
            Mail::send('email.venPay', $data, function ($message) use ($value, $files) {
                // $message->from($data['email']);
                $email = explode(";",$value['email']);
                // if($this->isDebug){
                //     $email = '';
                // }
                $message->cc($email)->subject("匯僑-發票開立通知　　　　　※此為系統自動送出信件，請勿直接回覆此信件。");
                // 將同一個廠商的資料 同時送出
                foreach ($files as $file) {
                      // 檔案名稱
                    $venName = explode("_", $file->getBasename())[0];
                    if (
                        $venName== $value['venno'] &&
                        $file->getExtension() == 'pdf'
                    ) {
                        $message->attach(
                            $file->getRealPath()
                        );
                    }
                }
            });
            SendLog::insert($this->logs);

        }

    }
}
