<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use App\Modules\acc\models\SendLog;
use Illuminate\Support\Facades\Storage;

class SendFaxJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // protected $info;
    // protected $dir;
    // protected $subjectTime;
    protected $isDebug;

    
    /**
     * Summary of __construct
     * @param string $dir 檔案位置
     * @param mixed $info 
     * @param mixed $subjectTime subject
     * @param mixed $logs
     */
    public function __construct(
        public string $dir,
        public $info,
        public $subjectTime,
        public $logs
    ) {
        // $this->info = $info;
        // $this->dir = $dir;
        // $this->subjectTime = $subjectTime;
        // $this->isDebug = config('app')['debug'];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $info = $this->info;
        $files = collect(File::files($this->dir));
        $sftp = Storage::disk('fax');


        // hinet 會移出檔案 所以無法抓取目錄有哪些檔案
        foreach ($info as $key => $value) {
            foreach ($files as $key => $file) {
                // 檔案名稱
                $venName = explode("_", $file->getBasename())[0];
                if (
                    $venName== $value['venno'] &&
                    $file->getExtension() == 'pdf'
                ) {
                    // file name

                    // $time = substr(time(), 2).chr(rand(97,122));
                    $time = substr(time(), -6, 5);
                    $name = 'HN' . config('filesystems')['hn_number'] . '.' . $time;

                    // pdf content
                    $pdftext = file_get_contents($file->getPathname());
                    // pdf 頁數
                    $num = preg_match_all("/\/Page\W/", $pdftext);

                    // pdf
                    $pdf = $sftp->put('/doc/' . $name . '.pdf', $pdftext);

                    // til
                    $tilString = $this->makeTil($value, $file, $num);
                    $til = $sftp->put('/doc/' . $name . '.til', $tilString);
                }
            }
        }

        SendLog::insert($this->logs);
    }


    public function makeTil($value, $file, $num, $name = '')
    {
        // sendLog 也會記錄subjectTime
        // 回報查詢 需要SUBJECT
        $til = [
            "SERVICETYPE" => 39,
            "REPLY" => config('fax')['fax_reply_mail'],
            "FORMAT" => "PDF",
            "PAGES" => $num,
            "PAGESIZE" => 1,
            "ORIENTATION" => "Plain",
            "XRES" => 204,
            "YRES" => 196,
            "WIDTH" => 1728,
            "LENGTH" => 2290,
            "NAME" => $name,
            "FAX" => "",
            "COMPANY" => "",
            "CODE" => "BIG5",
            "SUBJECT" => $this->subjectTime . $value['venno'],
            "REPORT" => 3,
            "BROCAST" => 0,
            "NO" => $value['fax'],
            // "NO" => $this->reNumber($value['fax']),
            "SEQNO" => 0,
            "RNAME" => 0,
            "RCOMPANY" => 0
        ];
        $textData = "";
        foreach ($til as $key => $value) {
            $textData .= "$key: $value\n";
        }
        return $textData;
    }
}
