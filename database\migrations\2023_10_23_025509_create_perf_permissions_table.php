<?php

use App\Modules\PerfBonus\Models\PerfPermission;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('perf_permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('權限名稱');
            $table->string('slug')->comment('權限代碼');
            $table->string('description')->nullable()->comment('權限描述');
            $table->timestamps();
        });
        // ->comment('績效獎金權限表，當中 slug 可以放路由名稱或是 group::加上路由前綴，例如 group::settings 這樣的話會對應到 settings 開頭的路由');

        PerfPermission::query()->insert([
            ['name' => '專案撈取', 'slug' => 'group::first', 'description' => '專案撈取', 'created_at' => '2023-10-23 00:00:00'],
            ['name' => '結果查詢', 'slug' => 'group::history', 'description' => '結果查詢', 'created_at' => '2023-10-23 00:00:00'],
            ['name' => '相關設定', 'slug' => 'group::settings', 'description' => '相關設定', 'created_at' => '2023-10-23 00:00:00'],
        ]);

        Schema::create('perf_employee_permission', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('employee_id')->comment('員工ID')->index();
            $table->unsignedBigInteger('permission_id')->comment('權限ID')->index();
            $table->timestamps();
        });
        // ->comment('績效獎金權限的 pivot 表，用於紀錄員工與權限的關係');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('perf_permissions');
        Schema::dropIfExists('perf_employee_permission');
    }
};
