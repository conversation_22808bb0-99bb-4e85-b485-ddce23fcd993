<?php

use App\Modules\acc\Controllers\AuthController as ModuleAccAuthController;
use App\Modules\SSO\Controllers\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/



Route::get('/', function () {
    if (config('app.debug') == true) {
        Session::flush();
        return view('_dev.logout');
    }
    else{
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE');
        header('Access-Control-Allow-Headers: Content-Type, X-Auth-Token, Origin, Authorization');
        header('Location: https://sso.funcsync.com/richhonour/login');
    }
});

Route::get('/login', [AuthController::class,'generalLogin']);

Route::get('/logout', function (Request $request) {
    Session::flush();
    return redirect('/');
});


Route::prefix('acc')->namespace('\App\Modules\acc\Controllers')->group(function () {
    // 傳入修改 session 資訊。
    Route::get('/company/update/{company}', [ModuleAccAuthController::class, 'updateSetSessionCompanyId']);
    Route::view('/project-payment', 'projectPayment')->middleware('check.auth:pay')->name('projectPayment');
    Route::view('/project-pay-search', 'projectPaySearch')->middleware('check.auth:report')->name('projectPaySearch');
    Route::view('/cash', 'pettyCash')->middleware('check.auth:report')->name('pettyCash');
    Route::view('/seal', 'seal')->middleware('check.auth:report')->name('seal');
    Route::view('/auth', 'auth')->middleware('check.auth:auth')->name('auth');

    Route::group(['as' => 'perf::front::', 'prefix' => 'perfbonus'], function () {
        Route::view('/inquiry', 'perfBonus')->name('inquiry');
        Route::view('/review', 'perfBonus')->name('review');
        Route::view('/history', 'perfBonus')->name('history');
        Route::view('/setting', 'perfBonus')->name('setting');
    });
});

// SSO 開始
Route::prefix('sso')->namespace('\App\Modules\SSO\Controllers')->group(function () {
    // 匯僑客製化 SSO 登入 (臨時)
    // Route::get('richhonour', 'RichhonourController@handle');
    Route::get('asap', 'SSOController@asap')->name('asap');
    Route::get('hr', 'SSOController@hr')->name('hr');
    Route::get('login', [AuthController::class,'SSOLogin']);
    Route::get('logout', function (Request $request) {
        Session::flush();
        return redirect('/');
    });
});
