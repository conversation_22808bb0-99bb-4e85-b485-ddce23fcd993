<?php

namespace App\Jobs;

use App\Modules\acc\models\PjPayDetail;
use App\Modules\acc\models\Venbud;
use DB;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PjPayDetailImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 240;
    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        \Log::info('detail start');
          // 目前 symno >'202201' 才有抓
        $this->pjPayDetail();
        $this->venbud();
        \Log::info('detail end');
    }

  
    public function pjPayDetail()
    {
        $query = DB::connection('sqlsrv')
            ->table('pjpay_detail')
            ->selectRaw('*')
            // ->get();
        ;
        PjPayDetail::truncate();


        // 使用 Generator 處理資料
        $batch = [];
        $batchSize = 400;

        foreach ($query->cursor() as $item) {
            $batch[] = [
                'symno' => $item->symno,
                'pbatchno' => trim($item->pbatchno),
                'pbatchna' => trim($item->pbatchna),
                'epjacc' => trim($item->epjacc),
                'epjno' => trim($item->epjno),
                'unkey' => intval($item->unkey),
                'payload' => json_encode([
                    'venno' => trim($item->venno),
                    'venna' => $item->venna,
                    'copname' => trim($item->copname),
                    'payrmk' => $item->payrmk,
                    'vencamt' => intval($item->vencamt),
                    'tvenpay' => intval($item->tvenpay),
                    'balance' => intval($item->balance),
                    'npay' => intval($item->npay),
                    'ppay' => intval($item->ppay),
                    'hpay' => intval($item->hpay),
                    'dpay' => intval($item->dpay),
                    'venpay' => intval($item->venpay),
                    'tax' => 0,
                    'pjctrno' => trim($item->pjctrno),
                    'po' => trim($item->po),
                    '_unkey' => intval($item->_unkey),
                ])
            ];

            //  $batch[] = $payload;

            // 當批次達到指定大小時寫入資料庫
            if (count($batch) >= $batchSize) {
                DB::connection('pgsql')->table('pj_pay_details')->insert($batch);
                $batch = [];
                gc_collect_cycles();
            }
        }

        // 處理最後剩餘的資料
        if (!empty($batch)) {
            DB::connection('pgsql')->table('pj_pay_details')->insert($batch);
        }

        $batch = [];
        unset($query);
        gc_collect_cycles();

    }

    public function venbud()
    {
        try {
            $chunkSize = 1000; // 每页数据大小
            $page = 1; // 起始页
            $connectionMSSQL = DB::connection('sqlsrv');
            $connectionPostgreSQL = DB::connection('pgsql');
            Venbud::truncate();
            while (true) {
                $offset = ($page - 1) * $chunkSize + 1;
                $end = $offset + $chunkSize - 1;
        
                // 使用 ROW_NUMBER() 实现分页
                $query = $connectionMSSQL->select(
                    "WITH FilteredData AS (
                        SELECT DISTINCT epjno
                        FROM pjpay_detail
                    ),
                    PaginatedData AS (
                        SELECT 
                            RTRIM(v.epjno) AS epjno,
                            RTRIM(v.budcno) AS budcno,
                            v.budcna,
                            CAST(v.quamt AS INT) AS quamt,
                            CAST(v.qudcp AS INT) AS qudcp,
                            CAST(v.budamt AS INT) AS budamt,
                            CAST(v.buddcp AS INT) AS buddcp,
                            RTRIM(v.venno) AS venno,
                            RTRIM(v.venna) AS venna,
                            CAST(v.vencamt AS INT) AS vencamt,
                            CAST(v.vendcp AS INT) AS vendcp,
                            CAST(v.tvencamt AS INT) AS tvencamt,
                            CAST(v.venpay AS INT) AS venpay,
                            v.rmk,
                            CAST(v.budkey AS INT) AS budkey,
                            v.pjbno,
                            v.pjbud,
                            CAST(v.vencamt2 AS INT) AS vencamt2,
                            CAST(v.vendcp2 AS INT) AS vendcp2,
                            CAST(v.tvencamt2 AS INT) AS tvencamt2,
                            CAST(v.venpay2 AS INT) AS venpay2,
                            CAST(v.quamt2 AS INT) AS quamt2,
                            CAST(v.qudcp2 AS INT) AS qudcp2,
                            CAST(v.budamt2 AS INT) AS budamt2,
                            CAST(v.buddcp2 AS INT) AS buddcp2,
                            v.curno,
                            RTRIM(v.sdate) AS sdate,
                            RTRIM(v.odate) AS odate,
                            RTRIM(v.cdate) AS cdate,
                            CAST(v.budnew AS INT) AS budnew,
                            CAST(v.budnew2 AS INT) AS budnew2,
                            CAST(v.vendp1 AS INT) AS vendp1,
                            CAST(v.dpay AS INT) AS dpay,
                            ROW_NUMBER() OVER (ORDER BY v.epjno) AS RowNum
                        FROM v_epjvenbud v
                        WHERE v.epjno IN (SELECT epjno FROM FilteredData)
                    )
                    SELECT *
                    FROM PaginatedData
                    WHERE RowNum BETWEEN ? AND ?",
                    [$offset, $end]
                );
        
            
                $chunk = array_map(function ($item) {
                    unset($item->RowNum);
                    return (array) $item;
                }, $query);
        
                foreach (array_chunk($chunk, 200) as $batch) {
                    $connectionPostgreSQL->table('venbuds')->insert($batch);
                    unset($batch);
                }

                // 如果当前结果少于分页大小，说明数据读取完成
                if (count($chunk) < $chunkSize) {
                    break;
                }

                $page++;
                unset($query); // 释放查询结果
                unset($chunk); // 释放查询结果
            }
        } catch (\Throwable $th) {
            \Log::error("Error on page: {$page}");
            \Log::error($th);
        }
    
    }
}
