<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pj_reports', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->bigInteger('demand_id');
            $table->string('no')->comment('需求單單號');
            $table->string('pkey')->comment('會計代號_專案代號');
            $table->jsonb('columns');
            $table->jsonb('list');
            $table->jsonb('payload');
            $table->jsonb('metadata');
            $table->smallInteger('created_by');
            $table->smallInteger('updated_by');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pj_reports');
    }
};
