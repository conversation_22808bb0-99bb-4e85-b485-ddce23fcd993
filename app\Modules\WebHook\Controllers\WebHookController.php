<?php

namespace App\Modules\WebHook\Controllers;

use App\Modules\acc\models\PjReport;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Encryption\Encrypter;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class WebHookController extends Controller
{

    public function acceptResponseInfo(Request $request)
    {
        \Log::info('webhook start');

        $demand = $request->input('demand');

        $customList = $request->input('customList');
        // 系統跑流程的話會是多筆情況，正常簽核時就是單筆
        if (is_array($demand)) {
            foreach ($demand as $key => $value) {
                $this->createReport(
                    $value
                    ,
                    array_values(
                        array_filter($customList, function ($cl) use ($value) {
                            return $cl['demand_id'] == $value['id'];
                        })
                    )[0]
                );
            }
        } else {
            /** @var \Illuminate\Database\Eloquent\Model $customList */
            $this->createReport($demand, $customList);
        }

        \Log::info('webhook end');

    }

    private function createReport($demand, $customList)
    {
        $id = $demand['id'];
        $no = $demand['no'];
        $payload = $demand['payload'];
        $columns = $payload['forms'][0]['columns'];
        $signRoles = $payload['sign_roles'];
        $created_by = $demand['created_by'];
        $updated_by = $demand['updated_by'];

        // 簽核流程
        foreach ($signRoles as $key => &$value) {
            $value = [
                "id" => $value['id'],
                "remark" => $value['remark'] ?? null,
                "role_id" => $value['role_id'],
                "raw_time" => $value['raw_time'] ?? null,
                "self_name" => $value['self_name'],
                "timestamp" => $value['timestamp'] ?? null,
                "isRepresent" => $value['isRepresent'] ?? null,
                "apply_status" => $value['apply_status']
            ];
        }
        // \log::info('model',$payload);
        $newPayload = [
            "startYm" => $payload['startYm'],
            "endYm" => $payload['endYm'],
            "pbatchno" => $columns[0]['value'],
            "epjacc" => $columns[1]['value'],
            "epjno" => $columns[2]['value'],
            "epjna" => $columns[3]['value'],
            "status" => $payload['status'],
        ];
        $pkey = $newPayload['epjacc'] . '_' . $newPayload['epjno'];
        unset($payload);

        /**
         * 付款明細
         */
        $form = $customList['payload']['form_setting'];
        $lists = $customList['list'];

        try {
            DB::beginTransaction();
            foreach ($lists as $key => $list) {
                $tmplist = [];
                foreach ($form as $key => $item) {
                    $tmplist[$item['acc_name']] = $list[$key];
                }

                $hashId = md5(json_encode($tmplist) . json_encode($newPayload));
                $newPayload['hash_id'] = $hashId;
                $newPayload['sign_roles'] = $signRoles;

                // unkey 幾乎等於unique key，但說明與項目是相同值
                // po -- 判斷是項目還是說明
                PjReport::where('pkey', $pkey)
                    ->where('payload->startYm', $newPayload['startYm'])
                    ->where('payload->endYm', $newPayload['endYm'])
                    ->where('payload->pbatchno', $newPayload['pbatchno'])
                    ->where('list->unkey', $tmplist['unkey'])
                    ->where('list->_unkey', $tmplist['_unkey'])
                    ->where('list->po', $tmplist['po'])
                    ->delete();

                PjReport::create([
                    'demand_id' => $id,
                    'no' => $no,
                    'pkey' => $pkey,
                    'list' => $tmplist,
                    'columns' => $columns,
                    'payload' => $newPayload,
                    'metadata' => [],
                    'created_by' => $created_by,
                    'updated_by' => $updated_by,
                ]);

            }

            DB::commit();

        } catch (\Throwable $th) {
            DB::rollBack();
            \Log::error($th);
        }

    }
}
