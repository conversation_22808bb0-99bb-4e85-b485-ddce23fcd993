<?php

use App\Modules\PerfBonus\Models\PerfBonusSetting;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('perf_bonus_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->comment('公司id');
            $table->string('key')->index()->comment('設定項目');
            $table->text('value')->comment('設定值');
            $table->text('description')->default('')->comment('設定描述');
            $table->unsignedBigInteger('update_by')->nullable()->comment('最後更新人員');
            $table->timestamps();
        });

        PerfBonusSetting::query()->insert([
            // RHT
            ['key' => 'notification.status', 'value' => '1', 'description' => '是否開啟通知功能', 'company_id' => 1],
            ['key' => 'notification.text', 'value' => '哈囉:{name}，您有 :{x} 尚未進行績效獎金評定，請盡速去評定', 'description' => '通知內容', 'company_id' => 1],
            ['key' => 'notification.frequencyDay', 'value' => '1', 'description' => '通知頻率', 'company_id' => 1],
            // RHF
            ['key' => 'notification.status', 'value' => '0', 'description' => '是否開啟通知功能', 'company_id' => 2],
            ['key' => 'notification.text', 'value' => '哈囉:{name}，您有 :{x} 尚未進行績效獎金評定，請盡速去評定', 'description' => '通知內容', 'company_id' => 2],
            ['key' => 'notification.frequencyDay', 'value' => '1', 'description' => '通知頻率', 'company_id' => 2],
            // RHY
            ['key' => 'notification.status', 'value' => '1', 'description' => '是否開啟通知功能', 'company_id' => 3],
            ['key' => 'notification.text', 'value' => '哈囉:{name}，您有 :{x} 尚未進行績效獎金評定，請盡速去評定', 'description' => '通知內容', 'company_id' => 3],
            ['key' => 'notification.frequencyDay', 'value' => '1', 'description' => '通知頻率', 'company_id' => 3],
            // RHQ
            ['key' => 'notification.status', 'value' => '0', 'description' => '是否開啟通知功能', 'company_id' => 4],
            ['key' => 'notification.text', 'value' => '哈囉:{name}，您有 :{x} 尚未進行績效獎金評定，請盡速去評定', 'description' => '通知內容', 'company_id' => 4],
            ['key' => 'notification.frequencyDay', 'value' => '1', 'description' => '通知頻率', 'company_id' => 4],
            // FuncSync
            ['key' => 'notification.status', 'value' => '1', 'description' => '是否開啟通知功能', 'company_id' => 5],
            ['key' => 'notification.text', 'value' => '哈囉:{name}，您有 :{x} 尚未進行績效獎金評定，請盡速去評定', 'description' => '通知內容', 'company_id' => 5],
            ['key' => 'notification.frequencyDay', 'value' => '1', 'description' => '通知頻率', 'company_id' => 5],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('perf_bonus_settings');
    }
};
