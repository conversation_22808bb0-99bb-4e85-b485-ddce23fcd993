<?php

namespace App\Modules\SSO\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Controller;
use App\Modules\acc\models\Employee;
use App\Modules\acc\models\Company;
use App\Modules\acc\models\FuncAuth;
use App\Modules\SSO\Models\SsoToken;
use Illuminate\Encryption\Encrypter;
use Carbon\Carbon;
use Exception;

class AuthController extends Controller
{
    protected $company_id;
    protected $user_id;

    public function __construct()
    {
    }

    public function check(Request $request)
    {
        if (!$request->has('token'))
            return redirect('/');
        $newEncrypter = new Encrypter(md5(Config('sso_key')), 'AES-256-CBC');
        $token = $newEncrypter->Decrypt($request->get('token'));
        $s = SsoToken::where('token', $token)->where('created_at', '>', Carbon::now()->subMinutes(10))->first();

        return $s;
    }

    /**
     * 將必要的資訊寫入 session: employee_id, CompanyId, timezone
     *
     * @param SsoToken $ssoToken
     */
    public function setSession($ssoToken)
    {
        $e = Employee::find($ssoToken->employee_id);
        Session::put('employee_id', $e->id);
        Session::put('CompanyId', $e->company_id);

        $c = Company::findOrFail($e->company_id);
        $t = $c->payload->get('timezone', 'Asia/Taipei');
        Session::put('timezone', $t);
    }

    /**
     * 設定側邊選單
     *
     * @return void|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function setSideMenu()
    {
        $funcAuth = FuncAuth::whereJsonContains('payload->user_list',(int)Session::get('employee_id'))
            ->orderby('payload->sort')
            ->get()
            ->pluck('type')
            ->toArray();

        // 沒有權限
        if (count($funcAuth) < 1 ) return redirect('/');

        $functionMenu = [
            [
                "type"=> "pay",
                "name" => "專案付款",
                "url" => "/acc/project-payment",
                "route" => "projectPayment",
                "icon" => "credit-card",
                "active" => false,
            ],
            [
                "type"=> "report",
                "name" => "報表查詢",
                "url" => "#",
                "icon" => "file-lines",
                "active" => false,
                "drop" => false,
                "drop_lists" => [
                    [
                        "name" => "專案付款",
                        "url" => "/acc/project-pay-search",
                        "route" => "projectPaySearch",
                        "active" => false,
                    ],
                    [
                        "name" => "零用金申請",
                        "url" => "/acc/cash",
                        "route" => "pettyCash",
                        "active" => false,
                    ],
                    [
                        "name" => "用印申請",
                        "url" => "/acc/seal",
                        "route" => "seal",
                        "active" => false,
                    ],
                ],
            ],
            [
                "type"=> "file",
                "name" => "文件管理",
                "url" => "#",
                "icon" => "folder-open",
                "active" => false,
            ],
            perf_bonus_route_generate((int)Session::get('employee_id')), // 績效獎金
            [
                "type"=> "auth",
                "name" => "權限管理",
                "url" => "/acc/auth",
                "route" => "auth",
                "icon" => "user-lock",
                "active" => false,
            ],
        ];
        $functionMenu = collect($functionMenu)->filter(function($item) use ($funcAuth) {
            if (!isset($item['type'])) {
                return false;
            }
            return in_array($item['type'], $funcAuth);
        });

        Session::put('menus', $functionMenu);
        Session::put('auth', $funcAuth);
    }

    /**
     * 測試登入，作用於測試環境；正式環境不會使用
     *
     * @param Request $request
     */
    public function generalLogin(Request $request)
    {
        $id = $request->get('id');

        if (empty($id)) {
            return redirect('/');
        }

        $e = Employee::findOrFail($id);

        Session::put('employee_id', $id);
        Session::put('CompanyId', $e->company_id);
        Session::put('timezone', 'Asia/Taipei');

        return redirect(
            to: $this->noSpecialUriGetFirstPath(),
            status: 302,
        );
    }

    /**
     * 透過原始 SSO 登入規則登入該模組
     *
     * @param Request $request
     */
    public function SSOLogin(Request $request)
    {
        $s = $this->check($request);
        if (empty($s)) {
            return redirect('/');
        }
        $this->setSession($s);

        return redirect(
            to: $this->noSpecialUriGetFirstPath(),
            status: 302,
        );
    }

    /**
     * 權限管理中，沒開通的話就跳轉至根網址
     * 那如果有開通的選項的話，則取得第一個有權限的 url 當作預設登入的 url
     *
     * @return string 擁有權限的第一個 url
     */
    public function noSpecialUriGetFirstPath()
    {
        // session 不存在 menu 選單的話，則重新撈 menu
        if (!Session::has('employee_id')) {
            throw new Exception('請確認是否登入!');
        }
        if (!Session::has('menus')) {
            $this->setSideMenu();
        }

        $firstAllowMenu = Session::get('menus')?->first();

        if (is_null($firstAllowMenu)) {
            return '/';
        }

        switch (true) {
            // 有下拉選單的話則選定第一個下拉選單的項目
            case $firstAllowMenu['url'] === '#' && isset($firstAllowMenu['drop_lists']):
                $targetUrl = $firstAllowMenu['drop_lists'][0]['url'] ?? null;
                $targetRoute = $firstAllowMenu['drop_lists'][0]['route'] ?? null;
                break;
            // 大項目的 url 不為 # 時，則代表他沒有子項目；直接跳轉即可。
            case $firstAllowMenu['url'] !== '#':
                $targetUrl = $firstAllowMenu['url'] ?? null;
                $targetRoute = $firstAllowMenu['route'] ?? null;
                break;
            default:
                return '/';
        }

        // 有 url 則回傳 url
        if (!empty($targetUrl)) {
            return $targetUrl;
        }

        // 有 route 則回傳 route 對應的 url
        if (!empty($targetRoute)) {
            return route($targetRoute);
        }
        return '/';
    }
}
