<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('venbuds', function (Blueprint $table) {
            $table->id();
            $table->string('epjno');
            $table->string('budcno');
            $table->string('budcna');
            $table->bigInteger('quamt');
            $table->bigInteger('qudcp');
            $table->bigInteger('budamt');
            $table->bigInteger('buddcp');
            $table->string('venno');
            $table->string('venna');
            $table->bigInteger('vencamt');
            $table->bigInteger('vendcp');
            $table->bigInteger('tvencamt');
            $table->bigInteger('venpay');
            $table->string('rmk');
            $table->bigInteger('budkey');
            $table->string('pjbno');
            $table->string('pjbud');
            $table->bigInteger('vencamt2');
            $table->bigInteger('vendcp2');
            $table->bigInteger('tvencamt2');
            $table->bigInteger('venpay2');
            $table->bigInteger('quamt2');
            $table->bigInteger('qudcp2');
            $table->bigInteger('budamt2');
            $table->bigInteger('buddcp2');
            $table->string('curno');
            $table->string('sdate');
            $table->string('odate');
            $table->string('cdate');
            $table->bigInteger('budnew');
            $table->bigInteger('budnew2');
            $table->bigInteger('vendp1');
            $table->bigInteger('dpay');
            // $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('venbuds');
    }
};

