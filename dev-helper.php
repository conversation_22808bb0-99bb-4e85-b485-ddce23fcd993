<?php


if (!function_exists('memory_use_helper')) {

    /**
     * Get memory usage
     * @param bool $realUsage
     * @return string
     */
    function memory_use_helper($realUsage = true): string
    {
        $mem = memory_get_usage($realUsage);
        $unit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        return @round($mem / pow(1024, ($i = floor(log($mem, 1024)))), 2) . ' ' . $unit[$i];
    }
}


if (!function_exists('memory_use_helper_callback')) {
    /**
     * Get memory usage with callback
     * @param callable $callback
     * @return string
     */
    function memory_use_helper_callback(callable $callback): string
    {
        $start = memory_get_usage();
        $callback();
        $end = memory_get_usage();
        $mem = $end - $start;
        $unit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        return @round($mem / pow(1024, ($i = floor(log($mem, 1024)))), 2) . ' ' . $unit[$i];
    }
}
