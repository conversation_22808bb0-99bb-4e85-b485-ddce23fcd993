<?php

  

namespace App\Modules\acc\Enums;

 

enum SendStatusenum:string {

    case SEND_FAX = 'send-fax';

    case SEND_EMAIL = 'send-email';

    case FAX_SUCCESS = 'fax-sucess';
    case FAX_FAILED = 'fax-failed';

    
    public function getLabel(): string
    {
        return match ($this) {
            self::SEND_FAX => '已送出傳真',
            self::SEND_EMAIL => '已送出信件',
            self::FAX_SUCCESS => '傳真發送成功',
            self::FAX_FAILED => '傳真發送失敗',
            default =>'未發送',
        };
    }
}