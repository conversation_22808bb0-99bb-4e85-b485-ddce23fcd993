<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>@yield('title')</title>
        @vite('resources/css/app.css')
        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

        <!-- Styles -->
        <style>
            html, body {
                background-color: #FAFBFD;
                color: #4D4D4D;
                font-family: Noto Sans TC;
                font-weight: 500;
                height: 100%;
                margin: 0;
                word-wrap: break-word;
                word-break: break-all;
                font-size: 14px;
            }
            @media (min-width: 768px) {
                html, body {
                font-size: 16px;
            }
            }
            #sideMenu {
                    min-width: 300px;
                    display: flex;
                    flex-direction: column;
                    transform: translateX(0);
                    transition: 0.5s;
                }
            @media (min-width: 768px) {
                #sideMenu {
                    min-height: 100vh;
                    position: sticky;
                    top: 0;
                }
            }    
            #sideMenu-active:checked + #sideMenu {
                height: 76px;
            }
            
            @media (min-width: 768px) {
                #sideMenu-active:checked + #sideMenu {
                    transform: translateX(-100%);
                    min-width: 0px;
                    max-width: 0px;
                    height: unset;
                }
            }
            #content {
                width: 100%;
            }
            @media (min-width: 768px) {
                #content {
                width: calc(100% - 300px);
                }
                #sideMenu-active:checked ~ #content {
                    width: 100%;
                }
            }

            #sideMenu-active {
                position: absolute;
                opacity: 0;
                z-index: -1;
            }
            .p-timeline-horizontal .p-timeline-event:last-child {
                flex: 1 !important;
            }
            ::selection {
                background-color: #3571e8;
                color: white;
            }
            .scroll-header::-webkit-scrollbar {
                width: 0px;
                height: 0px;
            }
            .scroll-header { 
                scrollbar-width: none; /* for Firefox only */
            }
            .p-sidebar-bottom .p-sidebar {
                height: 100px !important;
            }
        </style>
    </head>
    <body>
        <div class="flex flex-col md:flex-row text-base">
            <input type="checkbox" id="sideMenu-active">
            <div id="sideMenu" style="background-color: #1677FF" class="md: w-auto"></div>
            <div id="content" class="bg-neutral-100"></div>
        </div>

        <script  type="text/javascript"> let functionMenu=@json(Session::get('menus'));</script>
        @vite('resources/js/sideMenu.js')
    @yield('js')
    </body>
</html>
