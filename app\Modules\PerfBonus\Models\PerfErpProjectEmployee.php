<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Models;

use App\Modules\acc\models\Employee;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PerfErpProjectEmployee 專案員工.
 *
 * @property int $id id
 * @property string $epjno 專案代號
 * @property string $empno 員工代號
 * @property string $empna 員工姓名
 * @property string $perf_dep_code 部門代號
 */
class PerfErpProjectEmployee extends Model
{
    use HasFactory;

    public $timestamps = false;

    protected $table = 'perf_erp_project_employee';

    protected $fillable = [
        'epjno',
        'empno',
        'empna',
        'sno',
        'perf_dep_code',
    ];

    public function employee()
    {
        return $this->belongsTo(Employee::class, 'payload->employee_number', 'empno');
    }
}
