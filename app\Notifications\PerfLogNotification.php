<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class PerfLogNotification extends Notification
{
    protected string $title;
    protected string $content;
    protected array $metaData;
    protected string $redirectTo;
    protected string $from;


    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        string $title = '',
        string $content = '',
        array $metaData = [],
        string $redirectTo = '#',
        string $from = 'system',
    ) {
        $this->title = $title;
        $this->content = $content;
        $this->metaData = $metaData;
        $this->redirectTo = $redirectTo;
        $this->from = $from;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    public function viaConnections(): array
    {
        return [
            'database' => 'sync',
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->title,
            'content' => $this->content,
            'metaData' => $this->metaData,
            'redirectTo' => $this->redirectTo,
            'from' => $this->from,
        ];
    }
}
