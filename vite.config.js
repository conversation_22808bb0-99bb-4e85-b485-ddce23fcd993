import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue'

export default defineConfig({
    plugins: [
        vue(),
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/common/fontawsome.js',
                'resources/js/sideMenu.js',
                'resources/js/projectPayment/projectPayment.js',
                'resources/js/projectPaySearch/projectPaySearch.js',
                'resources/js/perfBonus/perfBonus.js',
                'resources/js/pettyCash/pettyCash.js',
                'resources/js/seal/seal.js',
                'resources/js/auth/auth.js'
        ],
            refresh: true,
        }),
    ],
});
