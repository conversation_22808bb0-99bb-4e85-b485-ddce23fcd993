<template>
  <div class="h-full flex flex-col overflow-x-auto">
    <Banner :names="['專案付款單', '廠商付款憑單','廠商用印通知單']" />
    <div class="m-6" style="height: calc(100vh - 128px);">
      <projectPay v-if="currentTab == 0" />
      <vendorPay v-if="currentTab == 1" />
      <venSeal v-if="currentTab == 2" />
    </div>
  </div>
</template>
<script>
import Banner from "../common/Banner.vue";
import projectPay from "../projectPaySearch/projectPay.vue";
import vendorPay from "../projectPaySearch/vendorPay.vue";
import venSeal from "../projectPaySearch/venSeal.vue";

export default {
  components: {
    Banner,
    projectPay,
    vendorPay,
    venSeal,
  },
  data: function () {
    return {
      currentTab: 0,
      pbatchno: [],
      apiURL: "/api/acc/pjpay",
      newDate:new Date(),
    };
  },
  mounted() {
    this.get_batch();
  },
  methods: {
    //#region fetch start
    get_batch() {
      axios
        .get(this.apiURL + "/get-batch", {
          params: {},
        })
        .then((response) => {
          this.pbatchno = response.data;
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {});
    },
    FormatValue(value) {
      const formatter = new Intl.NumberFormat("en-US", {
        currency: "USD",
      });
      if (value == null) {
        return (value = "-");
      } else {
        return (value = formatter.format(value));
      }
    },
    computedDetailTotal(detail, keyName) {
      let keyIndex = Object.keys(detail[0]).findIndex((key) => key == keyName);
      let detailValues = detail.map((item) => Object.values(item)[keyIndex]);
      return this.FormatValue(
        detailValues.reduce((total, value) => {
          return total + value;
        })
      );
    },
  },
};
</script>
<style>
    div.p-datatable .p-column-header-content {
        display: inline-block;
    }
</style>
