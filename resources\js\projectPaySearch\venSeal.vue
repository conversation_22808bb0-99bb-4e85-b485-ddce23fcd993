<template>
    <div class="h-full flex flex-col">
        <div class="p-6 flex gap-3 flex-wrap w-full bg-white rounded-xl shadow">
            <label class="my-auto"> 起始年月: </label>
            <Calendar
                v-model="year_start"
                view="month"
                dateFormat="yy/mm"
                placeholder="請選擇年月"
            />

            <label class="my-auto"> 結束年月: </label>
            <Calendar
                v-model="year_end"
                view="month"
                dateFormat="yy/mm"
                placeholder="請選擇年月"
            />

            <label class="my-auto"> 批次: </label>
            <MultiSelect
                v-model="select_fetch"
                :filter="true"
                :options="$parent.pbatchno"
                optionValue="code"
                optionLabel="name"
                dataKey="code"
                placeholder="請選擇"
                :maxSelectedLabels="3"
                class="w-25 md:w-20rem"
            />
            <label class="my-auto"> 發送狀態: </label>
            <Dropdown
                type="text"
                v-model="submitType"
                optionValue="code"
                optionLabel="name"
                :options="[
                    {
                        name: '全部',
                        code: 0,
                    },
                    {
                        name: '未發送',
                        code: 1,
                    },
                    {
                        name: '已發送',
                        code: 2,
                    },
                ]"
            />
            <label class="my-auto"> 廠商代號(選填): </label>
            <InputText
                type="text"
                v-model="venno"
                placeholder="請輸入"
                @input="venno = $event.target.value.toUpperCase()"
            />
            <!-- <Checkbox v-model="pizza" inputId="ingredient1" name="pizza" value="強制撈取" /> -->

            <Button
                type="button"
                label="重置"
                :loading="queryLoading"
                @click="clear()"
                text
                raised
            />

            <Button
                type="button"
                label="查詢"
                icon="pi pi-search"
                :loading="queryLoading"
                :disabled="year_start == null || year_end == null"
                @click="fetchPjPay()"
            />
        </div>
        <div
            class="flex-1 p-6 mt-6 bg-white rounded-xl shadow whitespace-nowrap overflow-x-auto flex relative"
            :class="
                projects.length == 0 || projects.data?.length == 0
                    ? 'justify-center items-center'
                    : 'flex-col'
            "
            :style="
                projects.length == 0 || projects.data?.length == 0
                    ? 'height : calc(100vh - 276px)'
                    : ''
            "
        >
            <div
                v-if="queryLoading"
                class="absolute top-1/2 left-1/2 z-10 transform -translate-x-1/2 -translate-y-1/2"
            >
                <ProgressSpinner />
            </div>
            <div
                class="text-center"
                v-if="projects.length == 0 || projects.data?.length == 0"
            >
                <img
                    v-if="!queryLoading && projects.length == 0"
                    src="../../images/page_state/search.png"
                    alt=""
                />
                <img
                    v-else-if="!queryLoading"
                    src="../../images/page_state/search_empty.png"
                    alt=""
                />
                <p v-if="!queryLoading && projects.length == 0">
                    輸入您要查詢的資料
                </p>
                <p v-else-if="!queryLoading && projects.data?.length == 0">
                    無任何查詢結果，請再次查詢
                </p>
            </div>
            <div v-else class="h-0 flex-1">
                <DataTable
                    v-model:expandedRows="expandedRows"
                    :expandedRows.sync="expandedRows"
                    :value="projects.data"
                    dataKey="venno"
                    :metaKeySelection="false"
                    :scrollable="true"
                    scrollHeight="flex"
                    responsiveLayout="scroll"
                    rowGroupMode="subheader"
                    groupRowsBy="id"
                    sortMode="single"
                    stripedRows
                    tableClass="vendor-table"
                    style="height: calc(100% - 80px)"
                >
                    <template #header>
                        <div
                            class="flex flex-wrap items-center justify-between gap-2"
                        >
                            <p class="text-xl text-900 font-bold">專案付款單</p>
                            <div>
                                <label class="my-auto"> 公司抬頭:&ensp; </label>
                                <Dropdown
                                    class="w-40"
                                    optionLabel="name"
                                    optionValue="id"
                                    :options="[
                                        {
                                            id: 'G',
                                            name: '匯僑室內裝修設計股份有限公司',
                                        },
                                        { id: 'F', name: '鴻僑傢俱有限公司' },
                                        {
                                            id: 'C',
                                            name: '新喬國際設計有限公司',
                                        },
                                    ]"
                                    v-model="company_title"
                                />
                                &ensp;
                                <Button
                                    :disabled="
                                        projects.data.reduce(
                                            (acc, row) =>
                                                acc + (row.selected ? 1 : 0),
                                            0
                                        ) == 0
                                    "
                                    @click="exportLists($event)"
                                    :loading="exportLoading"
                                    label="匯出"
                                />
                                &ensp;
                                <ConfirmPopup></ConfirmPopup>
                                <Button
                                    :disabled="
                                        projects.data.reduce(
                                            (acc, row) =>
                                                acc + (row.selected ? 1 : 0),
                                            0
                                        ) == 0
                                    "
                                    @click="fax($event)"
                                    :loading="faxLoading"
                                    label="傳真"
                                />
                                &ensp;
                                <Button
                                    :disabled="
                                        projects.data.reduce(
                                            (acc, row) =>
                                                acc + (row.selected ? 1 : 0),
                                            0
                                        ) == 0
                                    "
                                    @click="email($event)"
                                    :loading="sendLoading"
                                    label="寄信"
                                />
                            </div>
                        </div>
                    </template>
                    <Column frozen headerStyle="width: 3rem">
                        <template #header>
                            <input
                                type="checkbox"
                                class="cursor-pointer"
                                v-model="projects.allSelected"
                                @change="selectAllRow"
                            />
                        </template>
                        <template #body="slotProps">
                            <input
                                type="checkbox"
                                class="cursor-pointer"
                                v-model="slotProps.data.selected"
                                @change="selectRow($event, slotProps.data)"
                            />
                        </template>
                    </Column>
                    <Column
                        frozen
                        field="venno"
                        header="廠商代號"
                        headerStyle="width: 3rem"
                    ></Column>
                    <Column field="venna" header="廠商簡稱">
                        <template #body="slotProps">
                            {{ slotProps.data.list[0].venna }}
                        </template>
                    </Column>

                    <Column field="payym" header="付款年月">
                        <template #body="slotProps">
                            <span>{{
                                slotProps.data.list[0].startYm +
                                "-" +
                                slotProps.data.list[0].endYm
                            }}</span>
                        </template>
                    </Column>
                    <Column
                        field="total"
                        header="總計"
                        style="text-align: right"
                    >
                        <template #body="slotProps">
                            <span>{{
                                this.$parent.FormatValue(slotProps.data.total)
                            }}</span>
                        </template>
                    </Column>
                    <Column
                        field="pbatchno"
                        header="批次"
                        headerStyle="width: 1rem"
                    >
                        <template #body="slotProps">
                            <span>{{ slotProps.data.list[0].pbatchno }}</span>
                        </template>
                    </Column>
                    <Column field="email" header="廠商信箱">
                        <template #body="slotProps">
                            <span>{{ slotProps.data.email }}</span>
                        </template>
                    </Column>
                    <Column field="fax" header="廠商傳真">
                        <template #body="slotProps">
                            <span>{{ slotProps.data.fax }}</span>
                        </template>
                    </Column>
                    <Column expander style="width: 5rem">
                        <template #header>
                            <Button
                                @click="toggleExpandAll"
                                class="!w-8 !h-8"
                                :class="{ 'rotate-90': expandedRows.length }"
                                icon="pi pi-check"
                                text
                                rounded
                                severity="secondary"
                            >
                                <svg
                                    width="14"
                                    height="14"
                                    viewBox="0 0 14 14"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="p-icon p-row-toggler-icon"
                                    aria-hidden="true"
                                >
                                    <path
                                        d="M4.38708 13C4.28408 13.0005 4.18203 12.9804 4.08691 12.9409C3.99178 12.9014 3.9055 12.8433 3.83313 12.7701C3.68634 12.6231 3.60388 12.4238 3.60388 12.2161C3.60388 12.0084 3.68634 11.8091 3.83313 11.6622L8.50507 6.99022L3.83313 2.31827C3.69467 2.16968 3.61928 1.97313 3.62287 1.77005C3.62645 1.56698 3.70872 1.37322 3.85234 1.22959C3.99596 1.08597 4.18972 1.00371 4.3928 1.00012C4.59588 0.996539 4.79242 1.07192 4.94102 1.21039L10.1669 6.43628C10.3137 6.58325 10.3962 6.78249 10.3962 6.99022C10.3962 7.19795 10.3137 7.39718 10.1669 7.54416L4.94102 12.7701C4.86865 12.8433 4.78237 12.9014 4.68724 12.9409C4.59212 12.9804 4.49007 13.0005 4.38708 13Z"
                                        fill="currentColor"
                                    ></path>
                                </svg>
                            </Button>
                        </template>
                    </Column>
                    <template #expansion="slotProps">
                        <innerTable
                            :projects="projects"
                            :rowData="slotProps"
                            :showColumns="['epjacc', 'epjna', 'payrmk', 'vencamt', 'submitType']"
                        >
                        </innerTable>
                    </template>
                </DataTable>
                <Paginator
                    :data="projects"
                    @page="(val) => fetchPjPay(val.page, val.per)"
                />
            </div>
        </div>
    </div>
    <Sidebar
        v-model:visible="visibleBottom"
        position="bottom"
        :baseZIndex="1000"
        :modal="false"
        :showCloseIcon="false"
        class="p-sidebar-xs"
        :dismissable="false"
    >
        <div class="flex justify-end w-full">
            <p class="text-sm my-auto">
                已選取{{
                    this.projects.data.reduce(
                        (acc, row) => acc + (row.selected ? 1 : 0),
                        0
                    )
                }}個
            </p>
            <p
                @click="
                    projects.data.forEach((row) => {
                        row.selected = false;
                        row.list.forEach((detail) => (detail.selected = false));
                    })
                "
                class="mx-12 text-sm text-blue-600 underline my-auto cursor-pointer"
            >
                取消選取
            </p>
        </div>
    </Sidebar>
    <Toast position="top-center" />
</template>
<script>
import Paginator from "../common/Paginator.vue";
import innerTable from "./innerTable.vue";
// import { debounce } from 'lodash';
import debounce from "lodash/debounce";

export default {
    components: {
        Paginator,
        innerTable,
    },
    watch: {
        // row selected 跟 allSelected 連動
        "projects.data": {
            handler: function () {
                if (this.projects.data.some((row) => !row.selected)) {
                    this.projects.allSelected = false;
                } else {
                    this.projects.allSelected = true;
                }
                this.checkCount();
            },
            deep: true,
        },
    },
    data: function () {
        return {
            queryLoading: false,
            exportLoading: false,
            faxLoading: false,
            sendLoading: false,
            // year_start: null,
            // year_end: null,
            visibleBottom: false,
            year_start: this.$parent.newDate,
            year_end: this.$parent.newDate,
            select_fetch: ["1"],
            submitType: 0,
            company_title: "G",
            venno: "",
            force_fetch: false,
            page: 1,
            per: 50,
            projects: [],
            expandedRows: [],
            apiURL: "/api/acc/report/venpay",
            sendType: null,
            payList: [],
            expandedRows: [],
        };
    },
    mounted() {
        this.fetchPjPay(1);
    },
    methods: {
        selectRow(event, data) {
            if (event.target.checked) {
                // list 選取
                data.list.forEach((detail) => (detail.selected = true));
            } else {
                data.list.forEach((detail) => (detail.selected = false));
            }
        },
        selectAllRow(event) {
            if (event.target.checked) {
                this.projects.data.forEach((row) => {
                    // row 選取
                    row.selected = true;
                    // list 選取
                    row.list.forEach((detail) => (detail.selected = true));
                });
            } else {
                this.projects.data.forEach((row) => {
                    row.selected = false;
                    row.list.forEach((detail) => (detail.selected = false));
                });
            }
        },
        fetchPjPay(page, per) {
            this.queryLoading = true;
            if (page) this.page = page;
            if (per) this.per = per;
            axios
                .get(this.apiURL + "/list", {
                    params: {
                        startYm: this.year_start,
                        endYm: this.year_end,
                        pbatch: this.select_fetch,
                        submitType: this.submitType,
                        venno: this.venno,
                        epjacc: this.epjacc,
                        page: this.page,
                        per: this.per,
                    },
                })
                .then((response) => {
                    this.projects = response.data ?? [];
                })
                .catch((error) => {
                    console.error(error);
                })
                .finally(() => {
                    this.queryLoading = false;
                });
        },
        clear() {
            this.year_start = this.$parent.newDate;
            this.year_end = this.$parent.newDate;
            this.select_fetch = ["1"];
            this.venno = "";
        },
        exportLists() {
            this.exportLoading = true;
            let selectedDetails = this.projects.data
                .filter((row) => row.selected)
                .map((row) => row.list)
                .flat()
                .filter((detail) => detail.selected);
            axios({
                url: this.apiURL + "/export",
                method: "POST",
                data: {
                    payList: selectedDetails,
                    companyTitle: this.company_title,
                },
                responseType: "blob",
            })
                .then((response) => {
                    if (
                        response.headers["content-type"].includes(
                            "application/zip"
                        )
                    ) {
                        this.download(response);
                    } else if (
                        response.headers["content-type"].includes(
                            "application/octet-stream"
                        )
                    ) {
                        var blob = new Blob([response.data], {
                            type: "application/vnd.ms-excel;charset=utf-8",
                        });

                        var filename = decodeURIComponent(
                            response.headers["content-disposition"].split(
                                "filename*=utf-8''"
                            )[1]
                        );
                        var url = window.URL.createObjectURL(blob);
                        var aLink = document.createElement("a");
                        aLink.style.display = "none";
                        aLink.href = url;
                        aLink.setAttribute("download", filename);
                        document.body.appendChild(aLink);
                        aLink.click();
                        setTimeout(function () {
                            window.URL.revokeObjectURL(data);
                        }, 100);
                    } else {
                        window.print();
                    }
                })
                .catch((error) => {
                    console.log(error);
                })
                .finally(() => {
                    this.exportLoading = false;
                });
        },
        fax(event) {
            this.send(event, "fax");
        },
        email(event) {
            this.send(event, "email");
        },
        send(event, type) {
            //製作後端sendInfo需求格式
            const col_value = ["venno", "email", "fax"];
            let selectedProjects = this.projects.data.filter(
                (row) => row.selected
            );
            let selectedDetails = selectedProjects
                .map((row) => row.list)
                .flat()
                .filter((detail) => detail.selected);
            let sendInfo = JSON.parse(JSON.stringify(selectedProjects));
            sendInfo.forEach((info) => {
                Object.keys(info).forEach((key) => {
                    if (!col_value.includes(key)) {
                        delete info[key];
                    }
                });
            });

            const isSent = selectedDetails.some(
                (detail) => detail.submitTime !== null
            );
            const message = `${
                isSent ? "選取項目已傳真/寄信，是否重複" : "是否確定"
            }${type === "fax" ? "傳真" : "寄信"}？`;

            this.$confirm.require({
                target: event.currentTarget,
                message,
                acceptLabel: "確定",
                rejectLabel: "取消",
                accept: () => {
                    let param = {
                        sendType: type,
                        sendInfo: sendInfo,
                        payList: selectedDetails,
                        companyTitle: this.company_title,
                    };
                    if (type === "fax") {
                        this.faxLoading = true;
                    } else {
                        this.sendLoading = true;
                    }
                    axios
                        .post(this.apiURL + "/send", param)
                        .then((response) => {
                            if (response.data) {
                                this.$toast.add({
                                    severity: "success",
                                    summary: "成功",
                                    detail: this.messages,
                                    life: 3000,
                                });
                            } else {
                                this.$toast.add({
                                    severity: "error",
                                    summary: "失敗",
                                    detail: this.messages,
                                    life: 3000,
                                });
                            }
                        })
                        .catch((error) => {
                            console.error(error);
                            this.$toast.add({
                                severity: "error",
                                summary: "失敗",
                                detail: this.messages,
                                life: 3000,
                            });
                        })
                        .finally(() => {
                            this.fetchPjPay();
                            if (type === "fax") {
                                this.faxLoading = false;
                            } else {
                                this.sendLoading = false;
                            }
                        });
                },
            });
        },
        download(response) {
            var blob = new Blob([response.data], {
                type: "application/zip",
            });
            if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveOrOpenBlob(blob);
                return;
            }
            var filename = decodeURIComponent(
                response.headers["content-disposition"].split("filename=")[1]
            );
            const data = window.URL.createObjectURL(blob);
            var link = document.createElement("a");
            link.href = data;
            link.download = filename; // response.name + '.pdf'
            link.click();
            // link.remove();
            setTimeout(function () {
                window.URL.revokeObjectURL(data);
            }, 100);
        },
        checkCount: debounce(function () {
            this.visibleBottom = this.projects.data.reduce(
                (acc, row) => acc + (row.selected ? 1 : 0),
                0
            )
                ? true
                : false;
        }, 300),
        changeState(type) {
            switch (type) {
                case "已寄信":
                case "已傳真":
                    return "text-green-400 border-green-300";
                default:
                    return "text-gray-400 border-gray-400";
            }
        },
        toggleExpandAll() {
            if (this.expandedRows.length == 0) {
                this.expandedRows = this.projects.data.filter((p) => p.venno);
            } else {
                this.expandedRows = [];
            }
        },
    },
};
</script>
<style>
.expansionTable thead {
    z-index: 0 !important;
}

.vendor-table .p-datatable-thead > tr > th,
.vendor-table .p-datatable-tbody > tr > td {
    padding: 8px !important;
}
</style>
