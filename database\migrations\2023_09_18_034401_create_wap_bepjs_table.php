<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('temp_wap_bepjs', function (Blueprint $table) {
            $table->string('epjno')->comment('專案代號')->primary(); // 不完全是數字....
            $table->string('epjna')->comment('專案簡稱');
            $table->string('accasno')->nullable()->comment('會計單號');
            $table->string('epjname')->nullable()->comment('專案名稱');
            $table->string('sdate')->nullable()->comment('開始時間');
            $table->string('odate')->nullable()->comment('完工日');
            $table->string('cusno')->nullable()->comment('客戶代號');
            $table->string('cusname')->nullable()->comment('客戶公司名稱');
            $table->string('cusman')->nullable()->comment('客戶對接人姓名');
            $table->string('cmanwtna')->nullable()->comment('客戶職位');
            $table->string('cmantel')->nullable()->comment('客戶電話');
            $table->string('cmanmail')->nullable()->comment('客戶信箱');
            $table->string('wkcla')->nullable()->comment('案子類型');
            $table->string('wkclana')->nullable()->comment('案子類型相應名稱');
            $table->string('schdepno')->nullable()->comment('對應的部門');
            $table->string('pjmno')->nullable()->comment('專案經理的員工編號');
            $table->string('pjmna')->nullable()->comment('專案經理的名稱');
            $table->string('atype')->nullable()->comment('專案狀態');
            $table->string('atypena')->nullable()->comment('專案狀態名稱');
            $table->string('totpay')->nullable()->comment('總共支付');
            $table->string('epjbrno')->nullable()->comment('品牌名稱');
            $table->string('closeym')->nullable()->comment('結案的績效年月');
            $table->timestamps();
        });

        
        Schema::create('temp_wap_bemps', function (Blueprint $table) {
            $table->id();
            $table->string('empno')->index()->comment('員工編號');
            $table->string('empna')->nullable()->comment('員工姓名');
            $table->string('wlno')->nullable()->comment('職稱');
            $table->string('cemail')->nullable()->comment('email');
            $table->string('odate')->nullable()->comment('離職日');
            $table->string('depno')->nullable()->comment('部門職位');
            $table->string('depna')->nullable()->comment('部門職位名稱');
            $table->string('schdepno')->nullable()->comment('部門職位(似乎是控權限用的，目前無需使用)');
            $table->string('secempno')->nullable()->comment('不知道這幹嘛的 @TODO -> second empno ???');
            $table->string('secempna')->nullable()->comment('不知道這幹嘛的 @TODO -> second empname ???');
            $table->string('depempno')->nullable()->comment('不知道這幹嘛的');
            $table->string('depempna')->nullable()->comment('不知道這幹嘛的');
            $table->string('asman')->nullable()->comment('不知道這幹嘛的');
            $table->string('idno')->nullable()->comment('身分證字號');
            $table->string('_unkey')->nullable()->comment('不知道這幹嘛的');
        });

        Schema::create('temp_wap_epjemps', function (Blueprint $table) {
            $table->id();
            $table->string('epjno')->index()->comment('專案代號');
            $table->string('epjna')->nullable()->comment('專案簡稱');
            $table->string('empno')->nullable()->comment('員工編號');
            $table->string('empna')->nullable()->comment('員工姓名');
            $table->string('sno')->nullable()->comment('員工編號');
            $table->string('schdepno')->nullable()->comment('部門職位(似乎是控權限用的，目前無需使用)');
            $table->string('schgrpno')->nullable()->comment('部門職位(似乎是控權限用的，目前無需使用)');
            $table->string('pjmno')->nullable()->comment('專案經理的員工編號');
            $table->string('pjmna')->nullable()->comment('專案經理的名稱');
            $table->string('depno')->nullable()->comment('部門職位');
            $table->string('depna')->nullable()->comment('部門職位名稱');
        });

        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('temp_wap_bepjs');
        Schema::dropIfExists('temp_wap_bemps');
        Schema::dropIfExists('temp_wap_epjemps');
    }
};
