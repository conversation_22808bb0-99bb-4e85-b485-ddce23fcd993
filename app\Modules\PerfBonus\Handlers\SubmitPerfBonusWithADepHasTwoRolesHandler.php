<?php

declare(strict_types=1);

namespace App\Modules\PerfBonus\Handlers;

use App\Modules\PerfBonus\Models\PerfDepReview;
use App\Modules\PerfBonus\Models\PerfErpProject;
use App\Modules\PerfBonus\Services\EmployeeService;
use App\Modules\PerfBonus\Services\PerfBonusService;
use App\Traits\CalcNumberTrait;
use App\Traits\PerfLogTrait;
use Exception;
use Illuminate\Support\Arr;

/**
 * 送審績效獎金的時候，部門派出的人員中有 工程人員 及 設計人員
 * Class SubmitPerfBonusWithADepHasTwoRolesHandler.
 * @package App\Modules\acc\Handlers\PerfBonus
 */
class SubmitPerfBonusWithADepHasTwoRolesHandler
{
    use CalcNumberTrait, PerfLogTrait;

    public static function handle(
        string $code, // 部門代碼 -> C1, DA, ...
        int|float $amount, // 實際額度 ...
        int|float $suggestionAmount, // 建議額度 ...
        array $reviewers, // 審核人員
        int $projectModelId, // 專案id
        int $sponsorEmployeeId, // 發起人員id
        array $projectEmployees = [], // 專案參與人員
        string $closeym = '',
    ): void {
        static::perfLog('info', '建立發放績效獎金的審核資料(該專案有兩種角色存在: 工程|設計)', compact('code', 'amount', 'suggestionAmount', 'reviewers', 'projectModelId', 'sponsorEmployeeId', 'projectEmployees'));
        /** @var PerfBonusService $perfBonusService */
        $perfBonusService = app(PerfBonusService::class);

        // 取得部門主管對照表
        $depManagerCollection = $perfBonusService->getDepManagerCache();
        $depMaster = $depManagerCollection->first(fn ($it) => $it['code'] === $code);

        if (empty($depMaster)) {
            static::perfLog('error', '錯誤：找不到部門主管對照表');
            return;
        }

        // 算出專案的金額占比:
        $total = count($projectEmployees);
        $projectEngineers = array_values(array_filter($projectEmployees, fn ($emp) => $emp['common_type'] === PerfDepReview::COMMON_TYPE_CPM));
        $engineersNum = count($projectEngineers);
        $projectStaffs = array_values(array_filter($projectEmployees, fn ($emp) => $emp['common_type'] === PerfDepReview::COMMON_TYPE_PM));
        $projectsNum = count($projectStaffs);

        $calcPrices = [
            PerfDepReview::COMMON_TYPE_CPM => [
                'amount' => static::roundWithPrecision($amount * ($engineersNum / $total), 1),
                'suggestion_amount' => static::roundWithPrecision($suggestionAmount * ($engineersNum / $total), 1),
            ],
            PerfDepReview::COMMON_TYPE_PM => [
                'amount' => static::roundWithPrecision($amount * ($projectsNum / $total), 1),
                'suggestion_amount' => static::roundWithPrecision($suggestionAmount * ($projectsNum / $total), 1),
            ],
        ];

        // 部門主管員工編號及姓名
        ['empno' => $depMasterEmpno, 'empna' => $depMasterEmpna] = $depMaster;

        // 績效獎金的最後一關是部門主管
        // 總共初核人員有三種狀況：
        // 1) 無初核人員，故直接送到部門主管 -> case 0
        // 2) 一位初核人員，因此同部門裡面的工程人員跟專案人員都是同一位去做審核 -> case 1
        // 3) 初核人員有兩位，因此要先檢查初核人員的腳色是否一個為工程人員，一個為專案人員 -> case 2

        // 先產生 Record 紀錄，再去透過 Record 取產生對應的 log 資訊。
        // Record 紀錄是關於(部門+人員的腳色)去判斷
        // 故: 同部門若是有工程及專案，則會有兩筆記錄。

        $tmpReviews = [];
        switch (count($reviewers)) {
            case 0:
                foreach ($calcPrices as $commonType => ['amount' => $calcAmount, 'suggestion_amount' => $calcSuggestionAmount]) {
                    $tmpReviews[$commonType] = PerfDepReview::create([
                        'code' => $code,
                        'first_empno' => null, // 第一階段評核人員
                        'second_empno' => $depMasterEmpno, // 第二階段評核人員(通常為部門主管)
                        'prj_id' => $projectModelId, // 專案id
                        'amount' => $calcAmount, // 實際金額
                        'suggestion_amount' => $calcSuggestionAmount, // 推薦金額
                        'status' => $calcSuggestionAmount == 0 ? PerfDepReview::STATUS_SKIP : PerfDepReview::STATUS_PENDING, // pending, finished, skip
                        'common_type' => $commonType,
                        'closeym' => $closeym,
                    ]);
                }
                break;
            case 1:
                foreach ($calcPrices as $commonType => ['amount' => $calcAmount, 'suggestion_amount' => $calcSuggestionAmount]) {
                    $tmpReviews[$commonType] = PerfDepReview::create([
                        'code' => $code,
                        'first_empno' => $reviewers[0], // 第一階段評核人員
                        'second_empno' => $depMasterEmpno, // 第二階段評核人員(通常為部門主管)
                        'prj_id' => $projectModelId, // 專案id
                        'amount' => $calcAmount, // 實際金額
                        'suggestion_amount' => $calcSuggestionAmount, // 推薦金額
                        'status' => $calcSuggestionAmount == 0 ? PerfDepReview::STATUS_SKIP : PerfDepReview::STATUS_PENDING, // pending, finished, skip
                        'common_type' => $commonType,
                        'closeym' => $closeym,
                    ]);
                }
                break;
            case 2:
                /** @var EmployeeService $employeeService */
                $employeeService = app(EmployeeService::class);

                $tmpReviews = [];
                $reviewerInfo = array_map(fn ($reviewer) => $employeeService->getEmployeeByEmpno($reviewer), $reviewers);
                $reviewerType = array_unique(Arr::pluck($reviewerInfo, 'type'));

                if (count($reviewerType) === 1) {
                    throw new Exception('不可選定兩個人員皆為同一種角色');
                }

                foreach ($reviewerInfo as [
                    'type' => $reviewerType,
                    'number' => $reviewerNumber,
                ]) {
                    ['amount' => $calcAmount, 'suggestion_amount' => $calcSuggestionAmount] = $calcPrices[$reviewerType];

                    $tmpReviews[$reviewerType] = PerfDepReview::create([
                        'code' => $code,
                        'first_empno' => $reviewerNumber, // employee no
                        'second_empno' => $depMasterEmpno, // 第二階段評核人員(通常為部門主管)
                        'prj_id' => $projectModelId, // 專案id
                        'amount' => $calcAmount, // 實際金額
                        'suggestion_amount' => $calcSuggestionAmount, // 推薦金額
                        'status' => $calcSuggestionAmount == 0 ? PerfDepReview::STATUS_SKIP : PerfDepReview::STATUS_PENDING, // pending, finished, skip
                        'common_type' => $reviewerType,
                        'closeym' => $closeym,
                    ]);
                }
                break;
        }

        foreach ($tmpReviews as $type => $reviewRecord) {
            $commonData = [
                'review_id' => $reviewRecord->id,
                'is_new' => false,
                'append_by' => null,
            ];

            $insertDataSet = match ($type) {
                PerfDepReview::COMMON_TYPE_CPM => array_map(function ($employee) use (&$reviewRecord, &$commonData) {
                    return array_merge($commonData, [
                        'sno' => $employee['sno'],
                        'empno' => $employee['empno'],
                        'empna' => $employee['empna'],
                        'member_type' => $employee['member_type'],
                        'show_title' => $employee['show_title'],
                        'question_extra' => $employee['question_extra'],
                    ]);
                }, $projectEngineers),
                PerfDepReview::COMMON_TYPE_PM => array_map(function ($employee) use (&$reviewRecord, &$commonData) {
                    return array_merge($commonData, [
                        'sno' => $employee['sno'],
                        'empno' => $employee['empno'],
                        'empna' => $employee['empna'],
                        'member_type' => $employee['member_type'],
                        'show_title' => $employee['show_title'],
                        'question_extra' => $employee['question_extra'],
                    ]);
                }, $projectStaffs),
            };
            foreach ($insertDataSet as $dataset) {
                $reviewRecord->logs()->firstOrCreate($dataset);
            }
        }

        PerfErpProject::find($projectModelId)->update([
            'sponsor_employee_id' => $sponsorEmployeeId,
            'status' => PerfErpProject::STATUS_IN_PROGRESS,
        ]); // 將專案狀態改為審核中
    }
}
