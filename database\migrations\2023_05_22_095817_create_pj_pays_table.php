<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pj_pays', function (Blueprint $table) {
            $table->id();
            // $table->string('key')->storedAs('epjacc_epjno');
            $table->string('pkey')->storedAs("epjacc ||'_'|| epjno")->index();
            $table->string('epjacc');
            $table->string('epjno');
            $table->jsonb('payload')->nullable();
            $table->timestamps();
            // $table->primary(['epjacc', 'epjno']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pj_pays');
    }
};
